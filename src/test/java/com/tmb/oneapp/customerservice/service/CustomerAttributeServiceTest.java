package com.tmb.oneapp.customerservice.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.CustomerAppLevelAttribute;
import com.tmb.oneapp.customerservice.model.PinFreeQrUpdateRequest;
import com.tmb.oneapp.customerservice.repository.CustomerAttributeRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CustomerAttributeServiceTest {

    @Mock
    private CustomerAttributeRepository customerAttributeRepository;

    private CustomerAttributeService customerAttributeService;

    private final String crmId = "001100000000000000000001184383";
    private final String correlationId = "test-correlation-id";

    @BeforeEach
    void setUp() {
        customerAttributeService = new CustomerAttributeService(customerAttributeRepository);
    }

    @Test
    void updatePinFreeQrSettings_Success() throws TMBCommonException {
        // Given
        PinFreeQrUpdateRequest request = new PinFreeQrUpdateRequest("Y", new BigDecimal("5000.00"));
        CustomerAppLevelAttribute customer = new CustomerAppLevelAttribute();
        
        when(customerAttributeRepository.findAllByCrmIdAndAppId(crmId, CustomerServiceConstant.APP_ID_01))
            .thenReturn(Optional.of(customer));
        when(customerAttributeRepository.updatePinFreeQrSettings(anyString(), any(BigDecimal.class), anyString(), anyString()))
            .thenReturn(1);

        // When & Then
        assertDoesNotThrow(() -> customerAttributeService.updatePinFreeQrSettings(crmId, request, correlationId));
        
        verify(customerAttributeRepository).updatePinFreeQrSettings("Y", new BigDecimal("5000.00"), crmId, CustomerServiceConstant.APP_ID_01);
    }

    @Test
    void updatePinFreeQrSettings_CustomerNotFound_ThrowsException() {
        // Given
        PinFreeQrUpdateRequest request = new PinFreeQrUpdateRequest("Y", new BigDecimal("5000.00"));
        
        when(customerAttributeRepository.findAllByCrmIdAndAppId(crmId, CustomerServiceConstant.APP_ID_01))
            .thenReturn(Optional.empty());

        // When & Then
        TMBCommonException exception = assertThrows(TMBCommonException.class, 
            () -> customerAttributeService.updatePinFreeQrSettings(crmId, request, correlationId));
        
        assertEquals("Customer not found", exception.getErrorMessage());
        verify(customerAttributeRepository, never()).updatePinFreeQrSettings(anyString(), any(BigDecimal.class), anyString(), anyString());
    }

    @Test
    void updatePinFreeQrSettings_UpdateFailed_ThrowsException() {
        // Given
        PinFreeQrUpdateRequest request = new PinFreeQrUpdateRequest("Y", new BigDecimal("5000.00"));
        CustomerAppLevelAttribute customer = new CustomerAppLevelAttribute();
        
        when(customerAttributeRepository.findAllByCrmIdAndAppId(crmId, CustomerServiceConstant.APP_ID_01))
            .thenReturn(Optional.of(customer));
        when(customerAttributeRepository.updatePinFreeQrSettings(anyString(), any(BigDecimal.class), anyString(), anyString()))
            .thenReturn(0);

        // When & Then
        TMBCommonException exception = assertThrows(TMBCommonException.class, 
            () -> customerAttributeService.updatePinFreeQrSettings(crmId, request, correlationId));
        
        assertEquals("Failed to update pin free QR settings", exception.getErrorMessage());
    }
}