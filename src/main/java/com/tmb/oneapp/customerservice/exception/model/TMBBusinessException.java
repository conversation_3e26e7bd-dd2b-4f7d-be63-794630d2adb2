package com.tmb.oneapp.customerservice.exception.model;

import com.tmb.oneapp.customerservice.constant.ResponseCode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.http.HttpStatus;

@Data
@EqualsAndHashCode(callSuper = true)
public class TMBBusinessException extends com.tmb.common.exception.model.TMBBusinessException {

    public TMBBusinessException(ResponseCode responseCode, HttpStatus status) {
        super.setCode(responseCode.getCode());
        super.setMessage(responseCode.getMessage());
        super.setService(responseCode.getService());
        super.setDescription(responseCode.getDesc());
        super.setStatus(status);
    }
}
