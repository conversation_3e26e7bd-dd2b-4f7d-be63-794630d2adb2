package com.tmb.oneapp.customerservice.constant;

import java.math.BigDecimal;

/**
 * Constant class for Application
 */
public class CustomerServiceConstant {

	/**
	 * created private constructor so that no body can create object of this call
	 */
	private CustomerServiceConstant() {
	}

	public static final String HEADER_CUSTOMER_CRM_ID = "CRM-ID";
	public static final String CUSTOMER_DATA_NOT_FOUND = "Customer data not found";
	public static final String ACTION_NOT_FOUND = "Action not found";
	public static final String HEADER_CORRELATION_ID = "X-Correlation-ID";

	public static final String DEFAULT_HEADER_CORRELATION_ID = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";

	public static final String HEADER_PROSPECT_ID = "X-Prospect-ID";

	public static final String HEADER_CITIZEN_ID = "X-Citizen-ID";
	public static final String HEADER_ACCOUNT_NUMBER = "X-Account-Number";
	public static final String HEADER_ACCOUNT_TYPE = "X-Account-Type";
	public static final String DEVICEID_DATA_NOT_FOUND = "deviceId not found";
	public static final String USERID_DATA_NOT_FOUND = "userId not found";
	public static final String HEADER_TIMESTAMP = "Timestamp";
	public static final String BLANK = "";
	public static final boolean FALSE = false;
	public static final String HYPHEN = "-";
	public static final String SPACE = " ";
	public static final String UNDERSCORE = "_";
	public static final String CUSTOMER_EXP_SERVICE = "customer exp service";
	public static final String CUSTOMER_SERVICE = "customer service";
	public static final String CUSTOMER_EXP_SERVICE_REST_API = "Customer Exp Service REST API";
	public static final String EB_CUSTOMER_STATUS_ID = "02";
	public static final String MB_USER_STATUS_ID = "02";
	public static final String CRMID_DATA_NOT_FOUND = "crmId not found";
	public static final String NOSUCH_ALGORITHM_EXCEPTION = "NoSuchAlgorithmException exception occurred ";
	public static final String IO_EXCEPTION = "IOException exception occurred ";
	public static final String KEYSTORE_EXCEPTION = "KeyStoreException exception occurred ";
	public static final String FILE_NOTFOUND_EXCEPTION = "FileNotFoundException exception occurred ";
	public static final String INITIALIZE_SSL_CONTEXT = "[initializeSSLContext] ";
	public static final String EXCEPTION_OCCURED = "Exception occured : {}";
	public static final String CUSTOMERS_SEARCH_CONNECTION_REFUSED = "customers search connection refused : {}";
	public static final String ERROR_CUSTOMERS_SEARCH_RESPONSE = "error response from core banking service : {}";
	public static final String ERROR_CUSTOMERS_STATUS_AND_DATA_RESPONSE = "error response from oracle db for customer status and data for click customer status detail : {}";
	public static final String CUSTOMER_STATUS_RESPONSE_SUSSESS = "success";
	public static final String CUSTOMER_STATUS_RESPONSE = "Received customer status response {} ";
	public static final String APP_ID = "A0478-MB";
	public static final String APP_ID_FOR_FOREIGNER = "A0478-MB-RM";
	public static final String CUSTOMER_GET = "customers-get";
	public static final String CUSTOMER_SEARCH = "customers-personal-search";
	public static final String COMMONS_LIST_KYC_CLASSIFIES = "commons-list-kyc-classifies";
	public static final String CL_TYPE_COUNTRY_04 = "04";
	public static final String CL_TYPE_PROVINCE_07 = "07";
	public static final String SERVICE_NAME = "service-name";
	public static final String CONTENT_TYPE = "content-type";
	public static final String REQUEST_UID = "request-uid";
	public static final String REQUEST_APP_ID = "request-app-id";
	public static final String REFERENCE_ID = "reference-id";
	public static final String REQUEST_APP_ID_VALUE = "A0478-MB";
	public static final String REQUEST_APP_ID_EC_RM = "A0478-MB-RM";
	public static final String REQUEST_APP_ID_VALUE_REALTIME = "A0274-MB";
	public static final String REQUEST_DATE_TIME = "request-datetime";
	public static final String SEARCH_TYPE = "search_type";
	public static final String RM_ID = "rm-id";
	public static final String RM_ID_FORMAT = "**********0000";
	public static final String RM_ID_PREFIX = "0011**********00";
	public static final String SEARCH_VALUE = "search_value";
	public static final String CRM_ID = "crm_id";
	public static final String QUERY = "query";
	public static final String CUSTOMER = "customer";
	public static final String PROFILE = "profile";
	public static final String ADDITIONAL_KYC = "additional_kyc";
	public static final String PHONES = "phones";
	public static final String ENG_TNAME = "eng_tname";
	public static final String ENG_FNAME = "eng_fname";
	public static final String ENG_LNAME = "eng_lname";
	public static final String FIRST_NAME_ENG = "first_name_eng";
	public static final String MIDDLE_NAME_ENG = "middle_name_eng";
	public static final String LAST_NAME_ENG = "last_name_eng";
	public static final String THA_TNAME = "tha_tname";
	public static final String THA_FNAME = "tha_fname";
	public static final String THA_LNAME = "tha_lname";
	public static final String FIRST_NAME_THAI = "first_name_thai";
	public static final String MIDDLE_NAME_THAI = "middle_name_thai";
	public static final String LAST_NAME_THAI = "last_name_thai";
	public static final String EMAIL_ADDRESS = "email_address";
	public static final String IDENTIFICATION_ID = "identification_id";
	public static final String CARD_EMBOSSING_NAME_1 = "card_embossing_name1";
	public static final String CARD_EMBOSSING_NAME_2 = "card_embossing_name2";
	public static final String CREDIT_CARD_NODE = "credit_card";
	public static final String CUSTOMER_NODE = "customer";
	public static final String CARD_INFO_NODE = "card_info";
	public static final String CARD_PHONES_NODE = "card_phones";
	public static final String PNONE_NO = "phone_no_full";
	public static final String PNONE_TYPE = "phone_type";
	public static final String PNONE_TYPE_M = "m";
	public static final String PHONE_TYPE = "phone_type";
	public static final String PHONE_TYPE_M = "M";
	public static final String PHONE_TYPE_R = "R";
	public static final String PHONE_TYPE_B = "B";
	public static final String PHONE_NO = "phone_no";
	public static final String PHONE_NO_FULL = "phone_no_full";
	public static final String CONTENT_TYPE_VALUE = "application/json";
	public static final String CONTENT_TYPE_VALUE_UTF_8 = "application/json;charset=UTF-8";
	public static final String MOBILE_NO = "mobile_no";
	public static final String KYC = "kyc";
	public static final String ONEAPP = "OneApp";
	public static final String ID_NO = "id_no";
	public static final String ID_EXPIRE_DATE = "id_expire_date";
	public static final String ID_CARD = "id_card";
	public static final String LOGIN_ACTIVITY_TYPE = "011";
	public static final String ACTIVITY_STATUS_SUCCESS = "success";
	public static final String ACTIVITY_STATUS_FAILURE = "failure";
	public static final String ACTIVITY_STATUS = "activity_status";
	public static final String ACTIVITY_TYPE_ID = "activity_type_id";
	public static final String ACTIVITY_STATUS_CODE = "status_code";
	public static final String RM_ID_VALUE = "rm_id";
	public static final String CRM_ID_VALUE = "crm_id";
	public static final String REF_ID = "ref_id";

	public static final String HEADER_PARAM_CRM_ID_DEFAULT = "0011**********0000000001184383";

	public static final String HEADER_PARAM_X_COR_ID_DEFAULT = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da";

	public static final String X_HEADER_CRMID = "X-CRMID"; // pass crm id from external gateway
	public static final String X_HEADER_NO_PERIOD = "X-NO-PERIOD";
	public static final String TIMESTAMP = "activity_date";
	public static final String CUSTOMER_PROFILE_IMAGE_NOT_FOUND = "Customer image not found";
	public static final String EMAIL_VERIFY_FLAG = "email_verify_flag";
	public static final String EMAIL_TYPE = "email_type";
	public static final String EMAIL_VERIFY_FLAG_VALUE = "N";
	public static final String ACTIVITY_FAIL_REASON = "fail_reason";
	public static final String ACTIVITY_STATUS_FAIL = "Failure";
	public static final String INVALID_PIN = "Invalid_Pin";
	public static final String ACCOUNT_LOCKED = "Account_Locked";
	public static final String LOGIN_ACTIVITY_TYPE_ID = "*********";
	public static final String OS_VERSION_FLEX = "ios/os_version";
	public static final String LOGOUT_ACTIVITY_TYPE_ID = "*********";
	public static final String STATUS_ID = "05";
	public static final String DEVICE_NICKNAME = "device_nickname";
	public static final String EB_CUSTOMER_STATUS_ID_VALUE = "eb_customer_status_id";
	public static final String EB_CUSTOMER_STATUS_DESC_VALUE = "eb_customer_status_desc";
	public static final String MB_CUSTOMER_STATUS_ID = "mb_user_status_id";
	public static final String IB_CUSTOMER_STATUS_ID = "ib_user_status_id";
	public static final String LANGUAGE_CD = "language_cd";
	public static final String DEVICE_STATUS = "device_status";
	public static final String INVALID_PIN_ONE_TIME_LEFT_STATUS_CODE = "1000001";
	public static final String INVALID_PIN_TWO_TIME_LEFT_STATUS_CODE = "1000002";
	public static final String ACCESS_PIN_LOCKED_STATUS_CODE = "1000006";
	public static final String STATUS_CODE = "statusCode";
	public static final String APP_VERSION_ONE = "app_version";
	public static final String ID_BIRTH_DATE = "id_birth_date";
	public static final String UBO_POST = "customers-related-persons-get";
	public static final String RELATED_PERSONS = "related_persons";
	public static final String RELATE_ACTIVE_FLAG = "relate_active_flag";
	public static final int UBO_DATA_NOT_FOUND_CODE = 4002;
	public static final String TOUCH_DEVICEID = "touch-deviceid";
	public static final String ONEAPP_DEVICEID = "oneapp-deviceid";
	public static final String AUTH_PUBLIC_KEY = "auth-public-key";
	public static final String HIBERNATE_DDL = "hibernate.hbm2ddl.auto";
	public static final String DATA_NOT_FOUND = "DATA NOT FOUND";
	public static final String TMB_ONE_APP_ID = "01";
	public static final String CUSTOMER_SERVICE_NAME = "customers-service";
	public static final String SUCCESS_CODE = "0000";
	public static final String SUCCESS_MESSAGE = "success";
	public static final String FAILED_CODE = "0001";
	public static final String NO_CRM_IDFAILED_CODE = "No crm found with this deviceId";
	public static final String FAILED_MESSAGE = "failed";
	public static final String DEVICE_ID = "deviceId";
	public static final String DEVICE_ID_SEARCH = "device_id";
	public static final String MOBILE_NO_SEARCH = "mobile_no";
	public static final String CITIZEN_ID_SEARCH = "citizen_id";
	public static final String NAME_PROSPECT_SEARCH = "name";
	public static final String PROSPECT_ID_SEARCH = "prospect_id";

	public static final String AUTHORIZATION = "Authorization";
	public static final String CUSTOMERS_SERVICE = "customers service";
	public static final String HEADER_ACCEPT_LANGUAGE = "Accept-Language";
	public static final String ACCEPT_LANGUAGE = "accept-language";
	public static final String EN_US = "en-US";
	public static final String HEADER_APP_VERSION = "app-version";
	public static final String FIREBASE_FILE_CONTEXTPATH = "media/customers/";
	public static final String FIREBASE_FILENAME = "profile.png";
	public static final String CORRELATION_ID = "x-correlation-id";
	public static final String IMAGE_TYPE = "image/png";
	public static final String FILE = "file";
	public static final String FILE_NAME = "fileName";
	public static final String PATH = "path";
	public static final String STATUS_ONE = "1";
	public static final String STATUS_ZERO = "0";
	public static final String DEVICE_MODEL = "device-model";
	public static final String DEVICE_OS = "device-os";
	public static final String DEVICE_OS_TYPE = "device-os-type";
	public static final String DEVICE_OS_VERSION = "device-os-version";
	public static final String ONEAPP_VERSION_USAGE = "oneapp-version-usage";
	public static final String ACCOUNT_NO = "account-no";
	public static final String CURRENT_ACCOUNT_APPL_CODE = "50";
	public static final String SAVINGS_FIXED_ACCOUNT_CODE = "60";
	public static final String LOAN_ACCOUNT_APPL_CODE = "10";
	public static final String APPL_CODE = "appl_code";
	public static final String ACCT_NBR = "acct_nbr";
	public static final String ACRONYM = "acronym";
	public static final String ACRONYM_VALUE = "7350T61D";
	public static final String ACCOUNTS_OWNERS_GET = "accounts-owners-get";
	public static final String REQUEST_UID_ACC_CUST_VALUE = "7d8277e0-b5cc-41b2-8dc8-9e06d763a713";
	public static final String NO_ACCOUNT_MATCH_FOR_APPL_CODE = "00";
	public static final String ACCOUNT = "account";
	public static final String OWNERS = "owners";
	public static final String CC_ID = "cc_id";
	public static final String OWNER_TYPE = "owner_type";
	public static final String RELATIONSHIP_CODE = "relationship_code";
	public static final String THA_FULLANME = "tha_fullname";

	public static final String VERIFY_PIN_ACTIVITY_TYPE_ID = "*********";
	public static final String VERIFY_PIN_ACTIVITY_TYPE_ID_FAIL_REASON = "code 1000006 : Access PIN Locked";
	public static final String DB_FAILED_CODE = "0006";
	public static final String EMAIL_NOT_VERIFY_CODE = "0015";
	public static final String UPDATE_STATUS_SUCCESS_MESSAGE = "Customer status updated successfully";
	public static final String UPDATE_STATUS_FAILED_MESSAGE = "Customer status updated failed";
	public static final String CUSTOMER_NAME = "customer-name";
	public static final String FNAME = "first_name";
	public static final String LNAME = "last_name";
	public static final String PAGE = "page";
	public static final String PAGE_VALUE = "1";
	public static final String EMAIL = "email";
	public static final String SEARCH_VALUE1 = "search_value1";
	public static final String CUSTOMERS_ADVANCE_SEARCH = "customers-advance-search";

	public static final String CUSTOMERS_SEARCH_EMAIL = "customers-search-email";
	public static final String CRM_ID_PROMPTPAY = "crmId";
	public static final int CARD_ID_LENGTH = 16;
	public static final int ACCOUNT_NO_MIN_LENGTH = 10;
	public static final int LOAN_ACCOUNT_LENGTH = 13;
	public static final String LOAN_ACCOUNT_PREFIX = "0";
	public static final String HEADER_USER_NAME = "user-name";
	public static final String HEADER_X_FORWARD_FOR = "X-Forward-For";
	public static final String ACTIVITY_ID_SEARCH = "9002001";
	public static final String ACTIVITY_MODULE_CUSTOMER_STATUS_UPDATE = "Update customer status on common bar";

	public static final String HEADERS_DEVICE_ID = "device-id";
	public static final String HEADERS_X_CRMID = "x-crmid";
	public static final String HEADERS_FRUAD_X_CRM_ID = "X-Crm-ID";
	public static final String HEADERS_CRMID = "crmid";
	public static final String HEADERS_CORRELATION_ID = "correlation-id";
	public static final String HEADERS_X_EKYC_FLAG = "x-ekyc-flag";
	public static final String HEADERS_CHANNEL = "channel";

	public static final String DB_CRM_ID = "crm_id";
	public static final String DB_DEVICE_ID = "device_uid";
	public static final String DB_SERVICE_TYPE_ID = "service_type_id";
	public static final String DB_TIMESTAMP = "timestamp";

	public static final String ACTIVITY_ID_CUSTOMER_STATUS_UPDATE = "9000003";
	public static final String ERROR_EMP_ACTIIVITY_LOG_SAVE = "error response from MangoDb to log employee activity for customer status update {}";
	public static final String ACTIVITY_TYPE_ID_VALUE_SUBMIT_CUSTOMER_STATUS = "301400100";
	public static final String ACTIVITY_TYPE_VALUE_SUBMIT_CUSTOMER_STATUS = "Submit Customer Status";
	public static final String ACTIVITY_TYPE_ID_VALUE_REMOVE_PIN_FREE = "301401600";
	public static final String ACTIVITY_TYPE_ID_COMMMON_AUTH_VALIDATE_RULE = "100002401";
	public static final String NO_VALUE = "No Value";
	public static final String ACTIVITY_TYPE_VALUE_REMOVE_PIN_FREE = "Click Submit button on EB Status or MB Status popup";
	public static final String ACTIVITY_TYPE_VALUE_SET_PIN_FREE_TRANSACTIONS_OFF = "Turn OFF";
	public static final String POP_VALUE_SUBMIT_EB_CUSTOMER_STATUS_FOR_CUSTOSMER = "Change EB Customer Status";
	public static final String POP_VALUE_SUBMIT_MB_CUSTOMER_STATUS_FOR_CUSTOSMER = "Change MB Customer Status";
	public static final String POP_VALUE_SUBMIT_IB_CUSTOMER_STATUS_FOR_CUSTOSMER = "Change IB Customer Status";
	public static final String ACTIVITY_TYPE_ID_CUSTOMER_TRANS_DAILY_REQUEST_REJECT = "301400700";
	public static final String ACTIVITY_TYPE_ID_CUSTOMER_TRANS_DAILY_REQUEST_APPROVE = "301400700";
	public static final String ACTIVITY_TYPE_ID_CUSTOMER_TRANS_DAILY_REDUCE_LIMIT = "100002200";
	public static final String ACTIVITY_TYPE_ID_CUSTOMER_TRANS_DAILY_REDUCE_LIMIT_REASON = "Reactivate in same or different device";
	public static final String ACTIVITY_TYPE_VALUE_CUSTOMER_TRANS_DAILY_REQUEST_REJECT = "Submit Customer Status";
	public static final String ACTIVITY_CUSTOMER_TRANS_DAILY_REQUEST_APPROVE = "Approve";
	public static final String ACTIVITY_CUSTOMER_TRANS_DAILY_REQUEST_REJECT = "Reject";
	public static final String CUSTOMER_STATUS_IB_UNLOCK_REASON = "Unlock OTP";
	public static final String CUSTOMER_STATUS_MB_REASON = "Reset MB Password";
	public static final String POP_VALUE_SUBMIT_MB_RESET_PIN_STATUS = "Reset MB PIN";
	public static final String POP_VALUE_SUBMIT_IB_UNLOCK_STATUS = "Unlock IB OTP";

	public static final String EMP_ACTIVITY_ID_TRANS_DAILY_REQUEST = "9002010";
	public static final String EMP_ACTIVITY_ID_TRANS_APPROVE_REQUEST = "9002009";
	public static final String EMP_ACTIVITY_TRANS_DAILY_REQUEST_POP_UP = "Transaction Daily Limit Request";

	public static final String CC_CHANNEL = "CC";
	public static final String APP_ID_01 = "01";
	public static final String CUSTOMER_STATUS_SEPARATOR = "::";
	public static final String CUSTOMER_STATUS_EB = "EB";
	public static final String CUSTOMER_STATUS_MB = "MB";
	public static final String CUSTOMER_STATUS_IB = "IB";
	public static final String CUSTOMER_STATUS_IB_CANCELLED_BY_CUSTOMER = "07";
	public static final String CUSTOMER_STATUS_IB_CANCELLED_BY_BANK = "08";
	public static final String PROVINCE_DATA_KYC_CLASSIFIES = "kyc_classifies";
	public static final String PROVINCE_DATA_CL_CODE = "cl_code";
	public static final String PROVINCE_DATA_CL_DESC1 = "cl_desc1";
	public static final String COUNTRY_DATA_CL_CODE = "cl_code";
	public static final String COUNTRY_DATA_CL_DESC2 = "cl_desc2";

	public static final String GET_CUSTOMER_GENDER = "gender";
	public static final String GET_CUSTOMER_MARITAL_STATUS = "marital_status";
	public static final String GET_CUSTOMER_EXT = "Ext.";
	public static final String GET_CUSTOMER_PHONE_EXT = "phone_no_ext";
	public static final String GET_CUSTOMER_EMAIL = "email_address";
	public static final String GET_CUSTOMER_EKYC_FLAG = "ekyc_flag";
	public static final String GET_CUSTOMER_EMAIL_FLAG = "email_verify_flag";
	public static final String GET_CUSTOMER_EMAIL_TYPE = "email_type";
	public static final String GET_CUSTOMER_BIRTH_DATE = "birth_date";
	public static final String GET_CUSTOMER_RELEASE_DATE = "id_released_date";
	public static final String GET_CUSTOMER_EXPIRE_DATE = "id_expire_date";
	public static final String GET_CUSTOMER_ADDRESS_NODE = "addresses";
	public static final String GET_CUSTOMER_CONSENT_NODE = "consents";
	public static final String GET_CUSTOMER_SOURCE_OF_INCOMES_NODE = "source_of_incomes";
	public static final String GET_CUSTOMER_CURR_ADDR_FLAG = "curr_addr_flag";
	public static final String GET_CUSTOMER_REGIS_ADDR_FLAG = "regis_addr_flag";
	public static final String GET_CUSTOMER_WORK_ADDR_FLAG = "work_addr_flag";
	public static final String GET_CUSTOMER_ADDR_FLAG_Y = "Y";

	public static final String GET_CUSTOMER_ADDR_NO = "address_no";
	public static final String GET_CUSTOMER_VILLAGE_NAME = "build_village_name";
	public static final String GET_CUSTOMER_FLOOR = "floor";
	public static final String GET_CUSTOMER_ROOM_NO = "room_no";
	public static final String GET_CUSTOMER_MOO = "moo";
	public static final String GET_CUSTOMER_SOI = "soi";
	public static final String GET_CUSTOMER_ROAD = "road";
	public static final String GET_CUSTOMER_SUB_DISTRICT = "sub_district";
	public static final String GET_CUSTOMER_DISTRICT = "district";
	public static final String GET_CUSTOMER_PROVINCE = "province";
	public static final String GET_CUSTOMER_COUNTRY = "country";
	public static final String GET_CUSTOMER_PROVINCE_BKK_CODE = "C10000";
	public static final String GET_CUSTOMER_MOO_TH = "\u0e2b\u0e21\u0e39\u0e48 ";
	public static final String GET_CUSTOMER_FLOOR_TH = "\u0e0a\u0e31\u0e49\u0e19 ";
	public static final String GET_CUSTOMER_ROOM_NO_TH = "\u0e2b\u0e49\u0e2d\u0e07 ";
	public static final String GET_CUSTOMER_SOI_TH = "\u0e0b\u002e";
	public static final String GET_CUSTOMER_ROAD_TH = "\u0e16\u002e";
	public static final String GET_CUSTOMER_SUB_DISTRICT_TH = "\u0e15\u0e33\u0e1a\u0e25";
	public static final String GET_CUSTOMER_DISTRICT_TH = "\u0e2d\u0e33\u0e40\u0e20\u0e2d";
	public static final String GET_CUSTOMER_SUB_DISTRICT_BKK_TH = "\u0e41\u0e02\u0e27\u0e07";
	public static final String GET_CUSTOMER_DISTRICT_BKK_TH = "\u0e40\u0e02\u0e15";
	public static final String GET_CUSTOMER_COUNTRY_TH = "\u0e1b\u0e23\u0e30\u0e40\u0e17\u0e28 ";
	public static final String GET_CUSTOMER_POSTAL_CODE = "postal_code";
	// Get-Customer for Other Service Detail
	public static final String GET_CUSTOMER_LIMITED_FLAG = "limited_flag";
	public static final String GET_CUSTOMER_LIMITED_DATE = "limited_date";
	public static final String GET_CUSTOMER_UPDATE_DATE = "update_date";
	public static final String GET_CUSTOMER_CUSTOMER_TYPE = "customer_type";
	public static final String GET_CUSTOMER_NATIONALITY = "nationality";
	public static final String GET_CUSTOMER_SECOND_NATIONALITY = "nationality_2";
	public static final String GET_CUSTOMER_IAL = "ial";
	public static final String GET_CUSTOMER_TYPE_OF_CONSENT = "type_of_consent";
	public static final String GET_CUSTOMER_TYPE_OF_CONSENT_P = "P";
	public static final String GET_CUSTOMER_TYPE_OF_CONSENT_D = "D";
	public static final String GET_CUSTOMER_TYPE_OF_CONSENT_M = "M";
	public static final String GET_CUSTOMER_CONSENT_FLAG = "flag";
	public static final String GET_CUSTOMER_CONSENT_VERSION_NUMBER = "version_number";
	public static final String GET_CUSTOMER_CONSENT_CHANNEL = "channel";
	public static final String GET_CUSTOMER_CONSENT_SYSTEM_DATE = "system_date";

	public static final String GET_CUSTOMER_CONSENT_SIGNUP_DATE = "signup_date";
	public static final String GET_CUSTOMER_SALARY = "salary";
	public static final String GET_CUSTOMER_OCCUPATION_CODE = "occupation_code";
	public static final String GET_CUSTOMER_EDUCATION_CODE = "education_code";
	public static final String GET_CUSTOMER_BUSINESS_TYPE_DESC = "business_type_desc";
	public static final String GET_CUSTOMER_WORKING_PLACE = "working_place";
	public static final String GET_CUSTOMER_COUNTRY_OF_INCOME = "country_of_income";

	// Attributes for investment service detail
	public static final String GET_CUSTOMER_FATCA_FLAG = "fatca_flag";
	public static final String GET_AMLO_REFUSE_FLAG = "amlo_refuse_flag";
	public static final String GET_CUSTOMER_LEVEL = "customer_level";
	public static final String CREATE_CUSTOMER_FATCA_COUNTRY = "US";
	public static final String CREATE_CUSTOMER_FATCA_ANWSER_TYPE = "FP02";
	public static final String GET_CUSTOMER_BUSINESS_TYPE_CODE = "business_type_code";
	public static final String GET_CUSTOMER_KYC_FULL_FILL_FLAG = "full_fill_flag";
	public static final String KYC_REVIEW_LAST_MTN_DATE = "kyc_review_last_mtn_date";
	public static final String KYC_EMPLOYMENT_STATUS = "employment_status";
	public static final String KYC_EMPLOYMENT_CHARACTERISTIC = "employment_characteristic";
	public static final String CUST_TYPE_ID = "cust_type_id";
	public static final String CUSTOMER_STATUS ="customer_status";
	public static final String ID_ISSUE_COUNTRY ="id_issue_country";
	public static final String ENTRY_BRANCH ="entry_branch";
	public static final String PLACE_OF_BIRTH ="place_of_birth";



	public static final String BIOMETRIC_VERIFICATION_HISTORY_DATA_SOURCE = "TMB";
	public static final String REASON_FLAG_ONE = "1"; // data is null
	public static final String REASON_FLAG_TWO = "2"; // date is older than 6 months
	public static final String REASON_FLAG_THREE = "3"; // date is less than 6 months
	public static final String UPDATE_NOT_PASS_SUCCESS_MESSAGE = "Customer not pass verification details updated successfully";
	public static final String UPDATE_NOT_PASS_FAILED_MESSAGE = "Customer not pass verification details updated updated failed";
	public static final String NOTPASS_VERIFY_REASON_LOG = "notpass_verify_reason_log";
	public static final String FLAG_YES = "Y";
	public static final String FLAG_NO = "N";
	public static final String IB_USER_STATUS_ID_VALUE = "02";
	public static final String MB_USER_STATUS_ID_VALUE = "02";
	public static final String DISPLAY_CURRENT_STATUS = "01,02,03,04,10,11,12,13,14";
	public static final Integer FLAG_DISABLE = 0;
	public static final Integer FLAG_ACTIVE = 1;
	public static final String AUTO_SAVE_SLIP_MAIN_VALUE = "Y";
	public static final String AUTO_SAVE_SLIP_OWN_VALUE = "Y";

	public static final String TMB_ENABLE_FLAG_MESSAGE = "Customer TMB ENABLE FLAG received from Db  for customer profile details : {}";
	public static final String PUSH_NOTIFICATION_FLAG_MESSAGE = "Customer PUSH NOTIFICATION FLAG received from Db  for customer profile details : {}";
	public static final String TC_VERSTION_VALUE = "0.0.0";

	public static final String CUSTOMER_UPDATE_EB_STATUS_MESSAGE = "customer EB status to {} updated successfully for : {}";
	public static final String CUSTOMER_UPDATE_MB_STATUS_MESSAGE = "customer MB status to {} updated successfully for : {}";
	public static final String CUSTOMER_UPDATE_IB_STATUS_MESSAGE = "customer IB status to {} updated successfully for : {}";
	public static final String CUSTOMER_UPDATE_EB_REASON_MESSAGE = "update customer change EB status reason to {} updated by for : {}";
	public static final String CUSTOMER_UPDATE_MB_REASON_MESSAGE = "update customer change MB status reason to {} updated by for : {}";
	public static final String CUSTOMER_UPDATE_IB_REASON_MESSAGE = "update customer change IB status reason to {} updated by for : {}";

	public static final String MIGRATE_VERSION_VALUE = "V.10.23.13";
	public static final String APPLY_CHANNEL_VALUE = "CC";
	public static final Integer MIGRATE_MIB_FLAG_VALUE = 1;

	public static final String USERNAME_UV = "username";
	public static final String RMNUMBER_UV = "rmnumber";
	public static final String ROLE_UV = "role";
	public static final String CUSTOMERS_GET_UV_CONNECTION_REFUSED = "customers get UV connection refused : {}";
	public static final String ERROR_CUSTOMERS_GET_UV_RESPONSE = "error response from core banking service for getUv connection : {}";
	public static final String ONEAAPP_PREFIX_REMOVE = "OneApp ";
	public static final String IB_LOGIN_STATUS_OFFLINE = "Offline";
	public static final String IB_LOGIN_STATUS_ONLINE = "Online";
	public static final String IB_USER_LOGIN_CHANNEL = "01";

	public static final String CUSTOMER_EKYC_CLOSE_ERROR_CODE_NOT_FOUND = "ekyc_close_0001";
	public static final String CUSTOMER_EKYC_CLOSE_ERROR_MSG_NOT_FOUND = "the reference id is not found or service is inconvenient.";
	public static final String CUSTOMER_EKYC_ERROR_CODE_PREFIX = "ekyc_";
	public static final String CUSTOMER_EKYC_FATCA_ERROR_CODE_PREFIX = "ekyc_fatca_";
	public static final String CUSTOMER_EKYC_PROSPECT_ERROR_CODE_PREFIX = "ekyc_prospect_";
	public static final String CUSTOMER_EKYC_FATCA_ERROR_CODE_INCORRECT = "0001";
	public static final String CUSTOMER_EKYC_FATCA_ERROR_MSG_INCORRECT = "the anwsers are incorrect.";
	public static final String CUSTOMER_EKYC_FATCA_FLAG_N = "N";
	public static final String CUSTOMER_EKYC_FATCA_ERROR_CODE_Y_Y = "5299";
	public static final String CUSTOMER_EKYC_DOPA_ERROR_CODE_PREFIX = "ekyc_dopa_";

	public static final String DEPOSIT_DATE_FORMAT = "dd-MM-yyyy HH:mm:ss";

	public static final String BANK_DATEFORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSZ";
	public static final String BANK_DATEFORMAT2 = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
	public static final String BANK_DATETIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS";
	public static final String CUSTOMER_EKYC_DOPA_SERVICE_NAME = "check-citizen-by-laser-id";
	public static final String CUSTOMER_EKYC_GET_CUSTOMER_SERVICE_NAME = "customers-get";
	public static final String DOPA_REQUEST_APP_ID = "A0478";
	public static final String CONTENT_TYPE_VALUE_WITH_UTF8 = "application/json;charset=utf-8";
	public static final String CUSTOMER_EKYC_ACRONYM_VALUE = "2338804D";
	public static final String CUSTOMER_EKYC_GET_CUSTOMER_SEARCH_TYPE_VALUE = "citizen-id";
	public static final String CUSTOMER_EKYC_DOB_UNKNOWN_DATE_AND_MONTH = "00";
	public static final String CUSTOMER_EKYC_DOB_DEFAULT_DATE_AND_MONTH = "01";
	public static final String CUSTOMER_EXIST = "Customer Exist";
	public static final String CHANNEL_MB = "mb";
	public static final int NUMBER_ZERO = 0;
	public static final int NUMBER_FOUR = 4;
	public static final int NUMBER_SIX = 6;
	public static final String NUMBER_FIVE = "5";
	public static final int NUMBER_EIGHT = 8;
	public static final int EKYC_DOPA_CALL_RETRYATTEMPTS = 3;
	public static final int ZER0_INT = 0;
	public static final String CITIZEN_ID = "citizen_id";
	public static final String PROSPECT_REQUEST_APP_ID = "01";
	public static final String EKYC_REQUEST_APP_ID_MB = "MB";
	public static final String CC_PROSPECT_REQUEST_APP_ID = "MB";
	public static final String CALLRISK_APP_ID = "A0478";
	public static final String CALLRISK_ACRONYM = "45678";
	public static final String CALLRISK_PRODUCT_CODE = "MIB";
	public static final String CALLRISK_SELECTOR_FLAG = "AAII";
	public static final String CALLRISK_ALERT_FLAG = "Y";
	public static final String CALLRISK_PER_SEQ_KEY = "pers01";
	public static final String CALLRISK_NATIONAL_CODE = "TH";
	public static final String ONEAPP_TELLER_ID = "0010411D";
	public static final String VERIFY_QR_APP_ID = "A0478-MB";
	public static final String CUSTOMER_EKYC_PROSPECT_CREATE_APP_ID = "A0478-MB";
	public static final String CUSTOMER_EKYC_NDID_VERIFY_QR_SERVICE_NAME = "verification_add_qr";
	public static final String CUSTOMER_EKYC_PROSPECT_CREATE_SERVICE_NAME = "create-kyc-prospect";
	public static final String CUSTOMER_EKYC_PROSPECT_SEARCH_SERVICE_NAME = "search-kyc-prospect";
	public static final String CUSTOMER_EKYC_PROSPECT_IMAGE_SERVICE_NAME = "add-kyc-prospect-image";
	public static final String CUSTOMER_EKYC_PROSPECT_UPDATE_SERVICE_NAME = "update-kyc-prospect";
	public static final String CUSTOMER_EKYC_PROSPECT_DELETE_SERVICE_NAME = "delete-kyc-prospect";
	public static final String CUSTOMER_EKYC_GET_PROSPECT_ERROR_CODE_PREFIX = "get-kyc-prospect_";
	public static final String CUSTOMER_EKYC_PROSPECT_VERIFY_BIOMETRIC_SERVICE_NAME = "compare-prospect-biometric";
	public static final String CUSTOMER_EKYC_PROSPECT_GET_IMAGE_SERVICE_NAME = "get-kyc-prospect-image";
	public static final String CUSTOMER_EKYC_PROSPECT_IMAGE_ERROR_CODE_PREFIX = "ekyc_image_";
	public static final String CUSTOMER_EKYC_PROSPECT_SEARCH_ERROR_CODE_PREFIX = "ekyc_search_";
	public static final String CC_CUSTOMER_EKYC_PROSPECT_SEARCH_ERROR_CODE_PREFIX = "prospect_search_";
	public static final String CUSTOMER_EKYC_PROSPECT_GET_IMAGE_ERROR_CODE_PREFIX = "ekyc_get_image_";
	public static final String CUSTOMER_EKYC_PROSPECT_VERIFY_BIOMETRIC_ERROR_CODE_PREFIX = "ekyc_biometric_";
	public static final String CUSTOMER_EKYC_PROSPECT_DELETE_ERROR_CODE_PREFIX = "ekyc_delete_";
	public static final String CUSTOMER_EKYC_CAL_RISK_ERROR_CODE_PREFIX = "ekyc_risk_";
	public static final String CUSTOMER_EKYC_ADDRESS_REGISTERED_KEY_CONSTANT = "REG";
	public static final String CUSTOMER_EKYC_ADDRESS_PRIMARY_KEY_CONSTANT = "PRI";
	public static final String CUSTOMER_EKYC_ADDRESS_OFFICAL_KEY_CONSTANT = "OFF";
	public static final String DATEFORMAT_REFERENCE_NUMBER = "yyyyMMddHHmmss";
	public static final String GENERATE_EKYC_SEQUENCE_NO = "ekyc_sequence_";
	public static final String BIOMETRIC_VERIFICATION_GET_IMAGE_DATA_SOURCE = "TMB";
	public static final String BIOMETRIC_VERIFICATION_GET_IMAGE_DATA_METHOD = "selfie";
	public static final String BIOMETRIC_VERIFICATION_GET_IMAGE_DATA_FORMAT = "JPEG";
	public static final String COMMON_CONFIG_MODULE_NAME = "ekyc_module";
	public static final String CUSTOMER_EKYC_PROSPECT_VERIFY_BIOMETRIC_VERIFICATION_METHOD = "face";
	public static final String CUSTOMER_EKYC_PROSPECT_VERIFY_BIOMETRIC_CHANNEL_VALUE = "mb";
	public static final String CUSTOMER_EKYC_PROSPECT_VERIFY_BIOMETRIC_SOURCE_VALUE = "TMB";
	public static final String CUSTOMER_EKYC_PROSPECT_VERIFY_BIOMETRIC_METHOD_VALUE = "selfie";
	public static final String CUSTOMER_EKYC_PROSPECT_VERIFY_BIOMETRIC_TYPE_VALUE = "cid";
	public static final String CUSTOMER_EKYC_PROSPECT_VERIFY_BIOMETRIC_FORMAT_VALUE = "JPG";
	public static final String CUSTOMER_EKYC_PROSPECT_VERIFY_BIOMETRIC_LENGTH_VALUE = "17453";
	public static final String GET_IMAGE_SUCCESS_CODE = "1000";
	public static final String GET_IMAGE_FAILURE_CODE = "1001";
	public static final String NOT_FOUND_CODE = "404";
	public static final String DATE_YYYY_MM_DD = "yyyy-MM-dd";
	public static final String CUSTOMER_EKYC_PROSPECT_GET_SERVICE_NAME = "get-kyc-prospect";
	public static final String CUSTOMER_EKYC_PROSPECT_ACRONYM_VALUE = "9882001D";
	public static final String CUSTOMER_EKYC_PROSPECT_IMAGE_SOURCE = "TMB";
	public static final String CUSTOMER_EKYC_PROSPECT_IMAGE_FORMAT = "JPG";
	public static final String CUSTOMER_EKYC_PROSPECT_IMAGE_SUCCESS_CODE = "1000";
	public static final String DATEFORMAT_WITH_TIMEZONE = "yyyy-MM-dd'T'HH:mm:ss'Z'";
	public static final String DATETIME_FORMAT = "yyyy-MM-dd'T'HH:mm";
	public static final String DATEFORMAT_YEAR = "yyyy";
	public static final String DATEFORMAT_YEAR_MONTH_DAY = "yyyyMMdd";
	public static final String EKYC_COMMON_CONFIG_MODULE = "ekyc_module";
	public static final String TRANSFER_COMMON_CONFIG_FEIGN_REQUEST_PARAM_SEARCH = "search";
	public static final String CUSTOMER_SERVICE_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
	public static final String STATEMENT_NDID_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";
	public static final String CUSTOMER_SERVICE_DISPLAY_DATE_FORMAT = "dd/MM/yyyy HH:mm:ss";
	public static final String SERVICE_NAME_ANALYTIC_PROFILE = "customers-analytic-profile-get";
	public static final String ACRONYM_ANALYTIC_PROFILE = "2338804D";
	public static final String ETE_DATA_NOT_FOUND_ERROR_CODE = "4001";
	public static final String EKYC_PROSPECT_FLOW_STATE_DEFAULT = "-";

	public static final String BATCH = "Batch";
	public static final String SYSTEM = "System";
	public static final String CUSTOMER_TRANSACTION_LIMIT_REQUEST_STATUS_CANCEL = "04";
	public static final String CUSTOMER_TRANSACTION_LIMIT_REQUEST_STATUS_REJECT = "08";
	public static final String CUSTOMER_TRANSACTION_LIMIT_REQUEST_STATUS_APPROVE = "06";
	public static final String CUSTOMER_TRANSACTION_LIMIT_REQUEST_STATUS_REQUEST = "05";
	public static final String CUSTOMER_TRANSACTION_LIMIT_REQUEST_REJECT_SUCCESS = "Transaction daily limit request has been rejected.";
	public static final String CUSTOMER_TRANSACTION_LIMIT_REQUEST_APPROVE_SUCCESS = "Transaction daily limit request has been approved.";
	public static final String CUSTOMER_TRANSACTION_LIMIT_REQUEST_REJECT_FAIL = "Transaction daily limit request reject fail.";
	public static final String CUSTOMER_TRANSACTION_LIMIT_REQUEST_APPROVE_FAIL = "Transaction daily limit request approve fail.";
	public static final String CUSTOMER_TRANSACTION_LIMIT_REQUEST_REJECT_6MONTH_REASON = "The transaction limit requested period over than 6 months";
	public static final String CUSTOMER_TRANSACTION_LIMIT_REQUEST_REJECT_FRAUD_REASON = "System automatically reject transaction when Android reactivate in same device";
	public static final String REGEX_TRANSACTION_LIMIT_REQUEST_FLEX_VALUE = "[^0-9\\.]";
	public static final String TRANSACTION_LIMIT_REQUEST_SPLIT_STR = "to";

	public static final String THB_FORMAT_DEFAULT = "0.00 ฿";
	public static final String THB_FORMAT = "###,##0.00 ฿";
	public static final String FRAUD_THB_FORMAT = "###,##0.00";

	public static final String TRANSACTION_LIMIT_ACTIVITY_TYPE_ID = "052";
	public static final String TRANSACTION_LIMIT_SERVICE_REQUEST_STATUS = "05,10";
	public static final String TRANSACTION_LIMIT_CHANNEL_ID = "01,02,06";
	public static final String COMMA = ",";
	public static final String TRANSACTION_LIMIT_REQUEST_TYPE = "Domestic";
	public static final String TRANSACTION_LIMIT_REQUEST_CANCEL_VAL = "/request/cancel";

	public static final String ACTIVITY_LOG_PAYLOAD = "Activity Log Payload";
	public static final String ACTIVITY_EVENT_SERVICE = "data posted to event_service : {}";
	public static final String UNABLE_TO_PROCESS_REQUEST = "Unable to process the request : {}";
	public static final String YES_PINFREE_FLAG = "Y";
	public static final String NO_PINFREE_FLAG = "N";

	public static final String NAMESPACE_CITIZEN_CARD = "citizen_card";
	public static final String NAMESPACE_CITIZEN_ID = "citizen_id";
	public static final String IDENTIFIER_TYPE_IDP = "IDP";
	public static final String EVENT_SEARCH = "search";
	public static final String EVENT_REVOKED = "revoked";
	public static final String VERSION_NDID01 = "ndid01";
	public static final String NDID_ERROR_TIMEOUT = "20026";
	public static final String NDID_STATUS_TIMEOUT = "time out";
	public static final String NDID_STATUS_PENDING = "pending";

	public static final String ETE_STATUS_DATA_NOT_FOUND = "4001";
	public static final String CONSENT_TYPE_BIOMETRIC = "BIOMETRIC";
	public static final String CONSENT_TYPE_BIOMETRIC_S = "S";
	public static final String CONSENT_TYPE_BIOMETRIC_B = "B";
	public static final String CHANNEL_MIB = "MIB";
	public static final String USER_ID_BIOMETRIC_CONSENT = "********";

	public static final String HEADER_DEPOSIT_HOLD_REQUEST_DATETIME_FMT = "yyyy-MM-dd'T'HH:mm:ss'Z'";
	public static final String FOUR_ZERO_DIGIT = "0000";

	public static final String TMB_BANK_CODE_THREE_DIGITS = "0011";
	public static final String THB_CURRENCY_CODE = "0001";
	public static final String EMPTY_STRING = "";
	public static final String ZERO = "0";
	public static final String FOUR_ZERO_ADD_ACC = "0000";

	public static final String HEADER_DEPOSIT_HOLD_SERVICE_NAME = "service-name";

	public static final String HEADER_DEPOSIT_REMOVE_HOLD_BALANCE_SERVICE_NAME = "remove-account-hold";
	public static final String HEADER_DEPOSIT_HOLD_GET_BALANCE_SERVICE_NAME = "get-account-hold-balance";
	public static final String HEADER_DEPOSIT_HOLD_REQUEST_UID = "request-uid";
	public static final String HEADER_DEPOSIT_HOLD_REQUEST_APP_ID = "request-app-id";
	public static final String HEADER_DEPOSIT_HOLD_REQUEST_APP_ID_VALUE = "MB";
	public static final String HEADER_DEPOSIT_HOLD_REQUEST_DATETIME = "request-datetime";

	public static final String HB_HISTORY_TRAN_REF_ID_PREFIX = "HB";
	public static final String HB_HISTORY_TRAN_REF_ID_YEAR = "yy";
	public static final String HB_HISTORY_TRAN_REF_ID_ZERO_FMT = "**********0000";
	public static final String HB_HISTORY_TRAN_STATUS = "03";
	public static final String HB_SCHEDULE_DETAILS_SCHEDULE_STATUS = "T";
	public static final String HB_SCHEDULE_DETAILS_LAST_UPDATED_BY = "TMB";
	public static final String HB_SCHEDULE_DETAILS_TRAN_STATUS = "03";
	public static final String HB_GOAL_MASTER_GOAL_STATUS = "T";
	public static final String HB_GOAL_MASTER_LAST_UPDATED_BY = "CC";
	public static final String SELFIE = "selfie";

	public static final String SERVICE_TYPE_MATRIC_CODE_NCBR_SEND_NCB_BY_POST = "O0001";
	public static final String SERVICE_TYPE_MATRIC_CODE_NCBR_SEND_NCB_BY_EMAIL = "O0003";
	public static final String SERVICE_TYPE_MATRIC_CODE_PWA_CALL_TO_ADVISOR = "O0011";
	public static final String SERVICE_TYPE_MATRIC_CODE_PWA_SEND_MESSAGE_TO_ADVISOR = "O0012";
	public static final String SERVICE_TYPE_MATRIC_CODE_PWA_SEND_EMAIL_TO_ADVISOR = "O0013";
	public static final String SERVICE_TYPE_MATRIC_FINCERT_SEND_DOCUMENT_BY_POST = "O0014";

	public static final String EMPTY_DATA = "";
	public static final String COMMON_CONFIG_EKYC_MODULE_NAME = "ekyc_module";
	public static final String DATE_FORMAT_DD_MM_YYYY = "dd/MM/yyyy";
	public static final String DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
	public static final String DATE_FORMAT_YYYY_DD_MM = "yyyy-dd-MM";
	public static final String DATE_FORMAT_YYYY_MM_DD_CREATE_CRM = "yyyy-MM-dd";
	public static final String ERROR_CAL_RISK_CODE_A2 = "A2";
	public static final String ERROR_CAL_RISK_CODE_A3 = "A3";
	public static final String ERROR_CAL_RISK_CODE_B3 = "B3";
	public static final String ERROR_CAL_RISK_CODE_C3 = "C3";
	public static final String ERROR_CODE_001 = "001";
	public static final String ERROR_CODE_002 = "002";
	public static final String STRING_Y = "Y";
	public static final String SAVING = "saving";
	public static final String RISK_VALUE_STRING = "Risk Value :";

	public static final String IS_TRUE = "Y";
	public static final String IS_FALSE = "N";
	public static final String EMAIL_NOTI_ERROR_CODE = "email_notification_settings_10001";
	public static final String EMAIL_NOTI_SUCCESS_MESSAGE = "Email notification settings update successfully";
	public static final String UTF_8 = "utf-8";

    public static final String BIOMETRIC_RESULT_ERROR_CODE_STRING = "-1";
    public static final String BIOMETRIC_RESULT_FIALED_CODE_STRING = "0";
    public static final String BIOMETRIC_RESULT_SUCCEES_CODE_STRING = "1";

	public static final String SALESFORCE_GRANT_TYPE = "password";
	public static final String SALESFORCE_AUTHENTICATION = "Authorization";
	public static final String SALESFORCE_REQ_ID = "ReqId";
	public static final String SALESFORCE_INTEGRATION_SYSTEM = "Integration-System";

	public static final String CUSTOMER_INTERNAL_CASE_SUBMIT_SUCCESS_STATUS = "1";
	public static final String CUSTOMER_INTERNAL_CASE_SUBMIT_FAIL_STATUS = "2";
	public static final int CUSTOMER_INTERNAL_CASE_SUBMIT_MAX_RETRY = 3;
	public static final int CUSTOMER_CASE_HOUSEKEEPING_LIMIT_DAYS = 7;

	public static final String WEALTH_ADVISOR_LICENSE_UNIT_LINK_EN = "Unit Linked Consultant";
	public static final String WEALTH_ADVISOR_LICENSE_UNIT_LINK_TH = "\u0e1c\u0e39\u0e49\u0e41\u0e19\u0e30\u0e19\u0e33\u0e1c\u0e25\u0e34\u0e15\u0e20\u0e31\u0e13\u0e11\u0e4c\u0e22\u0e39\u0e19\u0e34\u0e15 \u0e25\u0e34\u0e07\u0e04\u0e4c";
	public static final String WEALTH_ADVISOR_LICENSE_INVESTMENT_CONSULTANT_EN = "Investment Consultant";
	public static final String WEALTH_ADVISOR_LICENSE_INVESTMENT_CONSULTANT_TH = "\u0e1c\u0e39\u0e49\u0e41\u0e19\u0e30\u0e19\u0e33\u0e01\u0e32\u0e23\u0e25\u0e07\u0e17\u0e38\u0e19";
	public static final String WEALTH_ADVISOR_LICENSE_LIFE_INSURANCE_CONSULTANT_EN = "Life Insurance Consultant";
	public static final String WEALTH_ADVISOR_LICENSE_LIFE_INSURANCE_CONSULTANT_TH = "\u0e15\u0e31\u0e27\u0e41\u0e17\u0e19\u0e1b\u0e23\u0e30\u0e01\u0e31\u0e19\u0e0a\u0e35\u0e27\u0e34\u0e15";
	public static final String WEALTH_ADVISOR_LICENSE_NON_LIFE_INSURANCE_CONSULTANT_EN = "Non-Life Insurance Consultant";
	public static final String WEALTH_ADVISOR_LICENSE_NON_LIFE_INSURANCE_CONSULTANT_TH = "\u0e15\u0e31\u0e27\u0e41\u0e17\u0e19\u0e1b\u0e23\u0e30\u0e01\u0e31\u0e19\u0e27\u0e34\u0e19\u0e32\u0e28\u0e20\u0e31\u0e22";
	public static final String THAILAND_CODE = "TH";
	public static final String ENGLISH_UNLIMITED_CODE = "EN";

	public static final String ONEAPP_APP_ID = "01";
	public static final String CUSTOMER_DATE_FMT = "yyyy-MM-dd";
	public static final String SDA_ACCOUNT_TYPE = "SDA";
	public static final String GENERATE_ACCOUNT_APP_ID = "A0478-MB";
	public static final String GENERATE_ACCOUNT_NO_SERVICE_NAME = "generate-account-number";
	public static final String TELLER_ID = "teller-id";
	public static final String CUSTOMER_CREATE_RM_ID_APP_ID = "A0478-MB";
	public static final String CUSTOMER_CREATE_RM_ID_SERVICE_NAME = "customers-personal-create";
	public static final String CUSTOMER_CREATE_RM_ID_ACRONYM = "2338804D";
	public static final String N_CODE = "N";
	public static final String EKYC_BUSINESS_CODE = "B";
	public static final String CITIZEN_CODE = "CI";
	public static final String RISK_LEVEL_SUCCESS_CODE = "A1";
	public static final String CUSTOMER_EKYC_CREATE_CUSTOMER_ERROR_CODE_PREFIX = "create_customer_";
	public static final String CREATE_ACCOUNT_NO_SERVICE_NAME = "create-deposit-account";
	public static final String CREATE_ACCOUNT_NO_APP_ID = "A0478-MB";
	public static final String ADDRESS_TYPE_DEFAULT_VALUE = "Primary";
	public static final String ADDRESS_SUBTYPE_DEFAULT_VALUE = "RES";
	public static final int ADDRESS_MAX_LENGTH = 40;
	public static final String CUSTOMER_EKYC_LINK_ACCOUNT_TO_CUSTOMER_ERROR_CODE_PREFIX = "link_account_";
	public static final String ECAS_SEGMENT_ID_VALUE = "MIB";
	public static final String ECAS_MB_ACP_W_D_VALUE = "MB_AcPwd";
	public static final String CUSTOMER_UPDATE_CONSENT_APP_ID = "A0478-MB";
	public static final String CUSTOMER_UPDATE_CONSENT_SERVICE_NAME = "update-consent-result";
	public static final String SUCCESS_RESPONSE_DESC = "TMB_ACCOUNT_OPENING_SUCCESS";
	public static final String UPDATE_CONSENT_CHANNEL_VALUE = "MIB";
	public static final String SDA_ACCOUNT_TYPE_VALUE = "SDA";
	public static final String THAILAND_COUNTRY_CODE = "TH";
	public static final String CITIZEN_ID_CODE = "CI";
	public static final String Y_CODE = "Y";
	public static final String MOBILE_CODE = "M";
	public static final String BUSSINESS_CODE = "B";
	public static final String RESIDENTIAL_CODE = "R";
	public static final String EMPTY_SPACE = " ";
	public static final Object DOPA_BYPASS_CODE = "01";
	public static final String DOPA_BYPASS_ZZ = "ZZ";

	public static final String STRING_HYPHEN = "-";
	public static final String SETTINGS_ACTION_DAILY_LIMITS = "daily-limit";
	public static final String DAILY_LIMITS_SUSSESS = "Daily Limit Update Successfully";
	public static final String SETTINGS_ACTION_PIN_FREE = "pin-free";
	public static final String PIN_FREE_SUSSESS = "PinFree details Update Successfully";
	public static final String SETTINGS_ACTION_CARDLESS_LIMITATION = "cardless-limit";
	public static final String CARDLESS_LIMITATION_SUSSESS = "Cardless Limitation Update Successfully";

	public static final String SERVICE_REQ = "SR";
	public static final String SIX = "6";
	public static final String BAHT = "฿";
	public static final String DAILY_LIMITS_DB_MESSAGE = "Increase Daily Limit From ";

	public static final String FAV_DELETED_SUCCESS = "Deleted favouriteId sucessfully";
	public static final String FAVOURITE_DATA_NOT_FOUND = "FavouriteId not found";
	public static final String INVALID_FAVTYPE = "Valid FavouriteType not found";
	public static final String FAV_ACCOUNT = "account";
	public static final String FAV_BILLPAY = "billpay";
	public static final String FAV_TOPUP = "topup";
	public static final String FAV_PROMTPAY = "promptpay";
	public static final String CUSTOMER_GET_E_STATEMENT = "customers-e-statement-get";
	public static final String CUSTOMER_UPDATE_E_STATEMENT = "customers-e-statement-update";
	public static final String PUSH_READ_FLAG_SUCCESS = "message status updated successfully!!";
	public static final String PUSH_DELETE_SUCCESS = "message deleted successfully!!!";
	public static final String BADGE_RESET_SUCCESS = "Badge reset successful!!";
	public static final String COMMON_MODULE_NAME = "common_module";
	public static final String CARDLESS_MODULE_NAME = "cardless_module";
	public static final int DEVICE_STATUS_FLAG = 1;
	public static final Integer BIOMETRIC_ENABLE_FLAG = 0;
	public static final String APPLICATION_ID_FLAG = "01";
	public static final String IOS_VALUE = "ios";
	public static final String ANDROID_VALUE = "android";
	public static final String ERROR_UNBALE_INSERT_CRM_RELATION = "RM relation error";
	public static final String ERROR_UNBALE_INSERT_CRM_CUST_PROFILE = "Customer Profile Error";
	public static final String ERROR_UNBALE_INSERT_TC_CONFIG = "Term Condition Error";
	public static final String ERROR_UNBALE_INSERT_CRM_CUST_APP_LEVEL_ATTR = "Customer APP LEVEL error";
	public static final String ERROR_UNBALE_INSERT_CUSTOMER_DEVICE_INFORMATION = "Customer Device Information Error";
	public static final String ERROR_UNBALE_INSERT_APP_CONTROL = "App Control Error ";

	public static final String ALPHABET_I = "I";
	public static final String B011B = "B011B";
	public static final String TMBM = "TMBM";
	public static final String TMBI = "TMBI";
	public static final String MB = "mb";
	public static final String PROMPTPAY_VALIDATION = "promptpay-credittransfer-inq";
	public static final String TRANSFER_ERROR_GENERATE_RUNNING_NUMBER_MESSAGE = "unable to generate running number";
	public static final String TERMINAL_REF = "terminal_ref";

	public static final String FEV_ACC_ID = "**********";
	public static final int FEV_ACC_LEN = 10;
	public static final String FEV_BANK_CODE = "11";
	public static final String FEV_TAX_ID = "*************";
	public static final String FEV_ACC_TYPE = "00";
	public static final String FEV_ACC_BANK_CODE = "06";
	public static final String FEV_PROXY_VAL = "**********";
	public static final String FEV_SENDER_TYPE = "H";
	public static final String TERMINAL_ID = "80";
	public static final String REQUEST_APP_ID_VAL = "A0478-MB";
	public static final String FEV_ACC_STATUS = "01";

	public static final String PROMPTPAY = "promptpay";
	public static final String TOPUP = "topup";
	public static final String BILLPAY = "billpay";
	public static final String TRANSFER = "transfer";
	public static final String TTB = "11";
	public static final String SDA_VALUE = "279";
	public static final String CDA_VALUE = "3";
	public static final String DDA_VALUE = "1";
	public static final String CDA_ACCOUNT = "CDA";
	public static final String DDA_ACCOUNT = "DDA";
	public static final String FEV_PROXY_VAL_MOB = "**********";
	public static final String FEV_PROXY_VAL_CITY = "*************";
	public static final String FEV_PROXY_TYPE_MOB = "02";
	public static final String FEV_PROXY_TYPE_CITY = "01";
	public static final String SUCCESS_CODE_0 = "0";
	public static final String FAV_ADD_SUCCESS = "Favourite Added Successfully";
	public static final String FAV_UPDATE_SUCCESS = "Favourite Updated Successfully";
	public static final String STATUS_SUCCESS_CODE = "0000";
	public static final String ERROR_UNBALE_INSERT_FAVINFORMATION = "Fav Information Error";

	public static final Integer EB_TXN_LIMIT_AMT = 50000;
	public static final Integer EB_MAX_TXN_LIMIT_AMT_CURRENT = 200000;
	public static final Integer EB_MAX_LIMIT_AMT_HIST = 500000;
	public static final Double EB_ACCU_USG_AMT_DAILY = Double.valueOf(0);
	public static final String FAV_NICKNAME = "nickName";
	public static final String FAV_PROMPTPAY_MOB_NUM = "promptpaymob";

	public static final String FLOW_NAME_REACTIVATE = "Reactivate";
	public static final String FLOW_NAME_REACTIVATION = "Reactivation";
	public static final String FLOW_NAME_RESET_PIN = "Reset Pin";
	public static final String FLOW_NAME_FORGOT_PIN = "Forgot Pin";
	public static final String FLOW_NAME_CANCEL_USER = "Cancel user";
	public static final String FLOW_NAME_COB = "COB";

	public static final String STATE_INTRODUCTION = "introduction";
	public static final String STATE_VERIFIED = "verified";
	public static final String REDIS_KEY_KYC_REF_ID = "KYC_VERIFY_PIN_REF_ID_%s";
	public static final String VERIFY_PIN_REF_ID_PREFIX = "VERIFY_PIN_REF_ID_%s";
	public static final String REDIS_KEY_VERIFY_PIN_STATUS = "VERIFY_PIN_REF_ID_%s";
	public static final String REF_ID_FORMAT = "KYC_REVIEW_%s";
	public static final String REDIS_KEY_BLOCK_FLAG_FORMAT = "BLOCK_FLAG_%s";
	public static final String REDIS_KEY_CUSTOMER_DEVICE_FORMAT = "CUSTOMER_DEVICE_%s";
	public static final String REDIS_KEY_ACTIVATION_OPTIONS_FORMAT = "ACTIVATION_OPTION_%s";
	public static final int REDIS_KEY_CUSTOMER_DEVICE_TTL = 360;
	public static final String KYC_PRODUCT_PF = "PF";
	public static final String KYC_PRODUCT_KYC = "KYC";
	public static final String FLAG_U = "U";
	public static final String REDIS_KEY_KYC_DATA = "KYC_DATA_%s";
	public static final String REDIS_KEY_DOPA_UPLIFT_REF_ID = "customer:uplift:dopa_result:ref_id:%s";
	public static final String REDIS_KEY_ACTIVATION_FRAUD_DATA = "%s_activation_fraud";

	public static final String HEADER_AUTHORIZATION = "Bearer %s";
	public static final int CRM_ID_LENGTH = 16;
	public static final String CUSTOMER_TYPE = "personal";
	public static final String TRUE = "true";
	public static final String AMOUNT_ONE = "1";
	public static final String FAV_DUBLICATE_MOB = "dublicateMob";
	public static final String FAV_DUBLICATE_CITI = "dublicateCiti";
	public static final String ADD = "add";
	public static final String UPDATE = "update";

	public static final String SERVICE_NAME_GET_EXCHANGE_RATE = "get-exchange-rate";
	public static final String FAV_DUBLICATE_CACHEKEY = "cachekey";
	public static final String FAV_DUBLICATE_ENTRY = "Dublicate Entry";
	public static final int FAV_TOPUP_BILLERID = 100300;
	public static final int FAV_BILLPAY_BILLERID = 100343;
	public static final String FAV_COMERCIALID = "1";
	public static final String FAV_BILLPAY_GROUP_TYPE = "0";
	public static final String FAV_TOPUP_GROUP_TYPE = "1";

	public static final String CAPTCHA_ACT_TYPE_ID = "ActivationViaCard";
	public static final String ACC_BANK_CODE = "06";
	public static final Integer CARDLESS_MAX_LIMIT_DEFAULT = 100000;
	public static final Integer TXN_LIMIT_DEFAULT = 200000;
	public static final Integer MAX_LIMIT_AMT_CURR_DEFAULT = 200000;
	public static final Integer MAX_LIMIT_AMT_REQUEST_DEFAULT = 0;
	public static final Integer MAX_LIMIT_AMT_HIST_DEFAULT = 500000;
	public static final Integer ACCU_USG_AMT_DAILY_DEFAULT = 0;
	public static final String LANGUAGE_DEFAULT = "TH";
	public static final Integer OTT_MAX_LIMIT_AMT_CURR_DEFAULT = 1500000;
	public static final Integer OTT_MAX_LIMIT_AMT_HIST_DEFAULT = 1500000;
	public static final Integer OTT_MAX_LIMIT_AMT_REQUEST_DEFAULT = 0;
	public static final Integer OTT_MAX_LIMIT_AMT_DAILY_DEFAULT = 0;
	public static final String APP_ID_DEFAULT = "01";
	public static final String IB_USER_STATUS_ID_NON_ACTIVE_VALUE = "00";
	public static final String TOKEN_DEVICE_FLAG_EKYC = "N";
	public static final String IB_APPLY_CHANNEL_EKYC = "00";
	public static final Integer MAX_TO_ACCT_ID = 10;

	public static final String BILLER_GROUP_TYPE_BILL = "0";
	public static final String BILLER_GROUP_TYPE_TOP_UP = "1";
	public static final String COMMON_CONFIG_COMMON_MODULE_NAME = "common_module,ekyc_module";
	public static final String COMMON_CONFIG_AND_EKYC_CONFIG_COMMON_MODULE_NAME = "common_module,ekyc_module";
	public static final String IB_USER_STATUS_ID_DEFAULT = "00";
	public static final String MB_VALUE = "MB";

	public static final String TRANSFER_FAVORITE_TYPE = "00";
	public static final String PROMPTPAY_CITIZEN_ID_FAVORITE_TYPE = "01";
	public static final String PROMPTPAY_MOBILE_NO_FAVORITE_TYPE = "02";
	public static final String TAX_ID_FAVORITE_TYPE_FROM_TOUCH_NOW_CANCEL = "03";

	public static final String ETE_ACCNO_ERR = "B247087";
	public static final String ETE_INACTIVE_ACC_ERR = "B247088";
	public static final String ETE_PROMPTPAY_ERR = "B247048";
	public static final String ETE_PROMPTPAY_UNAUTHORIZE_ERR = "B247050";
	public static final String ETE_PROMPTPAY_ERR_MOB = "ete_invalidetemob";
	public static final String ETE_PROMPTPAY_ERR_CITI = "ete_invalideteciti";
	public static final String ETE_ACC_ERR = "ete_invalideteacc";
	public static final String INVALID_REQUEST = "Invalid Request";

	public static final String INVALID_REQUEST_MESSAGE = "Invalid Request";

	// Push Notification Template
	public static final String ONEAPP_REFID_WOW = "oneapp_refid_wow";
	// Deposit
	public static final String DEPOSIT_NOT_FOUND_CODE = "5299";
	public static final Integer CREATE_CUSTOMER_IAL_VALUE = 230;
	public static final String DAILY_LIMIT_SERVICE_REQUEST_STATUS = "05";
	public static final String DAILY_LIMIT_ACTIVITY_TYPE_ID = "052";
	public static final String CANCEL_SERVICE_REQUEST_STATUS = "04";
	public static final String CUSTOMER_CANCEL_SR = "Customer";

	// save to ODS
	public static final String SAVE_ODS_APP_ID = "01";
	public static final String SUCCESS_UPDATE_ODS_ACTIVITY = "Successfully update customer activity to ODS";
	public static final String ODS_ADD_STATUS_TXT = "add-status";
	public static final String ODS_ADD_STATUS_DATE_FORMAT = "YYYY-MM-dd'T'HH:mm:ssZ";

	public static final String ETE_FATCA_CAL_ERR_CODE = "5002";
	public static final String FATCA_CAL_ALL_FREE_ACTIVITY_TYPE_ID = "102600105";
	public static final String FATCA_CAL_NO_FIXED_ACTIVITY_TYPE_ID = "102600204";
	public static final String FATCA_CAL_REAM_ACTIVITY_TYPE_ID = "102600404";
	public static final String FATCA_CAL_SAVING_CARE_ACTIVITY_TYPE_ID = "102600504";
	public static final String PRODUCT_CODE_225 = "225";
	public static final String PRODUCT_CODE_221 = "221";
	public static final String PRODUCT_CODE_206 = "206";
	public static final String PRODUCT_CODE_211 = "211";
	public static final String PRODUCT_CODE_659 = "659";
	public static final String PRODUCT_CODE_678 = "678";
	public static final String PRODUCT_CODE_672 = "672";
	public static final String PRODUCT_CODE_673 = "673";
	public static final String PRODUCT_CODE_674 = "674";
	public static final String PRODUCT_CODE_675 = "675";
	public static final String PRODUCT_CODE_677 = "677";

	public static final String PRODUCT_CODE_666 = "666";
	public static final String PRODUCT_CODE_664 = "664";
	public static final String PRODUCT_CODE_300 = "300";
	public static final String PRODUCT_CODE_301 = "301";
	public static final String PRODUCT_CODE_302 = "302";
	public static final String PRODUCT_CODE_601 = "601";
	public static final String PRODUCT_CODE_602 = "602";

	public static final String FCD_PRODUCT_CODE_282 = "282";
	public static final String FCD_PRODUCT_CODE_383 = "383";
	public static final String FCD_PRODUCT_CODE_384 = "384";
	public static final String FCD_PRODUCT_CODE_386 = "386";
	public static final String ACTIVITY_LOG_FATCA_CAL_STEP = "Completed FATCA Question";
	public static final String ACTIVITY_LOG_FATCA_CAL_FLOW = "Open 2nd Account";
	public static final String X_REAL_IP = "X-real-ip";
	public static final String OS_VERSION = "OS-Version";
	public static final String MOBILE_CODE_VALUE = "MB";
	public static final String APP_VERSION = "App-Version";
	public static final String X_CRMID = "x-crmid";
	public static final String TEMP_ID = "temp-id";
	public static final String ACTIVITY_SUCCESS = "Success";
	public static final String CUSTOMER_EKYC_PROSPECT_NDID_CONSENT_SERVICE_NAME = "get-consent-status";
	public static final String EKYC_GET_NDID_RESULT_NDID_EVENT_VALUE = "accepted";
	public static final String EKYC_GET_NDID_RESULT_IDENTIFIER_TYPE_VALUE = "RP";
	public static final String REQUEST_TYPE_GET_DIPCHIP_STATUS = "get_dipchip";
	public static final String CUSTOMER_EKYC_NDID_VERIFY_IDP_CONSENT_SERVICE_NAME = "verify-idp-consent";
	public static final String CUSTOMER_EKYC_NDID_GET_INCOMING_LIST_SERVICE_NAME = "get-incoming-list";
	public static final String CUSTOMER_EKYC_GET_BIOMETRIC_SERVICE_NAME = "get-biometric";
	public static final String CUSTOMER_EKYC_VERIFY_BIOMETRIC_SERVICE_NAME = "verify-biometric";

	public static final String SERVICE_NAME_GET_CONSENT_STATUS = "get-consent-status";
	public static final String SERVICE_NAME_GET_DIPCHIP_BY_CID = "get-dipchip-by-citizen-id";

	public static final String SERVICE_NAME_GET_CONSENT = "get-biometric-consent";
	public static final String SERVICE_NAME_UPDATE_CONSENT = "update-consent";
	public static final boolean KEY_VERSION_BIOMETRIC = true;
	public static final String SERVICE_NAME_UPDATE_BIO_METRIC_CONSENT = "update-biometric-consent";
	public static final String UPDATE_BIO_METRIC_ACRONYM_VALUE = "9831I201";
	public static final String SERVICE_NAME_AS_STATEMENT_INQUIRY = "as-statement-inquiry";

	public static final String INITIAL_VECTOR = "initial_vector";
	public static final String ENCRYPTED_DATA = "encrypted_data";
	public static final String ENCRYPTION_AES_ALGORITHM = "AES";
	public static final String ENCRYPTION_AES_METHOD = "AES/CBC/PKCS5Padding";

	public static final String UPDATE_BIOMETRIC_FLAG_ACTIVITY_TYPE_ID = "100200200";
	public static final String AUTO_UPDATE_BIOMETRIC_FLAG_FLOW = "Login - Auto detect Biometric change in device";
	public static final String TURN_ON = "Turn ON";
	public static final String TURN_OFF = "Turn OFF";
	public static final String SERVICE_NAME_CUSTOMER_GET_PHONE_MOBILE = "customers-phones-mobile-get";
	public static final String UPDATE_CUSTOMER_IAL_SERVICE_NAME = "customers-personal-update";

	public static final String TXN_TYPE_AVOID = "011";
	public static final String TXN_TYPE_CC_DISPLAY_FLAG_Y = "Y";

	public static final String J_SESSION_STR = "JSESSIONID=";
	public static final String COOKIE_STR = "Cookie";
	public static final String TITLE_CODE = "title_code";

	//For PDPA Consent
	public static final String HEADER_TYPE_OF_CONSENT = "Type-of-consent";
	public static final String TYPE_OF_CONSENT_MARKET = "market";
	public static final String TYPE_OF_CONSENT_PDPA = "pdpa";

	public static final String HEADER_SERVICE_NAME = "service-name";
	public static final String HEADER_REQUEST_UUID = "request-uid";
	public static final String HEADER_APP_ID = "request-app-id";
	public static final String HEADER_REQUEST_DATE_TIME = "request-datetime";

	public static final String ETE_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ssZ";
	public static final String PDPA_CONSENT_GET_FORM_ETE_SERVICE_NAME = "forms-consents-inq";
	public static final String PDPA_CONSENT_GET_FORM_ETE_APP_ID = "A0478-MB";
	public static final String PDPA_CONTENT_TEMPLATE_MODE = "D";
	public static final String PDPA_CONSENT_TEMPLATE_MODE = "P";
	public static final String MARKET_CONSENT_TEMPLATE_MODE = "M";
	public static final String PDPA_CONSENT_TEMPLATE_LANG_TH = "TH";
	public static final String PDPA_CONSENT_TEMPLATE_LANG_EN = "EN";
	public static final String COMMON_CONFIG_MODULE_SERVICE_HOUR = "service_hour";
	public static final String PDPA_SERVICE_HOUR_START_TIME = "06:00";
	public static final String PDPA_SERVICE_HOUR_END_TIME = "22:55";
	public static final String PDPA_GET_CONSENT_ETE_SERVICE_NAME = "customers-pdpa-get";
	public static final String PDPA_GET_CONSENT_SEARCH_TYPE = "rm-id";
	public static final String PDPA_UPDATE_CONSENT_ETE_SERVICE_NAME = "customers-pdpa-update";
	public static final String PDPA_TYPE_OF_CONSENT_PDPA = "PDPA-DEPOSIT";
	public static final String PDPA_TYPE_OF_CONSENT_DATA_ANALYTIC = "DATA-ANALYTIC";
	public static final String PDPA_TYPE_OF_CONSENT_MARKET = "MARKET";
	public static final String PDPA_CONSENT_SYSTEM_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";
	public static final String DATETIME_ISO = "yyyy-MM-dd'T'HH:mm:ss";
	public static final String AGREE_PDPA_DATA_ANALYTIC_MARKET_FLAG = "Agree";
	public static final String DISAGREE_PDPA_DATA_ANALYTIC_MARKET_FLAG = "Disagree";
	public static final String PDPA_CONTENT_TH_KEY = "PDPA_CONTENT_TH";
	public static final String PDPA_CONTENT_EN_KEY = "PDPA_CONTENT_EN";

	public static final String FATCA_CAL_TD_PRODUCT_ACTIVITY_TYPE_ID = "102600304";

	/* ETE */
	public static final String ERROR_CUSTOMERS_GET_ETE_RESPONSE = "Error response from ETE service : {}";

	public static final String ACTIVITY_LOG_DEEP_LINK_STEP = "Open deeplink";
	public static final String DEEP_LINK_102600113_ACTIVITY_TYPE_ID = "102600113";
	public static final String DEEP_LINK_102600212_ACTIVITY_TYPE_ID = "102600212";
	public static final String DEEP_LINK_102600312_ACTIVITY_TYPE_ID = "102600312";
	public static final String DEEP_LINK_102600513_ACTIVITY_TYPE_ID = "102600513";
	public static final String DEEP_LINK_102600611_ACTIVITY_TYPE_ID = "102600611";
	public static final String DEEP_LINK_102600711_ACTIVITY_TYPE_ID = "102600711";

	public static final String CHANNEL_UPDATE_BIOMETRIC_CONSENT = "A0478";
	public static final String USER_ID = "99";
	public static final String CONSENT_TYPE_STORE = "B";
	public static final String CONSENT_TYPE_SHARE = "S";
	public static final String REGISTERED_PROMPTPAY_UPDATE_SERVICE_NAME = "registered-promptpay-mobile-update";
	public static final String CHANNEL_UPDATE_REGISTERED_PROMPTPAY = "A0478";

	public static final String COMMON_FLOW_ERROR_CODE_PREFIX = "common_";

	public static final String BANKRUPTCY_CITIZEN_ID = "bankrupt-status-inquiry-by-citizen-id";
	public static final String BANKRUPTCY_JURISTIC = "bankrupt-status-inquiry-by-juristic-no";
	public static final String BANKRUPTCY_PASSPORT = "bankrupt-status-inquiry-by-passport-no";
	public static final String BANKRUPTCY_JUDGEMENT = "bankrupt-get-judgement-status";

	public static final String STATUS_CODE_200 = "200";
	public static final String OPEN_ACCOUNT_MODULE_NAME = "open_account_module";
	//Open deposite account pen test fix
	public static final String DEPOSIT = "deposit";
	public static final String REDIS_KEY_CITIZEN_ID_FACTA_RESULT = "%s_FACTA_RESULT";
	public static final String REDIS_KEY_CITIZEN_ID_DOPA_RESULT = "%s_DOPA_RESULT";
	public static final String REDIS_KEY_CITIZEN_ID_CALRISK_RESULT = "%s_CALRISK_RESULT";
	public static final String REDIS_KEY_CITIZEN_ID_BIOMETRIC_RESULT = "%s_BIOMETRIC_RESULT";
	public static final String REDIS_KEY_CITIZEN_ID_VERIFY_METHOD_RESULT = "%s_VERIFY_METHOD_RESULT";
	public static final String REDIS_VALUE_PASS = "PASS";

	public static final String MY_BENEFIT_CREDIT_CARD_CAMPAIGN_KEY = "mybenefit_%s_%s";
	public static final String HEADER_FLOW_FIRST_DEPOSIT = "flow";

	public static final String PASS = "Pass";
	public static final String FAIL = "Fail";
	public static final String FAILED = "Failed";

	public static final String TRANSFER_MODULE = "transfer_module";

	public static final String APP_ID_APPLICANT_ADDR = "01";

	public static final BigDecimal MAXIMUM_EXCHANGE_DAILY_LIMIT = BigDecimal.valueOf(********);

	public static final String PIN_FREE_SEETING_Y_FLAG = "Y";
	public static final String PIN_FREE_SEETING_N_FLAG = "N";
	public static final String CUSTOMER_STATUS_EB_SUSPENDED_BY_BANK = "10";
	public static final String CUSTOMER_STATUS_EB_TEMPORARY_SUSPENDED_BY_BANK = "11";
	public static final String CUSTOMER_STATUS_MB_CANCELLED_BY_BANK = "08";
	public static final String CUSTOMER_STATUS_MB_TEMPORARY_BLOCK = "09";

	public static final String CONFIRM = "confirm";

	public static final String SLASH = "/";
	public static final String FAVORITE_PAYEE = "/favorite_payee/";

	public static final String FOREIGNER_CUSTOMER_TYPE = "920";

	public static final String ACTIVITY_DAILY_TRANSACTION_LIMIT_COMPARE_SELFIE_ACTIVITY_TYPE_ID = "*********";
	public static final String ACTIVITY_DAILY_TRANSACTION_LIMIT_COMPARE_SELFIE_STEP = "Compare selfie image";
	public static final String ACTIVITY_DAILY_TRANSACTION_LIMIT_COMPARE_SELFIE_FLOW = "Setting";
	public static final String ACTIVITY_DAILY_TRANSACTION_LIMIT_COMPARE_SELFIE_IAL_LEVEL_2_3 = "2.3";
	public static final String BIOMETRIC_COMPARE_MATCH = "1";
	public static final String EKYC_CONFIGURATION_MODULE = "ekyc_module";

	public static final int SETTING_FLOW_ID = 9001;

	public static final String PROFILE_SETTING_DAILY_LIMIT_DOMESTIC = "domestic";
	public static final String PROFILE_SETTING_DAILY_LIMIT_INTERNATIONAL = "international";
	public static final String PROFILE_SETTING_DAILY_LIMIT_BILL_PAYMENT = "bill payment";
	public static final String PROFILE_SETTING_DAILY_LIMIT_CARDLESS = "cardless";
	public static final Integer PROFILE_SETTING_DAILY_LIMIT_DEFAULT_LIMIT = 50000;
	public static final Integer PROFILE_SETTING_DAILY_LIMIT_DOMESTIC_DEFAULT_LIMIT = 50000;
	public static final Integer PROFILE_SETTING_DAILY_LIMIT_INTERNATIONAL_DEFAULT_LIMIT = 50000;
	public static final Integer PROFILE_SETTING_DAILY_LIMIT_BILL_PAYMENT_DEFAULT_LIMIT = 50000;
	public static final Integer PROFILE_SETTING_DAILY_LIMIT_CARDLESS_DEFAULT_LIMIT = 50000;
	public static final Integer PROFILE_SETTING_DAILY_LIMIT_DOMESTIC_MAX_LIMIT = 5000000;
	public static final Integer PROFILE_SETTING_DAILY_LIMIT_INTERNATIONAL_MAX_LIMIT = 1500000;
	public static final Integer PROFILE_SETTING_DAILY_LIMIT_BILL_PAYMENT_MAX_LIMIT = 5000000;
	public static final Integer PROFILE_SETTING_DAILY_LIMIT_CARDLESS_MAX_LIMIT = 200000;
	public static final Integer PROFILE_SETTING_DAILY_LIMIT_CHILD_MAX_LIMIT = 5000;
	public static final Integer PROFILE_SETTING_DAILY_LIMIT_CHILD_AGE = 15;
	public static final Integer PROFILE_SETTING_DAILY_LIMIT_OLD_OS = 5000;
	public static final Integer X_COR_LENGTH = 60;
	public static final Integer CUST_ID_LENGTH = 8;
	public static final Integer RM_ID_LENGTH = 50;
	public static final Integer CSTA_LENGTH = 5;
	public static final Integer FULL_NAME_LENGTH = 101;
	public static final Integer CARD_TYPE_LENGTH = 8;
	public static final Integer ID_CARD_LENGTH = 25;
	public static final String EKYC_OPEN_ACCOUNT_ACTION_TYPE_VERIFY_BIOMETRIC = "NEW-ACCT";
	public static final String PROFILE_SETTING_DAILY_LIMIT_CHILD_GROUP = "Y";
	public static final String PROFILE_SETTING_DAILY_LIMIT_R1_GROUP = "R1";

	public static final String REDIS_CACHE_MANAGER = "redisCacheManager";
	public static final String REDIS_ENABLED_CONFIG = "redis.enabled";
	public static final String CUSTOM_CACHE_CONFIG = "custom.cache";
	public static final String TRUE_STRING = "true";
	public static final String FALSE_STRING = "false";
	public static final String CUSTOMER_FR_NORMAL_TYPE = "01";
	public static final String CUSTOMER_FR_NORMAL_TYPE_DISPLAY = "Normal customer";
	public static final String ACTIVITY_LOG_UPDATE_FR_CUSTOMER_TYPE = "*********";
	public static final String FAILED_REASON_UPDATE_FR_CUSTOMER_TYPE = "Cannot initiate customer FR type in database";

	public static final String CACHE_KYC_DATA = "KYC_DATA_";
	public static final String CACHE_ADD_FAVORITE = "add_favorite_";
	public static final String CACHE_UPDATE_FAVORITE = "update_favorite_";
	public static final String CUSTOMER_RESPONSE_BODY_IS_UNAVAILABLE = "customer Response body data is unavailable";
	public static final String VERIFY_PIN_MODULE = "KYC_VERIFY_PIN_STATUS";
	public static final String AUTH_VERIFY_PIN_REF_ID_PREFIX = "VERIFY_PIN_REF_ID_";
	public static final String PIN_REF_ERROR_CODE = "PIN_0001";
	public static final String HEADER_REFER_ID = "ref-id";

	public static final String FLOW_VERIFICATION_EKYC_ACTION_TYPE_ID = "21";
	public static final String FLOW_VERIFICATION_EKYC_CHANNEL_ID = "02";
	public static final String FLOW_VERIFICATION_EKYC_FLOW_STATUS_ID = "92";
	public static final String ERROR_UPDATE_DB = "Unable to update daily limit.";

	public static final String NO_SUCH_FILE = "No such file";
	public static final String ACTIVITY_ID_101501020 = "101501020";
	public static final String ACTIVITY_ID_101501021 = "101501021";
	public static final String BENEFIT_CC_CAMPAIGN_COLLECTION = "benefit_CC_campaign";
	public static final String CREDITCARD_PRODUCT_ALLOW_COLLECTION= "creditcard_product_allow";
	public static final String MY_BENEFIT_MODULE = "my_benefit_module";
	public static final String CREDITCARD_PRODUCT_ALLOW_CACHE_KEY = "mybenefit_" + CREDITCARD_PRODUCT_ALLOW_COLLECTION;
	public static final String MY_BENEFIT_MODULE_CACHE_KEY = MY_BENEFIT_MODULE + "_config";
	public static final String MY_BENEFIT_MODULE_INAPP = "inapp";
	public static final String INTEGRITY_INVALID = "Detect integrity invalid by FDIS system";
	public static final String HEADER_DEVICE_MODEL= "Device-Model";
	public static final String MY_BENEFIT_CONFIG_INCORRECT_PARAM_FORMAT = "Parameter '%s' is incorrect";
	public static final String MY_BENEFIT_CONFIG_INCORRECT_UNAVAILABLE_PLAY = "Parameter 'unavailable_play' is incorrect";
	public static final String MY_BENEFIT_CONFIG_INCORRECT_DATE_FROM = "Parameter 'unavailableDateFrom' is incorrect";
	public static final String MY_BENEFIT_CONFIG_INCORRECT_DATE_TO = "Parameter 'unavailable_date_to' is incorrect";
	public static final String MY_BENEFIT_CONFIG_INCORRECT_DATE_FROM_TH = "Parameter 'display_date_from_TH' is incorrect";
	public static final String MY_BENEFIT_CONFIG_INCORRECT_DATE_TO_TH = "Parameter 'display_date_to_TH' is incorrect";
	public static final String MY_BENEFIT_CONFIG_INCORRECT_DATE_FROM_EN = "Parameter 'display_date_from_EN' is incorrect";
	public static final String MY_BENEFIT_CONFIG_INCORRECT_DATE_TO_EN = "Parameter 'display_date_to_EN' is incorrect";
	public static final String MY_BENEFIT_CONFIG_INCORRECT_INAPP = "Parameter 'inapp' is incorrect";
	public static final String MY_BENEFIT_CONFIG_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";

	public static final String GSB_BANK_CODE = "30";
	public static final String GSB_FAV_MIN_AMOUNT = "50";

	public static final String TOGGLE_ACCOUNT_STATUS_SHOW = "01";
	public static final String TTB_PERSONALIZED_ACCT_NAME = "_MyTMB_";
	public static final String PERSONALIZED_STATUS_SHOW = "01";
	public static final String PERSONALIZED_STATUS_HIDE = "02";
	public static final String VERIFY_UNIVERSAL_LINK = "verify-universal-link";
	public static final String EHCACHE_CACHE_MANAGER_NAME = "ehcache";
	public static final String SOAPENV_PREFIX = "soapenv";
	public static final String WEB_PREFIX = "web";
	public static final String XML_SOAP_SCHEMAS_URL = "http://schemas.xmlsoap.org/soap/envelope/";
	public static final String SEARCH_ROSC_URL = "http://data.common.tmb.com/WebSearchKYCPersonal/";
	public static final String CREATE_ROSC_URL = "http://data.common.tmb.com/WebCreateKYCPersonal/";
	public static final String MAINTAIN_ROSC_URL = "http://data.common.tmb.com/WebMaintainKYCPersonal";
	public static final String MXL_SCHEMA_INSTANCE = "http://www.w3.org/2001/XMLSchema-instance/";
	public static final String HEADER_SOAP_ACTION = "SOAPAction";
	public static final String CUST_ID = "cust_id";
	public static final String CSTA_PREVIOUS = "csta_status_previous";
	public static final String CSTA_CURREENT = "csta_status_current";
	public static final String FULL_NAME = "full_name";
	public static final String CARD_TYPE = "card_type";
	public static final String XML_ENCRYPT_CONTENT_TYPE = "text/xml; charset=UTF-8";
	public static final String EKYC_DOCUMENT_ID_TYPE_VALUE = "cid";
	public static final String ERROR_CODE_8200 = "8200";
	public static final String ERROR_CODE_8201 = "8201";
	public static final String ERROR_CODE_8202 = "8202";
	public static final String ERROR_CODE_8299 = "8299";
	public static final String RES_CODE_4511 = "4511";
	public static final String EKYC_HEADER_LAST_MODIFIED = "Last-Modified";
	public static final String EKYC_HEADER_APP_ID = "App-ID";
	public static final String EKYC_HEADER_DOCUMENT_ID ="Document-ID";
	public static final String EKYC_HEADER_INITIAL_VECTOR ="Initial-Vector";
	public static final String SUCCESS_CODE_NEW = "000000";
	public static final String UNABLE_TO_UPDATE = "111003";
	public static final String UNABLE_TO_UPDATE_MESSAGE = "Unable to update customer status";
	public static final String HEADER_FLOW_NAME = "Flow-Name";
	public static final String MESSAGE_DUMMY = "Dummy";
	public static final String ERROR_LIMIT_GROUP_NOT_FOUND = "Group Limit config is not found";

}
