package com.tmb.oneapp.customerservice.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RedisConstants {
    public static final String CACHE_FETCHING_ERROR_MESSAGE = "exception while fetching data from Redis {}";
    public static final String CACHE_POSTING_ERROR_MESSAGE = "exception while setting data to Redis {}";
    public static final String CACHE_DELETING_ERROR_MESSAGE = "exception while deleting to Redis {}";
    public static final String CONNECTING_ERROR_MESSAGE = "exception while connecting to Redis {}";

    public static final long PWA_PROFILE_WEALTH_SERVICE_TTL = 3600;
}
