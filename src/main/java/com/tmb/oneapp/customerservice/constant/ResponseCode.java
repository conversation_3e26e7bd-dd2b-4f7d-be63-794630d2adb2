package com.tmb.oneapp.customerservice.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;

/**
 * enum class for response code to maintain response status
 *
 *
 */
@Getter
@AllArgsConstructor
public enum ResponseCode implements Serializable {

	SUCCESS("0000", "success", Constants.SERVICE_NAME, "success"),
	FAILED("0001", "failed", Constants.SERVICE_NAME, "failed"),
	BAD_REQUEST("400", "BAD REQUEST", Constants.SERVICE_NAME, "BAD REQUEST"),
	KEY_NOT_FOUND("404", "not found", Constants.SERVICE_NAME, "not found"),
	ETE_SERVICE_ERROR("0005", "ete service unavailable", Constants.SERVICE_NAME, "ete service unavailable"),
	ETE_5002_SERVICE_ERROR("0005", "5002 Contact Identifier is missing or invalid", Constants.SERVICE_NAME, "update promptpay request is invalid"),
	DATA_NOT_FOUND("0002", CustomerServiceConstant.DATA_NOT_FOUND, Constants.SERVICE_NAME,
			CustomerServiceConstant.DATA_NOT_FOUND),
	DATA_NOT_FOUND_V2("100006", CustomerServiceConstant.DATA_NOT_FOUND, Constants.SERVICE_NAME,
			CustomerServiceConstant.DATA_NOT_FOUND),
	DATA_NOT_FOUND_ERROR("0009", CustomerServiceConstant.DATA_NOT_FOUND, Constants.SERVICE_NAME,
			CustomerServiceConstant.DATA_NOT_FOUND),
	INVALID_DATA_ERROR("0010", CustomerServiceConstant.INVALID_REQUEST, Constants.SERVICE_NAME, CustomerServiceConstant.INVALID_REQUEST),
	INVALID_REQUEST_V2("100004", "Bad request", Constants.SERVICE_NAME, "Bad request"),
	CUSTOMER_NOT_FOUND("1000007", "customer not found", Constants.SERVICE_NAME, "customer not found"),
	DATABASE_CONNECTION_ERROR("0006", "database connection errror", Constants.SERVICE_NAME,
			"database connection errror"),
	UBO_DATA_NOT_FOUND("4002", CustomerServiceConstant.DATA_NOT_FOUND, Constants.SERVICE_NAME,

			CustomerServiceConstant.DATA_NOT_FOUND),
	GENERAL_ERROR("0001", "general error", Constants.SERVICE_NAME, "unknown error"),
	UNABLE_FETCH_DATA_COMMON_CONFIG("0001", "Unable to fetch data from commmon config", Constants.SERVICE_NAME,
			"Unable to fetch data from commmon config"),
	DOPA_FAILED_3TIMES("0003", "DOPA Failed 3 times ", Constants.SERVICE_NAME, "DOPA Failed 3 times"),
	CUSTOMER_PROSPECT_DATA_ERROR("0000", "unable to customer prospect data", Constants.SERVICE_NAME,
			"unable to customer proespect data"),
	CUSTOMER_PROSPECT_DATA_UPDATE_ERROR("0001", "unable to update customer prospect data", Constants.SERVICE_NAME,
			"unable to update customer proespect data"),
	CUSTOMER_PROSPECT_DATA_EXPIRED("0000", "Customer Prospect Data Expired", Constants.SERVICE_NAME,
			"Customer Prospect Data Expired"),
	CUSTOMER_EKYC_AGE_VERFICATION(
			"0002", "eKYC Age Verfication Error", Constants.SERVICE_NAME, "eKYC Age Verfication Error"),
	COMMON_CID_COMPARE_INCORRECT(
			"0006", "Citizen Id is Incorrect", Constants.SERVICE_NAME, "Citizen Id is Incorrect"),
	ECAS_ERROR("ecas_0001", "Ecas Error", Constants.SERVICE_NAME, "Ecas Error"),
	ERROR_UPDATE_CONSTENT("update_constent_0001", "Update Constent Error", Constants.SERVICE_NAME,
			"Update Constent Error"),
	CACHE_SERVICE_ERROR("0007", "Cache Service Error", Constants.SERVICE_NAME, "Cache Service Error"),
	CUSTOMER_EKYC_FLOW_STATE_WRONG("ekyc_9003", "flow state is wrong.", Constants.SERVICE_NAME, "flow state is wrong."),
	CIRCUIT_BREAKER_ERROR("0014", "Circuit break error", Constants.SERVICE_NAME, "Circuit break error"),

	DEPOSIT_CUSTOMER_GET_FATCA(
			"fatca_error_0404", "fatca is not found", Constants.SERVICE_NAME, "fatca is not found"),
	INVALID_CAMPAIGN_REQUEST_BODY("MY_BNF_001", "Invalid campaign request body", Constants.SERVICE_NAME, "Invalid campaign request body"),

	COMMON_FR_FAILED("8000", "Fail",
			Constants.SERVICE_NAME, "Fail"),
	COMMON_FR_GENERIC_ERROR("8000", "Common Face Recognition generic error",
			Constants.SERVICE_NAME, "Common Face Recognition generic error"),
	COMMON_FR_NOT_FOUND_REF_KEY_ERROR("8000", "Not found ref key",
			Constants.SERVICE_NAME, "Not found ref key"),
	COMMON_FR_INVALID_IMAGE_ERROR("8000", "Invalid image",
			Constants.SERVICE_NAME, "Invalid image"),
	COMMON_FR_INVALID_FACE_COMPARE_ERROR("8000", "Invalid face compare attempt",
			Constants.SERVICE_NAME, "Invalid face compare attempt"),
	COMMON_FR_IAL_NOT_ALLOWED_ERROR("8000", "IAL not allowed.",
			Constants.SERVICE_NAME, "IAL not allowed."),
	COMMON_FR_FAILED_VALIDATE_FACE_MATCHING_MAX_ATTEMPT("8218", "Failed validate face matching (Max-Attempt)",
			Constants.SERVICE_NAME, "Failed validate face matching (Max-Attempt)"),
	COMMON_FR_SERVICE_OFF_ERROR("8208", "Face Recognition service turn off",
			Constants.SERVICE_NAME, "Face Recognition service turn off"),
	FACE_RECOGNITION_OFF_ERROR("8209", Constants.CUSTOMER_TURN_OFF_FR,
			Constants.SERVICE_NAME, Constants.CUSTOMER_TURN_OFF_FR),
	FACE_RECOGNITION_CUSTOMER_BLIND_OR_ABROAD_ERROR("8209", "Customer Abroad, blind or normal customer with OFF flag",
			Constants.SERVICE_NAME, Constants.CUSTOMER_TURN_OFF_FR),
	COMMON_FR_IMAGE_NOT_EXIST("8210", "Not allow Face Recognition",
			Constants.SERVICE_NAME, "Not allow Face Recognition"),
	COMMON_FR_IMAGE_NOT_FOUND("8210", "Image not found",
			Constants.SERVICE_NAME, "Image not found"),
	COMMON_FR_CONSENT_FAILED("8211", "Get consent failed",
			Constants.SERVICE_NAME, "Get consent failed"),
	COMMON_FR_FOREIGN_CUSTOMER_ERROR("8213", "Customer type not allow in Face Recognition",
			Constants.SERVICE_NAME, "Customer type not allow in Face Recognition"),
	COMMON_FR_CUSTOMER_BLACKLIST_ERROR("8215", "Blacklist customer",
			Constants.SERVICE_NAME, "Blacklist customer"),
	COMMON_FR_CUSTOMER_INACTIVE_ERROR("8216", "Inactive customer",
			Constants.SERVICE_NAME, "Inactive customer"),
	FEATURE_NOT_ALLOW_FACE_RECOGNITION("8217", "Feature not allow face recognition %s",
			Constants.SERVICE_NAME, "Feature not allow face recognition %s"),
	COMMON_FR_FAILED_VALIDATE_FACE_MATCHING("8201", "Failed validate face matching",
			Constants.SERVICE_NAME, "Failed validate face matching"),

	MONGO_ERROR("0100", "Mongo error", Constants.SERVICE_NAME,"Mongo error"),
    NOT_FOUND_CUSTOMER_INFORMATION("0002", Constants.DATA_NOT_FOUND, Constants.SERVICE_NAME, "Data not found from table: CUST_DEVICE_INFORMATION"),
    NOT_FOUND_CUSTOMER_PROFILE("0003", Constants.DATA_NOT_FOUND, Constants.SERVICE_NAME, "Data not found from table: CRM_CUST_PROFILE"),
	INTERNAL_SERVER_ERROR("0001", "Internal error", Constants.SERVICE_NAME, "Cannot connect to oracle database"),
    INVALID_REQUEST_FIELDS("0004", Constants.INVALID_REQUEST, Constants.SERVICE_NAME, Constants.INVALID_REQUEST),
	DATA_NOT_FOUND_IN_DB("0005", Constants.DATA_NOT_FOUND, Constants.SERVICE_NAME, "Data not found from table: CUST_DEVICE_INFORMATION JOIN CRM_CUST_PROFILE"),
	PIN_VALIDATION_ERROR("transfer_003", "Pin Validation Error", Constants.SERVICE_NAME, "Pin Validation Error"),
	ERROR_8299("8299", "", Constants.SERVICE_NAME,""),
	ERROR_8200("8200", "", Constants.SERVICE_NAME,""),
	ERROR_8201("8201", "", Constants.SERVICE_NAME,""),
	ERROR_8202("8202", "Data Not Found", Constants.SERVICE_NAME,null),
	ERROR_8203("8203", "", Constants.SERVICE_NAME,""),
	COMMON_AUTH_VALIDATE_RULE_ERROR("8000", "Internal server error",
			Constants.SERVICE_NAME, "Validate rule error see log for detail"),
	INVALID_REQUEST_CAL_RISK("8100", Constants.INVALID_REQUEST, Constants.SERVICE_NAME, "Missing mandatory field"),
	ERROR_8001("8001", "%s : must not to be null", Constants.SERVICE_NAME, null),
	ERROR_8000("8000", "%s : must not to be null", Constants.SERVICE_NAME, null),
	INVALID_REQUEST("0010", "Invalid request", Constants.SERVICE_NAME, "Invalid request"),
	;

	private final String code;
	private final String message;
	private final String service;
	private final String desc;

	private static class Constants {
		public static final String SERVICE_NAME = "customers-service";
		public static final String DATA_NOT_FOUND = "Data not found";
		public static final String INVALID_REQUEST = "Invalid Request";
		public static final String CUSTOMER_TURN_OFF_FR = "Customer turn off FR";
	}
}