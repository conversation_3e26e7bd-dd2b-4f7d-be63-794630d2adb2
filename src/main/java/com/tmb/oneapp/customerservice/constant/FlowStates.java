package com.tmb.oneapp.customerservice.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import org.apache.commons.lang3.StringUtils;

public enum FlowStates {
    SAVE_PROSPECT("SAVE_PROSPECT"),
    OTP_PASS("OTP_PASS"),
    SAVE_ADDRESS_INFO("SAVE_ADDRESS_INFO"),
    ACCEPT_TANDC_NDID("ACCEPT_TANDC_NDID"),
    SEND_NDID_REQUEST("SEND_NDID_REQUEST"),
    VERIFY_NDID_SUCCESS("VERIFY_NDID_SUCCESS"),
    CAL_RISK_PASS("CAL_RISK_PASS"),
    CAL_RISK_FAIL("CAL_RISK_FAIL"),
    OPEN_ACCOUNT_NDID_SUCCESS("OPEN_ACCOUNT_NDID_SUCCESS"),
    CAL_RISK_PASS_BY7ELEVENFLOW("CAL_RISK_PASS_BY7ELEVENFLOW"),
    CAL_RISK_FAIL_BY7ELEVENFLOW("CAL_RISK_FAIL_BY7ELEVENFLOW"),
    SEND_REQUEST_7ELEVEN("SEND_REQUEST_7ELEVEN"),
    OPEN_ACCOUNT_7ELEVEN_SUCCESS("OPEN_ACCOUNT_7ELEVEN_SUCCESS"),
    VERIFY_7ELEVEN_SUCCESS("VERIFY_7ELEVEN_SUCCESS"),
    SAVE_PERSONAL_INFO_BY7ELEVENFLOW("SAVE_PERSONAL_INFO_BY7ELEVENFLOW"),
    EMPTY(""),
    DASH("-"),
    OPEN_NEW_DEPT_SEND_TO_NDID("OPEN_NEW_DEPT_SEND_TO_NDID"),
    OPEN_NEW_DEPT_ACCEPT_TANDC_NDID("OPEN_NEW_DEPT_ACCEPT_TANDC_NDID"),
    OPEN_NEW_DEPT_VERIFY_NDID_SUCCESS("OPEN_NEW_DEPT_VERIFY_NDID_SUCCESS"),
    OPEN_NEW_DEPT_CHANGE_METHOD("OPEN_NEW_DEPT_CHANGE_METHOD"),
    OPEN_NEW_DEPT_OPEN_ACCOUNT_NDID_SUCCESS("OPEN_NEW_DEPT_OPEN_ACCOUNT_NDID_SUCCESS"),
    OPEN_NEW_DEPT_SAVE_AND_SEND_TO_7ELEVEN("OPEN_NEW_DEPT_SAVE_AND_SEND_TO_7ELEVEN"),
    OPEN_NEW_DEPT_VERIFY_7ELEVEN_SUCCESS("OPEN_NEW_DEPT_VERIFY_7ELEVEN_SUCCESS"),
    ACCEPT_TANDC_AIS_NDID("ACCEPT_TANDC_AIS_NDID"),
    SAVE_PERSONAL_INFO_BYAISFLOW("SAVE_PERSONAL_INFO_BYAISFLOW"),
    CAL_RISK_PASS_BYAISFLOW("CAL_RISK_PASS_BYAISFLOW"),
    CAL_RISK_FAIL_BYAISFLOW("CAL_RISK_FAIL_BYAISFLOW"),
    SEND_REQUEST_AIS("SEND_REQUEST_AIS"),
    VERIFY_BY_AIS_SUCCESS("VERIFY_BY_AIS_SUCCESS"),
    OPEN_ACCOUNT_AIS_SUCCESS("OPEN_ACCOUNT_AIS_SUCCESS"),
    OPEN_NEW_DEPT_SAVE_AND_SEND_TO_AIS("OPEN_NEW_DEPT_SAVE_AND_SEND_TO_AIS"),
    OPEN_NEW_DEPT_VERIFY_AIS_SUCCESS("OPEN_NEW_DEPT_VERIFY_AIS_SUCCESS"),
    COMMON_SELECT_METHOD("COMMON_SELECT_METHOD"),
    COMMON_CHANGE_METHOD("COMMON_CHANGE_METHOD"),
    COMMON_ACCEPT_TANDC_NDID("COMMON_ACCEPT_TANDC_NDID"),
    COMMON_SEND_REQUEST_NDID("COMMON_SEND_REQUEST_NDID"),
    COMMON_VERIFY_NDID_SUCCESS("COMMON_VERIFY_NDID_SUCCESS"),
    COMMON_SEND_REQUEST_TO_7ELEVEN("COMMON_SEND_REQUEST_TO_7ELEVEN"),
    COMMON_VERIFY_7ELEVEN_SUCCESS("COMMON_VERIFY_7ELEVEN_SUCCESS"),
    COMMON_ACCEPT_TANDC_AIS_NDID("COMMON_ACCEPT_TANDC_AIS_NDID"),
    COMMON_SEND_REQUEST_AIS("COMMON_SEND_REQUEST_AIS"),
    COMMON_VERIFY_AIS_SUCCESS("COMMON_VERIFY_AIS_SUCCESS"),
    COMMON_ACCEPT_TANDC_ATM_NDID("COMMON_ACCEPT_TANDC_ATM_NDID"),
    COMMON_SEND_REQUEST_ATM("COMMON_SEND_REQUEST_ATM"),
    COMMON_VERIFY_ATM_SUCCESS("COMMON_VERIFY_ATM_SUCCESS"),
    COMMON_ACCEPT_TANDC_7ELEVEN_NDID("COMMON_ACCEPT_TANDC_7ELEVEN_NDID");


    private final String flowstate;

    FlowStates(String flowstate) {
        this.flowstate = flowstate;
    }

    @Override
    public String toString() {
        return flowstate.toLowerCase();
    }

    @JsonCreator // This is the factory method and must be static
    public static FlowStates fromString(String string) {
        if(StringUtils.isBlank(string)){
            return FlowStates.EMPTY;
        } else if(DASH.flowstate.equals(string)){
            return FlowStates.DASH;
        } else {
            return valueOf(string);
        }
    }

    @JsonValue
    public String getFlowstate() {
        return flowstate;
    }

}