package com.tmb.oneapp.customerservice.constant;

import lombok.experimental.UtilityClass;

@UtilityClass
public class EventParameterConstant {

    public static final String EVENT_NAME = "event_name";
    public static final String REQUEST_FROM = "request_from";
    public static final String REQUEST_UID = "request_uid";
    public static final String SERVICE_NAME = "service_name";
    public static final String RESPONSE_STATUS_CODE = "response_status_code";
    public static final String RESPONSE_STATUS_DESCRIPTION = "response_status_description";
    public static final String RESPONSE_ADDITIONAL_STATUS = "response_additional_status";
    public static final String RESPONSE_MESSAGE = "response_message";
    public static final String RESPONSE_ERROR = "response_error";
    public static final String PHONE_TYPE = "phone_type";
    public static final String PHONE_SEQ = "phone_seq";


}
