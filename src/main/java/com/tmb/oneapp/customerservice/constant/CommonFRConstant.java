package com.tmb.oneapp.customerservice.constant;

import java.util.List;

public class CommonFRConstant {
    // Common FR Config
    public static final String IAL_VERSION_210 = "210";
    public static final String IAL_VERSION_220 = "220";
    public static final String IAL_VERSION_230 = "230";
    public static final String EKYC_ID_TYPE_AI = "AI";
    public static final String EKYC_ID_TYPE_OT = "OT";
    public static final String BIOMETRIC_METHOD_SELFIE = "selfie";
    public static final String BIOMETRIC_SOURCE_TMB = "TMB";
    public static final String CUSTOMER_TYPE_PASSPORT_CODE = "PP";
    public static final String CUSTOMER_TYPE_CID = "cid";
    public static final String CUSTOMER_TYPE_PASSPORT = "passport";
    public static final String DIPCHIP_STATUS_NO = "No";
    public static final String CONFIG_TYPE_COMMON = "common-fr-config";
    public static final String CONFIG_CODE_MINIMUM_IAL = "minimum-ial";
    public static final String CONFIG_CODE_TOGGLE_FLAG = "common-fr-toggle-flag";
    public static final String CONFIG_CODE_FIREBASE_FACE_MATCHING = "is-check-firebase-face-matching";
    public static final String CONFIG_CODE_ALLOW_CUSTOMER_FOREIGNER = "is-allow-customer-type-foreigner";
    public static final String CONFIG_CODE_SKIP_VERIFY_LIVENESS = "is-skip-verify-liveness";
    public static final String CONSENT_PAGE_NAVIGATION = "CONSENT";
    public static final String UPLIFT_PAGE_NAVIGATION = "UPLIFT";
    public static final String SECURITY_RECOMMENDED_PAGE_NAVIGATION = "SECURITY_RECOMMENDED";
    public static final String STATUS_YES = "Yes";
    public static final String STATUS_NO = "No";
    public static final String STATUS_ON = "ON";
    public static final String STATUS_OFF = "OFF";
    public static final String SPACE = " ";
    public static final String DASH = "-";
    public static final String COLON = ":";
    public static final String UNDERSCORE = "_";
    public static final String ACTIVITY_ID_100002015 = "100002015";
    public static final String ACTIVITY_ID_100002001 = "100002001";
    public static final String ACTIVITY_ID_100002002 = "100002002";
    public static final String ACTIVITY_ID_100002003 = "100002003";
    public static final String ACTIVITY_ID_100002009 = "100002009";
    public static final String ACTIVITY_ID_100002019 = "100002019";
    public static final String ACTIVITY_ID_100002010 = "100002010";
    public static final String ACTIVITY_ID_100002012 = "100002012";
    public static final String ACTIVITY_ID_100002013 = "100002013";
    public static final String ACTIVITY_ID_100002014 = "100002014";
    public static final String ACTIVITY_ID_100002020 = "100002020";
    public static final String TURN_ON = "Turn ON";
    public static final String TURN_OFF = "Turn OFF";
    public static final String INTERNAL_SERVICE_ERROR = "Internal Service Error";
    public static final String INTERNAL_SERVER_ERROR = "Internal server error";
    public static final String INVALID_FACE_COMPARE_ERROR = "8000 : Invalid face compare attempt (%s time%s)";
    //FR Reason for activity log
    public static final String IMAGE_NOT_FOUND = "Image not found";
    public static final String INSERT_FR_REF_ID_FAILURE = "Insert fr ref Id failure";
    public static final String FACE_RECOGNITION_SERVICE_TURN_OFF = "Face recognition service turn off";
    public static final String FAIL_TO_GET_CONFIG_COMMON_FR_FEATURE = "Fail to get config common FR feature";
    public static final String FEATURE_NOT_ALLOW_FACE_RECOGNITION = "Feature not allow face recognition";
    public static final String INVALID_FR_FLAG = "Invalid FR flag";
    public static final String FAIL_TO_GET_FR_FLAG = "Fail to get FR flag";
    public static final String FAIL_TO_GET_IMAGE_DIP_CHIP = "Fail to get image Dip Chip";
    public static final String INSERT_PERSONALIZE_FACE_RECOGNITION_FAILURE = "Insert PERSONALIZE_FACE_RECOGNITION failure";
    public static final String INVALID_CONFIG_AS_PER_CUSTOMER_STATUS = "Invalid config as per customer status";
    public static final String FAIL_TO_GET_AS_PER_CUSTOMER_STATUS = "Fail to get as per customer status";
    public static final String NOT_FOUND_CACHE_AND_FAIL_TO_GET_KYC_DATA = "Not found cache and fail to get KYC data";
    public static final String FAIL_TO_GET_KYC_DATA = "Fail to get KYC Data";
    public static final String FAIL_TO_GET_COMMON_FR_CONFIG = "Fail to get common FR config";
    public static final String FAIL_TO_GET_IMAGE = "Fail to get image";
    public static final String FAIL_TO_GET_CURRENT_CUSTOMER_STATUS = "Fail to get current customer status";
    public static final String IAL_NOT_ALLOWED = "iAL not allowed";

    public static final String CONFIRM_FR = "Confirm FR";
    public static final String CUSTOMER_STATUS_BLANK = "00";
    public static final String CUSTOMER_STATUS_APPLICANT = "01";
    public static final String CUSTOMER_STATUS_ACTIVE = "02";
    public static final String CUSTOMER_STATUS_TNX_LOCKED = "03";
    public static final String CUSTOMER_STATUS_OTP_LOCK = "04";
    public static final String CUSTOMER_STATUS_ACCESS_LOCKED = "05";
    public static final String CUSTOMER_STATUS_RESET_PASS = "06";
    public static final String CUSTOMER_STATUS_CANCELLED_BY_CUSTOMER = "07";
    public static final String CUSTOMER_STATUS_CANCELLED_BY_BANK = "08";
    public static final String CUSTOMER_STATUS_TEMPORARY_BLOCK = "09";
    public static final String CUSTOMER_STATUS_SUSPENDED_BY_BANK = "10";
    public static final String CUSTOMER_STATUS_TEMPORARY_SUSPENDED_BY_BANK = "11";
    public static final String CUSTOMER_STATUS_INACTIVE_CUSTOMER = "12";
    public static final String CUSTOMER_STATUS_REGISTERED = "13";
    public static final String CUSTOMER_STATUS_CLOSE = "14";
    public static final String CUSTOMER_STATUS_TYPE_BLACKLIST = "BLACKLIST";
    public static final String CUSTOMER_STATUS_TYPE_ACTIVE = "ACTIVE";
    public static final String CUSTOMER_STATUS_TYPE_INACTIVE = "INACTIVE";
    public static final List<String> BLACKLIST_CUSTOMER = List.of(CUSTOMER_STATUS_CANCELLED_BY_BANK, CUSTOMER_STATUS_TEMPORARY_BLOCK,
            CUSTOMER_STATUS_SUSPENDED_BY_BANK, CUSTOMER_STATUS_TEMPORARY_SUSPENDED_BY_BANK, CUSTOMER_STATUS_INACTIVE_CUSTOMER);
    public static final List<String> INACTIVE_CUSTOMER = List.of(CUSTOMER_STATUS_BLANK, CUSTOMER_STATUS_APPLICANT, CUSTOMER_STATUS_TNX_LOCKED,
            CUSTOMER_STATUS_OTP_LOCK, CUSTOMER_STATUS_ACCESS_LOCKED, CUSTOMER_STATUS_RESET_PASS, CUSTOMER_STATUS_CANCELLED_BY_CUSTOMER, CUSTOMER_STATUS_REGISTERED, CUSTOMER_STATUS_CLOSE);
    public static final String FOREIGN_CUSTOMER_TYPE_920 = "920";
    public static final String FILTER_TYPE_ALL = "ALL";
    public static final String FILTER_TYPE_SPECIFIC = "SPECIFIC";
    public static final List<String> FILTER_TYPE_LIST = List.of(FILTER_TYPE_ALL, FILTER_TYPE_SPECIFIC);
    public static final String ELIGIBLE_FACE_AUTHENTICATION_STATUS_YES = "Yes";
    public static final String ELIGIBLE_FACE_AUTHENTICATION_STATUS_NO = "No";
    public static final String PERSONALIZE_FACE_RECOGNITION_STATUS_Y = "Y";
    public static final String PERSONALIZE_FACE_RECOGNITION_STATUS_N = "N";
    public static final Integer DEFAULT_FLOW_ID = 9002;
    public static final String COMMON_FR_NAME = "Common FR";
    // Common FR Consent Baseline
    public static final String BIOMETRIC_CONSENT = "BIOMETRIC-CONSENT";
    // Common FR Face-matching Request
    public static final String CONFIG_CODE_MAXIMUM_FACE_MATCHING_FAIL = "maximum-face-matching-fail";
    public static final String CONFIG_CODE_MAXIMUM_BLOCK = "maximum-block";
    public static final String BIOMETRIC_FORMAT_PNG = "PNG";
    public static final String BIOMETRIC_LENGTH_17453 = "17453";
    public static final String CHANNEL_MIB = "MIB";
    public static final String VERIFICATION_METHOD_FACE = "face";
    public static final String FACIAL_COMPARISON_RESULT_MATCH = "Match";
    public static final String FACIAL_COMPARISON_RESULT_NOT_MATCH = "Not Match";
    public static final String ALLOW_TO_CONTINUE_YES = "Yes";
    public static final String ALLOW_TO_CONTINUE_NO = "No";
    public static final String ACTIVITY_ID_100002008 = "100002008";
    public static final String RESULT_ONE = "1";
    public static final String RESULT_ZERO = "0";
    public static final String RESULT_MINUS_ONE = "-1";
    public static final String ACTION_TYPE_OTHER = "OTHER";
    public static final String INCORRECT_PART_LOGGING_ATTEMPTS = "attempts";
    public static final String INCORRECT_PART_LOGGING_FACE_MATCHING_RESULT = "faceMatchingResult";
    public static final String VERIFY_FR_REDIS_PREFIX = "VERIFY_FR_";
    public static final String STATUS_FAIL = "Fail";
    public static final String STATUS_SUCCESS = "Success";
    public static final String VERIFY_FR_CORRECT = "Correct";
    public static final String VERIFY_FR_INCORRECT_NOT_FOUND = "Incorrect : Not Found";
    public static final String CUSTOMER_VERSION_ACTIVATE_POST_LOGIN = "Activate : Post-login";
    public static final String CUSTOMER_VERSION_PROSPECT = "Prospect";
    public static final String COMMON_FR_BLOCK_ID_PREFIX = "FR_BLOCK_%s";
    public static final String CUSTOMER_TYPE_VALUE_AS_PER_CUST = "AS_PER_CUST";
    public static final String CUSTOMER_TYPE_VALUE_Y = "Y";
    public static final String CUSTOMER_TYPE_VALUE_N = "N";
    public static final String LIVENESS_REF_KEY_BY_CRM_CACHE = "customer:common_fr:crm:%s:liveness_ref_id:%s";
    public static final String LIVENESS_REF_KEY_BY_TEMP_CACHE = "customer:common_fr:temp_id:%s:liveness_ref_id:%s";
    public static final String AUTH_PUBLIC_KEY = "auth-public-key";

    private CommonFRConstant() {
    }

}
