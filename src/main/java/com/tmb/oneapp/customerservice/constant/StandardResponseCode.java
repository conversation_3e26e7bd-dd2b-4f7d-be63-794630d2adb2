package com.tmb.oneapp.customerservice.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;

/**
 * enum class for response code to maintain response status
 */
@Getter
@AllArgsConstructor
public enum StandardResponseCode implements Serializable {

    SUCCESS("000000", "success", StandardResponseCode.Constants.SERVICE_NAME, "success"),
    FAILED("100001", "Generic error", StandardResponseCode.Constants.SERVICE_NAME, "Generic error"),
    ERROR_DATA_NOT_FOUND_100006("100006", StandardResponseCode.Constants.DATA_NOT_FOUND, StandardResponseCode.Constants.SERVICE_NAME, null),
    ERROR_DATABASE_100003("100003", Constants.DATABASE_ERROR,StandardResponseCode.Constants.SERVICE_NAME, Constants.DATABASE_ERROR);


    private final String code;
    private final String message;
    private final String service;
    private final String desc;

    private static class Constants {
        public static final String SERVICE_NAME = "customers-service";
        public static final String DATA_NOT_FOUND = "Data not found";
        public static final String DATABASE_ERROR = "Database error";
    }
}