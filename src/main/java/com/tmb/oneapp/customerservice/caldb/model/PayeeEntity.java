package com.tmb.oneapp.customerservice.caldb.model;

import lombok.Data;
import lombok.ToString;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import org.hibernate.annotations.Nationalized;

import java.util.Date;

@Entity
@Data
@ToString
@Table(name = "PAYEE")
public class PayeeEntity {
    @Id
    @Column(name = "PAYEE_ID")
    private Integer payeeId;
    @Column(name = "PAYEE_NAME")
    private String payeeName;
    @Column(name = "PAYMENT_METHOD_ID")
    private Integer paymentMethodId;
    @Column(name = "PAYMENT_CATEGORY")
    private String paymentCategory;
    @Column(name = "EXTERNAL_ACCT_ID")
    private String externalAcctId;
    @Column(name = "CUST_SERVICE_PROVIDER_NAME")
    private String custServiceProviderName;
    @Column(name = "CUST_PERMANENT_ID")
    private String custPermanentId;
    @Column(name = "PAYEE_NICKNAME")
    private String payeeNickname;
    @Column(name = "PAYEE_TYPE")
    private String payeeType;
    @Column(name = "ACCT_PAYABLE_ID")
    private String acctPayableId;
    @Column(name = "EXTERNAL_REF_TYPE")
    private String externalRefType;
    @Column(name = "PAYEE_ACCT_NAME")
    private String payeeAcctName;
    @Column(name = "EXTERNAL_REF_ID")
    private String externalRefId;
    @Column(name = "ACCT_SERVICE_PROVIDER_NM")
    private String acctServiceProviderNm;
    @Column(name = "BILLER_BUSINESS_NAME")
    private String billerBusinessName;
    @Column(name = "TO_ACCOUNT_TYPE")
    private String toAccountType;
    @Column(name = "TO_ACCOUNT_KEY")
    private String toAccountKey;
    @Column(name = "TO_ACCOUNT_CCODE")
    private String toAccountCode;
    @Column(name = "TO_BANK_ID_TYPE")
    private String toBankIdType;
    @Column(name = "TO_BANK_ID")
    private String toBankId;
    @Column(name = "TO_BANK_NAME")
    private String toBankName;
    @Column(name = "TO_BRANCH_ID")
    private String toBranchId;
    @Column(name = "TO_BRANCH_NAME")
    private String toBranchName;
    @Column(name = "REMITTANCE_NAME")
    private String remittanceName;
    @Column(name = "PAYEE_BILLER_ID")
    private String payeeBillerId;
    @Column(name = "INTERNAL_ACCT_ID")
    private String internalAcctId;
    @Column(name = "FROM_ACCT_TYPE")
    private String fromAcctType;
    @Column(name = "FROM_ACCT_KEY")
    private String fromAcctKey;
    @Column(name = "FROM_ACCT_CCODE")
    private String fromAcctCcode;
    @Column(name = "FROM_BANK_ID_TYPE")
    private String fromBankNameType;
    @Column(name = "FROM_BANK_ID")
    private String fromBankId;
    @Column(name = "FROM_BANK_NAME")
    private String fromBankName;
    @Column(name = "FROM_BRANCH_ID")
    private String fromBranchId;
    @Column(name = "FROM_BRANCH_NAME")
    private String fromBranchName;
    @Column(name = "CARD_TYPE")
    private String cardType;
    @Column(name = "CARD_EXPIRY_DATE")
    private Date cardExpiryDate;
    @Column(name = "CARD_VERIFICATION_DATA")
    private String cardVerificationData;
    @Column(name = "TRACK1_DATA")
    private String track1Data;
    @Column(name = "TRACK2_DATA")
    private String track2Data;
    @Column(name = "TRACK3_DATA")
    private String track3Data;

    @Nationalized
    @Column(name = "MEMO_TEXT", columnDefinition = "nvarchar")
    private String memoText;
    
    @Column(name = "CARD_EMBOSSED_NBR")
    private String cardEmbossedNbr;
    @Column(name = "CARD_SEQ_NBR")
    private String cardSeqNbr;
    @Column(name = "CARD_TECHNOLOGY")
    private String cardTechnology;
    @Column(name = "CARD_CUST_NAME")
    private String cardCustName;
    @Column(name = "CARD_SERVICE_PHONE_NBR")
    private String cardServicePhoneNbr;
    @Column(name = "REFERENCE_TYPE")
    private String referenceType;
    @Column(name = "REFERENCE_NUMBER")
    private String referenceNumber;
    @Column(name = "IS_DELETED")
    private Integer isDeleted;
    @Column(name = "DELETED_DATE")
    private Date deletedDate;
    @Column(name = "REFERENCE_NUMBER_2")
    private String referenceNumber2;
    @Column(name = "REFERENCE_NUMBER_3")
    private String referenceNumber3;
    @Column(name = "REFERENCE_NUMBER_4")
    private String referenceNumber4;
    @Column(name = "IS_COMMERCIAL")
    private String isCommercial;
    @Column(name = "CRM_ID")
    private String crmId;
    @Column(name = "BILLER_ID")
    private Integer billerId;
    @Column(name = "CREATED_DATE")
    private Date createdDate;
    @Column(name = "PAYEE_DELETED_BY")
    private String payeeDeletedBy;
    @Column(name = "HEART_FLAG")
    private String heartFlag;
    @Column(name = "PICTURE_ID")
    private String pictureId;
    @Column(name = "DISPLAY_ORDER")
    private Integer displayOrder;
    @Column(name = "BILLER_COMPCODE")
    private String billerCompcode;
    @Column(name = "FAVORITE_MEMO")
    private String favoriteMemo;
    @Column(name = "BILLER_GROUP_TYPE")
    private String billerGroupType;
    @Column(name = "FROM_ACCT_ID")
    private String fromAcctId;
}