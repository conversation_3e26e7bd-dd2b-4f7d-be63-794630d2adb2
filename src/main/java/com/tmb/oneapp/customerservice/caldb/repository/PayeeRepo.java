package com.tmb.oneapp.customerservice.caldb.repository;

import com.tmb.oneapp.customerservice.caldb.model.PayeeEntity;
import com.tmb.oneapp.customerservice.model.FavTopUpBillPayDetailsModel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import jakarta.transaction.Transactional;
import jakarta.validation.Valid;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface PayeeRepo extends JpaRepository<PayeeEntity, String> {
  @Query(value="Select * from Payee " +
          " where crm_id=:crmId " +
          " and biller_group_type=:billerGroupType " +
          " and ( 'true'=:includeSoftDeleted or (is_deleted is null or is_deleted=0) ) ",
          nativeQuery = true)
  List<PayeeEntity> findByCrmIdAndBillerGroupType( String crmId, String billerGroupType, String includeSoftDeleted );

  @Query(value="Select * from Payee " +
          " where crm_id=:crmId " +
          " and (is_deleted is null or is_deleted=0) ",
          nativeQuery = true)
  List<PayeeEntity> findByCrmId(@Param("crmId") String crmId);

  @Query(value="SELECT PAYEE_NICKNAME FROM PAYEE " +
          " where CRM_ID=:crmId " +
          " AND PAYEE_NICKNAME=:nickName " +
          " and (is_deleted is null or is_deleted=0) ",
          nativeQuery = true)
  String getFaNickNameforTopBill(@Param("crmId") String crmId, @Param("nickName") String nickName);

  @Query(value="SELECT PAYEE_NICKNAME FROM PAYEE " +
          " WHERE CRM_ID=:crmId " +
          " AND REFERENCE_NUMBER=:refnumber " +
          " AND BILLER_COMPCODE=:compcode " +
          " and (is_deleted is null or is_deleted=0) ",
          nativeQuery = true)
  String getFaNickNamefromRefCompcode(@Param("crmId") String crmId, @Param("refnumber") String refnumber, @Param("compcode") String compcode);

  @Query(value = "SELECT PAYEEID.nextval FROM dual", nativeQuery = true)
  int getPayeeIdSequence();

  @Query("SELECT new com.tmb.oneapp.customerservice.model.FavTopUpBillPayDetailsModel (d.payeeNickname, d.createdDate, d.heartFlag, d.billerCompcode, d.referenceNumber, d.referenceNumber2, d.billerId, d.memoText, d.pictureId) " +
          " FROM PayeeEntity d " +
          " WHERE d.crmId =?1 " +
          " AND d.payeeId = ?2 " +
          " AND ( d.isDeleted is null or d.isDeleted=0 ) "
  )
  FavTopUpBillPayDetailsModel getFavtopupbillpayDetails(@Valid String crmId, Integer payeeId);

  @Transactional
  @Modifying
  @Query(value = "update payee " +
          " set heart_flag='Y', display_order=:displayOrder " +
          " where crm_id = :crmId " +
          " and payee_id = :favoriteId " +
          " and biller_group_type =:billerGroupType " +
          " and ( is_deleted is null or is_deleted=0 ) ",
            nativeQuery = true)
    int pinHeartFlag(String crmId, String favoriteId, String billerGroupType, Integer displayOrder );

    @Transactional
    @Modifying
    @Query(value = "update payee " +
            " set heart_flag=null, display_order=null " +
            " where crm_id = :crmId " +
            " and payee_id = :favoriteId " +
            " and biller_group_type =:billerGroupType " +
            " and ( is_deleted is null or is_deleted=0 ) ",
            nativeQuery = true)
    int unpinHeartFlag(String crmId, String favoriteId, String billerGroupType);

  @Transactional
  @Modifying
  @Query(
          value="Update Payee set IS_DELETED=1, DELETED_DATE=(CURRENT_TIMESTAMP) where payee_id=:favoriteId ",
          nativeQuery = true
  )
  int softDeleteByPayeeId(Integer favoriteId);

  @Transactional
  @Modifying
  @Query(
          value="Update Payee set IS_DELETED=2, DELETED_DATE=(CURRENT_TIMESTAMP) where payee_id=:favoriteId ",
          nativeQuery = true
  )
  int expireByPayeeId(Integer favoriteId);

  @Transactional
  @Modifying
  @Query(
          value="Update Payee set IS_DELETED=2, DELETED_DATE=:deletedDate where payee_id=:favoriteId ",
          nativeQuery = true
  )
  int expireByPayeeId(Integer favoriteId, LocalDateTime deletedDate);

  @Query(value="select COALESCE(max(display_order),0) " +
          " from Payee " +
          " where crm_id=:crmId " +
          " and (is_deleted is null or is_deleted=0) ",
          nativeQuery = true)
  int getMaxDisplayOrder(String crmId);
  
  @Transactional
  @Modifying
  @Query(
          value="UPDATE Payee set PICTURE_ID=:pictureId where CRM_ID=:crmId and PAYEE_ID=:favoriteId " +
                  " and (is_deleted is null or is_deleted=0) ",
          nativeQuery = true
  )
  int updatePictureId(@Param("pictureId") String pictureId, @Param("crmId") String crmId, @Param("favoriteId") Long favoriteId );
}