package com.tmb.oneapp.customerservice.config;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.controller.v1.CustomersDigitalController;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;

@ControllerAdvice(assignableTypes = CustomersDigitalController.class)
public class InvalidRequestExceptionHandler {

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler({MethodArgumentNotValidException.class})
    public final ResponseEntity<TmbOneServiceResponse<Object>> handleModelError(MethodArgumentNotValidException ex, final HttpServletRequest request) {
        return getInvalidRequestResponse();
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler({MissingRequestHeaderException.class})
    public final ResponseEntity<TmbOneServiceResponse<Object>> handleHeaderError(MissingRequestHeaderException ex, final HttpServletRequest request) {
        return getInvalidRequestResponse();
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<TmbOneServiceResponse<Object>> handleJsonParseError(HttpMessageNotReadableException ex) {
        return getInvalidRequestResponse();
    }

    private ResponseEntity<TmbOneServiceResponse<Object>> getInvalidRequestResponse() {
        TmbOneServiceResponse<Object> oneServiceResponse = new TmbOneServiceResponse<>();
        oneServiceResponse.setStatus(new TmbStatus(ResponseCode.INVALID_REQUEST.getCode(), ResponseCode.INVALID_REQUEST.getMessage(),
                ResponseCode.INVALID_REQUEST.getService(), ResponseCode.INVALID_REQUEST.getMessage()));
        return ResponseEntity.badRequest().body(oneServiceResponse);
    }

}