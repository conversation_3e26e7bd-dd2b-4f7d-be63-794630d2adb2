package com.tmb.oneapp.customerservice.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.method.HandlerTypePredicate;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    @Override
    public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
        configurer.defaultContentType(MediaType.APPLICATION_JSON);
    }

    @Override
    public void configurePathMatch(PathMatchConfigurer configure) {

        configure.addPathPrefix("/v1/customers-service/", HandlerTypePredicate.forBasePackage("com.tmb.oneapp.customerservice.controller.v1"));
        configure.addPathPrefix("/v2/customers/", HandlerTypePredicate.forBasePackage("com.tmb.oneapp.customerservice.controller.v2"));
        configure.addPathPrefix("/apis", HandlerTypePredicate.forBasePackage("com.tmb.oneapp.customerservice.controller"));
    }
}
