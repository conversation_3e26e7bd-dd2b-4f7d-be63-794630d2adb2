package com.tmb.oneapp.customerservice.config;

import java.util.HashMap;
import javax.sql.DataSource;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;

/**
 * oneapp db configuration class
 *
 */
@Configuration
@EnableJpaRepositories(
		basePackages = "com.tmb.oneapp.customerservice.repository",
		entityManagerFactoryRef = "userEntityManager",
		transactionManagerRef = "userTransactionManager")
@EnableTransactionManagement
public class OneAppDbConfig {

	private final String hibernateDdl;
	private final String oneAppDriverName;
	private final String oneAppUrl;
	private final String oneAppUsername;
	private final String oneAppPassword;

	private final Integer hikariConnectionTimeout;
	private final Integer hikariIdleTimeout;
	private final Integer hikariMaxLifetime;
	private final Integer hikariMinimumIdle;
	private final Integer hikariMaximumPoolSize;
	private final String hikariConnectionTestQuery;

	public OneAppDbConfig(
			@Value("${hibernate.hbm2ddl.auto}") final String hibernateDdl,
			@Value("${oneapp.datasource.driver-class-name}") final String oneAppDriverName,
			@Value("${oneapp.datasource.url}") final String oneAppUrl,
			@Value("${oneapp.datasource.username}") final String oneAppUsername,
			@Value("${oneapp.datasource.password}") final String oneAppPassword,
			@Value("${spring.datasource.hikari.connection-timeout}") final Integer hikariConnectionTimeout,
			@Value("${spring.datasource.hikari.idle-timeout}") final Integer hikariIdleTimeout,
			@Value("${spring.datasource.hikari.max-lifetime}") final Integer hikariMaxLifetime,
			@Value("${spring.datasource.hikari.minimum-idle}") final Integer hikariMinimumIdle,
			@Value("${spring.datasource.hikari.maximum-pool-size}") final Integer hikariMaximumPoolSize,
			@Value("${spring.datasource.hikari.connection-test-query}") String hikariConnectionTestQuery) {
		this.hibernateDdl = hibernateDdl;
		this.oneAppDriverName = oneAppDriverName;
		this.oneAppUrl = oneAppUrl;
		this.oneAppUsername = oneAppUsername;
		this.oneAppPassword = oneAppPassword;
		this.hikariConnectionTimeout = hikariConnectionTimeout;
		this.hikariIdleTimeout = hikariIdleTimeout;
		this.hikariMaxLifetime = hikariMaxLifetime;
		this.hikariMinimumIdle = hikariMinimumIdle;
		this.hikariMaximumPoolSize = hikariMaximumPoolSize;
		this.hikariConnectionTestQuery = hikariConnectionTestQuery;
	}

	@Bean
	@Primary
	public LocalContainerEntityManagerFactoryBean userEntityManager() {
		LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
		em.setDataSource(userDataSource());
		em.setPackagesToScan("com.tmb.common.model","com.tmb.oneapp.customerservice.model");

		HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
		em.setJpaVendorAdapter(vendorAdapter);
		HashMap<String, Object> properties = new HashMap<>();
		properties.put(CustomerServiceConstant.HIBERNATE_DDL, hibernateDdl);
		em.setJpaPropertyMap(properties);
		return em;
	}

	@Primary
	@Bean
	public DataSource userDataSource() {
		HikariDataSource dataSource = new HikariDataSource();
		dataSource.setDriverClassName(oneAppDriverName);
		dataSource.setJdbcUrl(oneAppUrl);
		dataSource.setUsername(oneAppUsername);
		dataSource.setPassword(oneAppPassword);

		dataSource.setConnectionTimeout(hikariConnectionTimeout);
		dataSource.setIdleTimeout(hikariIdleTimeout);
		dataSource.setMaxLifetime(hikariMaxLifetime);
		dataSource.setMinimumIdle(hikariMinimumIdle);
		dataSource.setMaximumPoolSize(hikariMaximumPoolSize);
		dataSource.setConnectionTestQuery(hikariConnectionTestQuery);
		dataSource.setPoolName("ONEAPP_HIKARICP_CONNECTION_POOL");
		return dataSource;
	}

	@Primary
	@Bean
	public PlatformTransactionManager userTransactionManager() {
		JpaTransactionManager transactionManager = new JpaTransactionManager();
		transactionManager.setEntityManagerFactory(userEntityManager().getObject());
		return transactionManager;
	}

}
