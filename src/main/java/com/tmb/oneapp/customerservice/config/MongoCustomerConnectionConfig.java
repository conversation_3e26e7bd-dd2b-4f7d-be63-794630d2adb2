package com.tmb.oneapp.customerservice.config;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoClientFactoryBean;

import java.util.concurrent.TimeUnit;

@Configuration
public class MongoCustomerConnectionConfig {

    private static final Logger logger = LoggerFactory.getLogger(MongoCustomerConnectionConfig.class);

    @Value("${spring.data.mongodb.customer.uri}")
    private String mongodbUri;

    @Value("${spring.data.mongodb.customer.username}")
    private String username;

    @Value("${spring.data.mongodb.customer.password}")
    private String password;

    @Value("${spring.data.mongodb.customer.database}")
    private String databaseName;

    @Value("${mongo.maxidletime:600000}")
    private long maxIdleTime;

    @Value("${mongo.maxlifetime:3600000}")
    private long maxLifeTime;

    @Value("${mongo.minsize:1}")
    private int minSize;

    @Value("${mongo.maxsize:100}")
    private int maxSize;

    @Value("${mongo.connect.timeout:10000}")
    private int connectionTimeout;

    @Value("${mongo.read.timeout:15000}")
    private int readTimeout;

    @Value("${mongo.maintenanceFrequency:60000}")
    private int maintenanceFrequency;

    @Value("mongodb://")
    private String mongoDbPrefix;


    public MongoClientSettings getMongoSetting() {
        String uri = mongodbUri;

        logger.info("initial mongodb connection : {}", uri);
        logger.info("initial mongo maxidletime : {}", maxIdleTime);
        logger.info("initial mongo maxlifetime : {}", maxLifeTime);
        logger.info("initial mongo minsize : {}", minSize);
        logger.info("initial mongo maxsize : {}", maxSize);
        logger.info("initial mongo connect timeout : {}", connectionTimeout);
        logger.info("initial mongo read timeout : {}", readTimeout);
        logger.info("initial mongo maintenance frequency : {}", maintenanceFrequency);

        if (!mongodbUri.contains("@")) {
            uri = mongodbUri.replace(mongoDbPrefix, (new StringBuilder(mongoDbPrefix)).append(username).append(":")
                    .append(password).append("@").toString());
        }
        return MongoClientSettings.builder()
                .applyToConnectionPoolSettings(pool -> pool
                        .maxConnectionIdleTime(maxIdleTime, TimeUnit.MILLISECONDS)
                        .maxConnectionLifeTime(maxLifeTime, TimeUnit.MILLISECONDS)
                        .minSize(minSize)
                        .maxSize(maxSize))
                .applyConnectionString(new ConnectionString(uri))
                .applyToSocketSettings(socket -> socket
                        .readTimeout(readTimeout, TimeUnit.MILLISECONDS)
                        .connectTimeout(connectionTimeout, TimeUnit.MILLISECONDS)).build();
    }

    public MongoClientFactoryBean customerClientFactoryBean() {
        MongoClientFactoryBean factoryBean = new MongoClientFactoryBean();
        String uri = mongodbUri;

        logger.info("initial mongodb connection : {}", uri);

        if (!mongodbUri.contains("@")) {
            uri = mongodbUri.replace(mongoDbPrefix, (new StringBuilder(mongoDbPrefix)).append(username).append(":")
                    .append(password).append("@").toString());
        }

        ConnectionString connectionString = new ConnectionString(uri);
        factoryBean.setConnectionString(connectionString);
        return factoryBean;
    }
}
