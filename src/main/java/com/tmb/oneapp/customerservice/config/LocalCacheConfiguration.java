package com.tmb.oneapp.customerservice.config;

import com.tmb.oneapp.customerservice.mongodb.customer.model.EKYCConfig;
import com.tmb.oneapp.customerservice.mongodb.customer.model.MyBenefitModule;
import org.ehcache.config.CacheConfiguration;
import org.ehcache.config.builders.CacheConfigurationBuilder;
import org.ehcache.config.builders.ExpiryPolicyBuilder;
import org.ehcache.config.builders.ResourcePoolsBuilder;
import org.ehcache.config.units.MemoryUnit;
import org.ehcache.jsr107.Eh107Configuration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.jcache.JCacheCacheManager;
import org.springframework.cache.jcache.JCacheManagerFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.cache.CacheManager;
import javax.cache.Caching;
import javax.cache.spi.CachingProvider;
import java.time.Duration;
import java.util.ArrayList;

@EnableCaching
@Configuration
public class LocalCacheConfiguration {
    @Value("${cache.ekycmodule.ttl}")
    private Integer ekycCacheTTL;

    @Value("${cacheable.evictionConfig.common_fr_face_recognition_feature.ttlSeconds}")
    private Integer commonFRFaceRecognitionFeatureTTL;

    @Value("${cacheable.evictionConfig.common_fr_config_config_type.ttlSeconds}")
    private Integer commonFRConfigTypeTTL;

    @Value("${cacheable.evictionConfig.common_fr_consent_baseline.ttlSeconds}")
    private Integer commonFRConsentBaselineTTL;

    @Value("${ttb.localcache.evictionConfig.my_benefit_module_config.ttlMillisecond}")
    private Integer myBenefitModuleConfigTTL;

    @Bean
    public JCacheManagerFactoryBean ehCacheCacheManager() {
        return new JCacheManagerFactoryBean();
    }

    @Primary
    @Bean(name = {"ehcache", "JCacheCacheManager"})
    public JCacheCacheManager ehCacheManager() {
        JCacheCacheManager cacheManager = new JCacheCacheManager();
        cacheManager.setCacheManager(cacheConfiguration());
        return cacheManager;
    }

    private CacheManager cacheConfiguration() {
        CachingProvider cachingProvider = Caching.getCachingProvider();
        CacheManager manager = cachingProvider.getCacheManager();

        manager.createCache("ekyc_module_config", ekycModuleConfiguration(ekycCacheTTL));
        manager.createCache("common-fr-feature", FaceRecognitionFeatureConfiguration(commonFRFaceRecognitionFeatureTTL));
        manager.createCache("common-fr-config-config-type", CommonFRConfigTypeConfiguration(commonFRConfigTypeTTL));
        manager.createCache("common-fr-consent-baseline", CommonFRConsentBaselineConfiguration(commonFRConsentBaselineTTL));
        manager.createCache("my_benefit_module_config", MyBenefitModuleConfigConfiguration(myBenefitModuleConfigTTL));
        return manager;
    }

    private javax.cache.configuration.Configuration<String, EKYCConfig> ekycModuleConfiguration(Integer cacheTTL) {
        CacheConfiguration<String, EKYCConfig> cacheConfig = CacheConfigurationBuilder
                .newCacheConfigurationBuilder(String.class,
                        EKYCConfig.class,
                        ResourcePoolsBuilder.newResourcePoolsBuilder()
                                .offheap(10, MemoryUnit.MB)
                                .build())
                .withExpiry(ExpiryPolicyBuilder.timeToLiveExpiration(Duration.ofSeconds(cacheTTL)))
                .build();
        return Eh107Configuration.fromEhcacheCacheConfiguration(cacheConfig);
    }

    private javax.cache.configuration.Configuration<String, ArrayList> FaceRecognitionFeatureConfiguration(Integer cacheTTL) {
        CacheConfiguration<String, ArrayList> cacheConfig = CacheConfigurationBuilder
                .newCacheConfigurationBuilder(String.class,
                        ArrayList.class,
                        ResourcePoolsBuilder.newResourcePoolsBuilder()
                                .offheap(10, MemoryUnit.MB)
                                .build())
                .withExpiry(ExpiryPolicyBuilder.timeToLiveExpiration(Duration.ofSeconds(cacheTTL)))
                .build();
        return Eh107Configuration.fromEhcacheCacheConfiguration(cacheConfig);
    }

    private javax.cache.configuration.Configuration<String, ArrayList> CommonFRConfigTypeConfiguration(Integer cacheTTL) {
        CacheConfiguration<String, ArrayList> cacheConfig = CacheConfigurationBuilder
                .newCacheConfigurationBuilder(String.class,
                        ArrayList.class,
                        ResourcePoolsBuilder.newResourcePoolsBuilder()
                                .offheap(10, MemoryUnit.MB)
                                .build())
                .withExpiry(ExpiryPolicyBuilder.timeToLiveExpiration(Duration.ofSeconds(cacheTTL)))
                .build();
        return Eh107Configuration.fromEhcacheCacheConfiguration(cacheConfig);
    }

    private javax.cache.configuration.Configuration<String, ArrayList> CommonFRConsentBaselineConfiguration(Integer cacheTTL) {
        CacheConfiguration<String, ArrayList> cacheConfig = CacheConfigurationBuilder
                .newCacheConfigurationBuilder(String.class,
                        ArrayList.class,
                        ResourcePoolsBuilder.newResourcePoolsBuilder()
                                .offheap(10, MemoryUnit.MB)
                                .build())
                .withExpiry(ExpiryPolicyBuilder.timeToLiveExpiration(Duration.ofSeconds(cacheTTL)))
                .build();
        return Eh107Configuration.fromEhcacheCacheConfiguration(cacheConfig);
    }

    private javax.cache.configuration.Configuration<String, MyBenefitModule> MyBenefitModuleConfigConfiguration(Integer cacheTTL) {
        CacheConfiguration<String, MyBenefitModule> cacheConfig = CacheConfigurationBuilder
                .newCacheConfigurationBuilder(String.class,
                        MyBenefitModule.class,
                        ResourcePoolsBuilder.newResourcePoolsBuilder()
                                .offheap(10, MemoryUnit.MB)
                                .build())
                .withExpiry(ExpiryPolicyBuilder.timeToIdleExpiration(Duration.ofSeconds(cacheTTL)))
                .build();
        return Eh107Configuration.fromEhcacheCacheConfiguration(cacheConfig);
    }
}
