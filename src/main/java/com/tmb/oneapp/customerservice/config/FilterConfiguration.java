package com.tmb.oneapp.customerservice.config;

import com.tmb.oneapp.bodydecryption.filter.BodyDecryptionFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FilterConfiguration {
    private final String[] bodyDecryptionEndpoints = {
        "/apis/customers/ekyc/prospect/update-NDIDConsent",
        "/apis/customers/ekyc/ndid/verifyBiometric"
    };

    @Bean
    public FilterRegistrationBean<BodyDecryptionFilter> bodyDecryptionFilterFilterRegistrationBean() {
        FilterRegistrationBean<BodyDecryptionFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(bodyDecryptionFilter());
        registrationBean.addUrlPatterns(bodyDecryptionEndpoints);
        return registrationBean;
    }

    @Bean
    public BodyDecryptionFilter bodyDecryptionFilter() {
        return new BodyDecryptionFilter();
    }
}
