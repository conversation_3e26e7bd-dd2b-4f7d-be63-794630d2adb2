package com.tmb.oneapp.customerservice.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.tmb.oneapp.customerservice.client.PDPAConsentFeignClient;

@Configuration
public class FeignConfig {

    @Autowired
    private ApplicationContext context;
    @Bean
    public PDPAConsentFeignClient PdpaEteClient(@Value("${ete.pdpa.consent.service.selector}") String qualifier) {
        if(qualifier==null){
            qualifier="real-pdpa-content-service";
        }
        return (PDPAConsentFeignClient) context.getBean(qualifier);
    }
}
