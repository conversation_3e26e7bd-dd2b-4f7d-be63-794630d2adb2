package com.tmb.oneapp.customerservice.config;

import jakarta.annotation.PostConstruct;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexOperations;

@Configuration
public class EnableMongoTTLConfig {

    public static final String TEMPORARY_CACHE_COLLECTION_NAME = "temporary_cache";
    public static final String EXPIRED_AT_PROPERTY_KEY = "expiredAt";
    private final MongoTemplate mongoTemplate;

    public EnableMongoTTLConfig(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @PostConstruct
    public void initExpiredAtIndexesDraftTransfer() {
        IndexOperations indexOps = mongoTemplate.indexOps(TEMPORARY_CACHE_COLLECTION_NAME);
        // Check if the index already exists
        boolean indexExists = indexOps.getIndexInfo().stream()
                .anyMatch(indexInfo -> indexInfo.getName().equals(EXPIRED_AT_PROPERTY_KEY.concat("_1")));
        if (!indexExists) {
            Index index = new Index().on(EXPIRED_AT_PROPERTY_KEY, Sort.Direction.ASC).expire(0L);
            indexOps.ensureIndex(index);
        }
    }

}