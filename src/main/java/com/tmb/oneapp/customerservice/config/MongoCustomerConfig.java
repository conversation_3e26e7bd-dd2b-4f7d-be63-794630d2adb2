package com.tmb.oneapp.customerservice.config;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.ttb.oneapp.oneappmongoclientlib.MongoConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

@Configuration
@EnableMongoAuditing
@EnableMongoRepositories(basePackages = "com.tmb.oneapp.customerservice.mongodb.customer", mongoTemplateRef = "customerMongoTemplate")
public class MongoCustomerConfig extends AbstractMongoClientConfiguration {

    @Value("${spring.data.mongodb.customer.database}")
    private String databaseName;


    private final MongoCustomerConnectionConfig mongoCustomerConnectionConfig;

    private final MongoConfig mongoConfig;

    @Autowired
    public MongoCustomerConfig(MongoCustomerConnectionConfig mongoCustomerConnectionConfig, MongoConfig mongoConfig) {
        this.mongoCustomerConnectionConfig = mongoCustomerConnectionConfig;
        this.mongoConfig = mongoConfig;
    }

    @Bean(name = "customerMongoTemplate")
    public MongoTemplate customerMongoTemplate() {
        return new MongoTemplate(mongoClient(), getDatabaseName());
    }

    @Bean
    public MongoConfig mandatoryConfig() {
        return mongoConfig;
    }

    @Override
    protected String getDatabaseName() {
        return databaseName;
    }

    @Override
    public MongoClient mongoClient() {
        return MongoClients.create(mongoCustomerConnectionConfig.getMongoSetting());
    }
}

