package com.tmb.oneapp.customerservice.config;

import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.customerservice.utils.MongoTemplateUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

@Configuration
@EnableMongoRepositories(basePackages =
        {"com.tmb.oneapp.customerservice.mongodb.common.repository"})
public class MongoCommonConfig {

    private static final TMBLogger<MongoCommonConfig> logger = new TMBLogger<>(MongoCommonConfig.class);

    @Value("${spring.data.mongodb.common.uri}")
    private String mongodbUri;

    @Value("${spring.data.mongodb.common.username}")
    private String username;

    @Value("${spring.data.mongodb.common.password}")
    private String password;

    @Value("${spring.data.mongodb.common.database}")
    private String databaseName;

    @Bean(value="commonMongoTemplate")
    public MongoTemplate getMongoTemplate() {
        return MongoTemplateUtil.getMongoTemplate(logger,mongodbUri,databaseName,username,password);
    }

}
