package com.tmb.oneapp.customerservice.config;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.util.HashMap;

/**
 * caldb configuration class
 *
 */
@Configuration
@EnableJpaRepositories(
		basePackages = {"com.tmb.oneapp.customerservice.caldb.repository", "com.tmb.oneapp.customerservice.caldb.model", "com.tmb.common.oneapp.caldb" },
		entityManagerFactoryRef = "caldbEntityManager",
		transactionManagerRef = "caldbTransactionManager")
public class CalDbConfig {

	private final String hibernateDdl;
	private final String caldbDriverName;
	private final String caldbUrl;
	private final String caldbUsername;
	private final String caldbPassword;

	private final Integer hikariConnectionTimeout;
	private final Integer hikariIdleTimeout;
	private final Integer hikariMaxLifetime;
	private final Integer hikariMinimumIdle;
	private final Integer hikariMaximumPoolSize;
	private final String hikariConnectionTestQuery;

	public CalDbConfig(
			@Value("${hibernate.hbm2ddl.auto}") final String hibernateDdl,
			@Value("${caldb.datasource.driver-class-name}") final String caldbDriverName,
			@Value("${caldb.datasource.url}") final String caldbUrl,
			@Value("${caldb.datasource.username}") final String caldbUsername,
			@Value("${caldb.datasource.password}") final String caldbPassword,
			@Value("${spring.datasource.hikari.connection-timeout}") final Integer hikariConnectionTimeout,
			@Value("${spring.datasource.hikari.idle-timeout}") final Integer hikariIdleTimeout,
			@Value("${spring.datasource.hikari.max-lifetime}") final Integer hikariMaxLifetime,
			@Value("${spring.datasource.hikari.minimum-idle}") final Integer hikariMinimumIdle,
			@Value("${spring.datasource.hikari.maximum-pool-size}") final Integer hikariMaximumPoolSize,
			@Value("${spring.datasource.hikari.connection-test-query}") String hikariConnectionTestQuery) {
		this.hibernateDdl = hibernateDdl;
		this.caldbDriverName = caldbDriverName;
		this.caldbUrl = caldbUrl;
		this.caldbUsername = caldbUsername;
		this.caldbPassword = caldbPassword;
		this.hikariConnectionTimeout = hikariConnectionTimeout;
		this.hikariIdleTimeout = hikariIdleTimeout;
		this.hikariMaxLifetime = hikariMaxLifetime;
		this.hikariMinimumIdle = hikariMinimumIdle;
		this.hikariMaximumPoolSize = hikariMaximumPoolSize;
		this.hikariConnectionTestQuery = hikariConnectionTestQuery;
	}

	@Bean
	public LocalContainerEntityManagerFactoryBean caldbEntityManager() {
		LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
		em.setDataSource(caldbDataSource());
		em.setPackagesToScan("com.tmb.oneapp.customerservice.caldb.model");

		HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
		em.setJpaVendorAdapter(vendorAdapter);
		HashMap<String, Object> properties = new HashMap<>();
		properties.put(CustomerServiceConstant.HIBERNATE_DDL, hibernateDdl);
		em.setJpaPropertyMap(properties);

		return em;
	}

	@Bean
	public DataSource caldbDataSource() {

		HikariDataSource dataSource = new HikariDataSource();
		dataSource.setDriverClassName(caldbDriverName);
		dataSource.setJdbcUrl(caldbUrl);
		dataSource.setUsername(caldbUsername);
		dataSource.setPassword(caldbPassword);

		dataSource.setConnectionTimeout(hikariConnectionTimeout);
		dataSource.setIdleTimeout(hikariIdleTimeout);
		dataSource.setMaxLifetime(hikariMaxLifetime);
		dataSource.setMinimumIdle(hikariMinimumIdle);
		dataSource.setMaximumPoolSize(hikariMaximumPoolSize);
		dataSource.setConnectionTestQuery(hikariConnectionTestQuery);
		dataSource.setPoolName("CALDB_HIKARICP_CONNECTION_POOL");

		return dataSource;
	}

	@Bean
	public PlatformTransactionManager caldbTransactionManager() {

		JpaTransactionManager transactionManager = new JpaTransactionManager();
		transactionManager.setEntityManagerFactory(caldbEntityManager().getObject());
		return transactionManager;
	}

}
