package com.tmb.oneapp.customerservice.config;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@OpenAPIDefinition
public class SwaggerConfig implements WebMvcConfigurer {

    @Value("${spring.application.name}")
    private String appName;

    @Value("${swagger.host:apis-portal.oneapp.tmbbank.local}")
    private String swaggerHost;

    @Value("${spring.application.description}")
    private String appDescription;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(apiInfo());
    }

    @Bean
    public GroupedOpenApi publicApi() {
        return GroupedOpenApi.builder()
                .group("oneapp-in-gw-service")
                .packagesToScan("com.tmb.oneapp.ingw")
                .pathsToMatch("/**")
                .build();
    }

    private Info apiInfo() {
        return new Info()
                .title(appName)
                .description(appDescription)
                .contact(apiContact());
    }

    private Contact apiContact() {
        return new Contact()
                .name("TMB Team");
    }
}