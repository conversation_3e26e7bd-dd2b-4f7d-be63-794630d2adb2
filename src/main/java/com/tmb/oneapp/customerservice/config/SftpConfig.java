package com.tmb.oneapp.customerservice.config;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import com.tmb.common.logger.TMBLogger;
import org.springframework.stereotype.Component;

import java.io.InputStream;

@Component
public class SftpConfig {

    private static final TMBLogger<SftpConfig> logger = new TMBLogger<>(SftpConfig.class);

    private ChannelSftp channelSftp;

    public void init(String username, String password, String remoteHost, Integer port) throws JSchException {
        try {
            JSch jsch = new JSch();
            Session jschSession = jsch.getSession(username, remoteHost, port);
            jschSession.setConfig("StrictHostKeyChecking", "no");
            jschSession.setPassword(password);
            jschSession.connect();
            this.channelSftp = (ChannelSftp) jschSession.openChannel("sftp");
        } catch (JSchException e) {
            logger.error("Cannot connect to SFTP server - ", e);
            throw e;
        }

    }

    public void connect() throws JSchException {
        try {
            channelSftp.connect();
        } catch (JSchException e) {
            logger.error("Cannot connect to SFTP server - ", e);
            throw e;
        }
    }

    public void disconnect() {
        channelSftp.disconnect();
    }

    public InputStream getFile(String src) throws SftpException {
        try {
            return channelSftp.get(src);
        } catch (SftpException e) {
            if (e.getMessage().contains("File not found")) {
                logger.error(String.format("File not found %s", src), e);
                return null;
            }

            throw e;
        }
    }
}
