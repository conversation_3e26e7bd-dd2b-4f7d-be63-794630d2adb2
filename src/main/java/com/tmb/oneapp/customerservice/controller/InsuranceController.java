package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.InsuranceCreateRequestResponse;
import com.tmb.oneapp.customerservice.model.InsuranceTaxInQuiryPolicyResponse;
import com.tmb.oneapp.customerservice.model.InsuranceTaxSubmitRequest;
import com.tmb.oneapp.customerservice.model.TaxYearRequest;
import com.tmb.oneapp.customerservice.service.InsuranceService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_PARAM_X_COR_ID_DEFAULT;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_PARAM_CRM_ID_DEFAULT;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_X_FORWARD_FOR;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.IS_FALSE;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.X_HEADER_CRMID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.X_HEADER_NO_PERIOD;
import static com.tmb.oneapp.customerservice.constant.ResponseCode.SUCCESS;

@RestController
@Tag(name = "Insurance Controller")
public class InsuranceController {
    private static final TMBLogger<InsuranceController> logger = new TMBLogger<>(InsuranceController.class);
    private final InsuranceService insuranceService;

    @Autowired
    public InsuranceController(InsuranceService insuranceService) {
        this.insuranceService = insuranceService;
    }

    @LogAround
    @PostMapping(value = "/customers/insurance/taxInquiry/policy")
    public ResponseEntity<TmbOneServiceResponse<InsuranceTaxInQuiryPolicyResponse>> insuranceGetTaxInquiryPolicy(
            @Parameter(description = HEADER_CORRELATION_ID, example =HEADER_PARAM_X_COR_ID_DEFAULT, required = true)
            @RequestHeader(HEADER_CORRELATION_ID) String correlationId,
            @Parameter(description = X_HEADER_CRMID, example =HEADER_PARAM_CRM_ID_DEFAULT, required = true)
            @RequestHeader(X_HEADER_CRMID) String crmID,
            @RequestBody TaxYearRequest taxYear,
            @Parameter(description = X_HEADER_NO_PERIOD, example =IS_FALSE, required = true)
            @RequestHeader(X_HEADER_NO_PERIOD) String noPeriod,
            @RequestHeader(HEADER_X_FORWARD_FOR) String ipAddress) {
        logger.info("insuranceGetTaxInquiryPolicy method start time {}", System.currentTimeMillis());
        TmbOneServiceResponse<InsuranceTaxInQuiryPolicyResponse> serviceResponse = new TmbOneServiceResponse<>();

        try {
            InsuranceTaxInQuiryPolicyResponse insuranceListResponse = insuranceService.
                    getTaxInquiryPolicy(crmID,
                            taxYear.getTaxYear(),
                            correlationId,
                            noPeriod,
                            ipAddress);
            serviceResponse.setData(insuranceListResponse);

            serviceResponse.setStatus(new TmbStatus(SUCCESS.getCode(), SUCCESS.getMessage(),
                    SUCCESS.getService(), SUCCESS.getDesc()));

            return ResponseEntity.ok().body(serviceResponse);
        } catch (Exception e) {
            serviceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(serviceResponse);
        }
    }

    @LogAround
    @PostMapping(value = "/customers/insurance/tax/create/request")
    public ResponseEntity<TmbOneServiceResponse<InsuranceCreateRequestResponse>> insuranceGetCreateRequest(
            @RequestBody InsuranceTaxSubmitRequest request
    ) {
        logger.info("insuranceGetCreateRequest method start time {}", System.currentTimeMillis());
        TmbOneServiceResponse<InsuranceCreateRequestResponse> serviceResponse = new TmbOneServiceResponse<>();

        try {
            InsuranceCreateRequestResponse insuranceSubmitRequest = insuranceService.getTaxCreateRequest(request);
            serviceResponse.setData(insuranceSubmitRequest);

            serviceResponse.setStatus(new TmbStatus(SUCCESS.getCode(), SUCCESS.getMessage(),
                    SUCCESS.getService(), SUCCESS.getDesc()));

            return ResponseEntity.ok().body(serviceResponse);
        } catch (Exception e) {
            serviceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(serviceResponse);
        }
    }
}