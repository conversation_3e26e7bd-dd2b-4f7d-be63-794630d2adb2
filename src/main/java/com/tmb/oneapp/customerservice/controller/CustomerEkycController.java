package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.CustomerEKYCProspectRequest;
import com.tmb.oneapp.customerservice.model.CustomerEKYCProspectResponse;
import com.tmb.oneapp.customerservice.model.CustomerEKYCServiceRequest;
import com.tmb.oneapp.customerservice.model.EKYCProspectGetResponse;
import com.tmb.oneapp.customerservice.model.EKYCProspectImageRequest;
import com.tmb.oneapp.customerservice.model.EKYCProspectUpdatingRequest;
import com.tmb.oneapp.customerservice.model.EKYCVerifyBioMetericRequest;
import com.tmb.oneapp.customerservice.model.EKYCVerifyBioMetricCustomerETEBiometricResult;
import com.tmb.oneapp.customerservice.model.FetchCustomerCitizenIDETERequest;
import com.tmb.oneapp.customerservice.model.FetchCustomerCitizenIDResponse;
import com.tmb.oneapp.customerservice.model.SearchProspectGetRequestBody;
import com.tmb.oneapp.customerservice.model.prospect.EKYCProspectSearchProspectCustomerDataResponse;
import com.tmb.oneapp.customerservice.service.CreateProspectDataInterface;
import com.tmb.oneapp.customerservice.service.CustomerEKYCSaveImageService;
import com.tmb.oneapp.customerservice.service.CustomerEKycProfileService;
import com.tmb.oneapp.customerservice.service.CustomerEKycService;
import com.tmb.oneapp.customerservice.service.CustomerEkycProspectService;
import com.tmb.oneapp.customerservice.service.DeleteCustomerProspectService;
import com.tmb.oneapp.customerservice.service.SearchCustomerProspectService;
import com.tmb.oneapp.customerservice.service.VerifyCustomerBiometricService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;
import com.tmb.oneapp.customerservice.utils.HMACSecurityUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.time.Instant;
import java.util.Map;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.CORRELATION_ID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_CITIZEN_ID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_FLOW_FIRST_DEPOSIT;

/**
 * Controller responsible for Customer EKYC
 */
@RestController
@Tag(name = "Customer EKYC Api")
public class CustomerEkycController {
    private static final TMBLogger<CustomerEkycController> logger = new TMBLogger<>(CustomerEkycController.class);
    private final CustomerEKycService customerEKycService;
    private final CustomerEKycProfileService customerEKycProfileService;
    private final CustomerEkycProspectService customerEKycProspectService;
    private final CustomerEKYCSaveImageService customerEkycSaveImageService;
    private final SearchCustomerProspectService searchCustomerProspectService;
    private final VerifyCustomerBiometricService verifyCustomerBiometricService;
    private final DeleteCustomerProspectService deleteCustomerProspectService;

    /**
     * Constructor
     */
    @Autowired
    public CustomerEkycController(CustomerEKycService customerEKycService,
                                  CustomerEKycProfileService customerEKycProfileService,
                                  CustomerEkycProspectService customerEKycProspectService,
                                  CustomerEKYCSaveImageService customerEkycSaveImageService,
                                  SearchCustomerProspectService searchCustomerProspectService,
                                  VerifyCustomerBiometricService verifyCustomerBiometricService,
                                  DeleteCustomerProspectService deleteCustomerProspectService) {
        this.customerEKycService = customerEKycService;
        this.customerEKycProfileService = customerEKycProfileService;
        this.customerEKycProspectService = customerEKycProspectService;
        this.customerEkycSaveImageService = customerEkycSaveImageService;
        this.searchCustomerProspectService = searchCustomerProspectService;
        this.verifyCustomerBiometricService = verifyCustomerBiometricService;
        this.deleteCustomerProspectService = deleteCustomerProspectService;
    }

    /**
     * Controller responsible for fetching citizen ID based on image
     */
    @Operation(summary = "Fetch Customer Citizen ID based on Image")
    @LogAround
    @PostMapping(value = "/customers/ekyc/scan")
    public ResponseEntity<TmbServiceResponse<FetchCustomerCitizenIDResponse>> getCustomerCitizenIDBasedOnImage(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Valid @RequestBody FetchCustomerCitizenIDETERequest reqBody) throws TMBCommonException {
        logger.debug(correlationId);
        logger.info("CustomerEkycController fetchCustomerCitizenID method start Time : {} ",
                System.currentTimeMillis());
        return callService("fetchCustomerCitizenID", reqBody, customerEKycService::fetchCustomerCitizenIdBasedOnImage);
    }

    @Operation(summary = "Validate Customer with DOPA & Get-Customer")
    @LogAround
    @PostMapping(value = "/customers/ekyc/profile/validation")
    public ResponseEntity<String> validateCustomerWithDOPAWithGetCustomer(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @RequestBody CustomerEKYCServiceRequest reqBody) throws TMBCommonException {
        logger.info("CustomerEkycController fetchCustomerProfileData method start Time : {} ",
                System.currentTimeMillis());
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        try {
            customerEKycProfileService.validateCustomerProfilefromDOPAWithGetCustomer(reqBody, correlationId);
            return ResponseEntity.ok().headers(responseHeaders).body(null);
        } catch (TMBCommonException ex) {
            logger.error("fetchCustomerProfileData  : {}", ex);
            throw ex;
        } catch (Exception e) {
            logger.error("CustomerEKYCValidation : Exception : {}", e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }

    }

    @Operation(summary = "Validate Customer with DOPA ")
    @LogAround
    @PostMapping(value = "/customers/profile/dopaCheck")
    public ResponseEntity<TmbServiceResponse<String>> validateCustomerWithDOPA(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @RequestBody CustomerEKYCServiceRequest reqBody) throws TMBCommonException {
        logger.debug("dopaCheck - correlationId: {}", correlationId);
        logger.info("CustomerEkycController fetchCustomerProfileData method start Time : {} ",
                System.currentTimeMillis());
        return callService("fetchCustomerProfileData", reqBody,
                customerEKycProfileService::validateCustomerProfilefromDOPA);
    }

    @Operation(summary = "Create Customer Prospect ID or Update Prospect Info")
    @LogAround
    @PostMapping(value = "/customers/ekyc/prospect/create-prospect")
    public ResponseEntity<TmbServiceResponse<CustomerEKYCProspectResponse>> createCustomerProspectData(
            @Parameter(description = CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader(CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Valid @RequestBody CustomerEKYCProspectRequest request) throws TMBCommonException {
        logger.debug("create-prospect - correlationId: {}", correlationId);
        logger.info("CustomerEkycController createCustomerProspectData method start Time : {} ",
                System.currentTimeMillis());
        return callService("createCustomerProspectData", request,
                customerEKycProspectService::checkAndSaveCustomerProspectData);

    }

    @Operation(summary = "Create Customer Prospect ID")
    @LogAround
    @PostMapping(value = "/customers/prospect/create-new-prospect")
    public ResponseEntity<TmbServiceResponse<CustomerEKYCProspectResponse>> postCreateNewCustomerProspect(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @RequestBody Map<String, Object> reqBody) throws TMBCommonException {
        logger.debug("create-new-prospect - correlationId: {}", correlationId);
        logger.info("CustomerEkycController postCreateNewCustomerProspect method start Time : {} ",
                System.currentTimeMillis());
        return callService("postCreateNewCustomerProspect",
                reqBody, customerEKycProspectService::postCreateNewProspect);
    }

    private <T, R> ResponseEntity<TmbServiceResponse<R>> callService(String serviceName, T request,
                                                                     CreateProspectDataInterface<T, R> function)
            throws TMBCommonException {
        TmbServiceResponse<R> response = new TmbServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        try {
            R data = function.apply(request);
            response.setData(data);
            response.setStatus(returnSuccessStatus());
        } catch (TMBCommonException error) {
            logger.error(serviceName + " : TMBCommonException : {}", error);
            throw error;
        } catch (Exception e) {
            logger.error(serviceName + " : Exception : {}", e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
        return ResponseEntity.ok().headers(responseHeaders).body(response);
    }

    @Operation(summary = "Update prospect data by prospect id.")
    @LogAround
    @PostMapping(value = "/customers/ekyc/prospect/update-prospect")
    public ResponseEntity<TmbServiceResponse<String>> updateCustomerProspectData(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Valid @RequestBody EKYCProspectUpdatingRequest reqBody) throws TMBCommonException {
        logger.debug("update-prospect - correlationId: {}", correlationId);
        logger.info("CustomerEkycController updateCustomerProspectData method start Time : {} ",
                System.currentTimeMillis());
        HttpHeaders responseHeaders = new HttpHeaders();
        TmbServiceResponse<String> oneServiceResponse = new TmbServiceResponse<>();
        responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        try {
            customerEKycProspectService.updateCustomerProspectData(reqBody);
            oneServiceResponse.setStatus(returnSuccessStatus());
            return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
        } catch (TMBCommonException err) {
            logger.error("updateCustomerProspectData : TMBCommonException : {}", err);
            throw err;
        }
    }

    @Operation(summary = "Get prospect data by prospect id.")
    @LogAround
    @GetMapping(value = "/customers/ekyc/prospect/get-prospect")
    public ResponseEntity<TmbServiceResponse<EKYCProspectSearchProspectCustomerDataResponse>> getCustomerProspectData(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "Prospect-ID", example ="P32088549737", required = true) @RequestHeader("X-Prospect-ID")
            @RequestHeaderNonNull String prospectId)
            throws TMBCommonException {
        logger.debug("get-prospect - correlationId: {}", correlationId);
        logger.info("CustomerEkycController getCustomerProspectData method start Time : {} ",
                System.currentTimeMillis());
        return callService("GetCustomerProspect", prospectId, customerEKycProspectService::getCustomerProspectData);
    }

    @Operation(summary = "Save prospect Image by Citizen id.")
    @LogAround
    @PostMapping(value = "/customers/ekyc/prospect/save-image")
    public ResponseEntity<TmbServiceResponse<String>> saveCustomerProspectImageData(
            @Parameter(description = "X-Correlation-ID", example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @RequestBody EKYCProspectImageRequest reqBody) throws TMBCommonException {
        logger.info("CustomerEkycController saveCustomerProspectImageData method start Time : {} ",
                System.currentTimeMillis());
        reqBody.setCorrelationId(correlationId);
        return callService("saveCustomerProspectImageData",
                reqBody, customerEkycSaveImageService::saveImageProspect);
    }

    @Operation(summary = "Search Prospect based on Device-ID.")
    @LogAround
    @GetMapping(value = "/customers/ekyc/search/prospect")
    public ResponseEntity<TmbServiceResponse<EKYCProspectGetResponse>> searchCustomerProspectBasedOnDeviceData(
            @Parameter(description = "X-Correlation-ID", example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "Device-ID", example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader("Device-Id") @RequestHeaderNonNull String deviceId,
            @Parameter(description = HEADER_FLOW_FIRST_DEPOSIT, example ="1234567890123")
            @RequestHeader(value = HEADER_FLOW_FIRST_DEPOSIT, required = false) String flow,
            @Parameter(description = HEADER_CITIZEN_ID, example ="1234567890123")
            @RequestHeader(value = HEADER_CITIZEN_ID, required = false) String citizenId)
            throws TMBCommonException {
        logger.debug("search-prospect - correlationId: {}", correlationId);
        logger.info("CustomerEkycController searchCustomerProspectBasedOnDeviceData method start Time : {} ",
                System.currentTimeMillis());
        SearchProspectGetRequestBody searchProspectReq = new SearchProspectGetRequestBody();
        searchProspectReq.setDeviceId(deviceId);
        searchProspectReq.setFlow(flow);
        searchProspectReq.setCitizenId(citizenId);
        return callService("searchCustomerProspectBasedOnDeviceData",
                searchProspectReq, searchCustomerProspectService::searchCustomerProspectBasedOnDeviceId);
    }

    @Operation(summary = "Search Customer Exit In TMB Previously Based on Citizen ID.")
    @LogAround
    @GetMapping(value = "/customers/ekyc/customerExist")
    public ResponseEntity<TmbServiceResponse<String>> checkCustomerBasedExistence(
            @Parameter(description = "X-Correlation-ID", example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "Citizen ID", required = true)
            @RequestHeader("X-Citizen-ID") @RequestHeaderNonNull String citizenId)
            throws TMBCommonException {
        logger.debug("customer exist - correlationId: {}", correlationId);
        logger.info("CustomerEkycController checkCustomerBasedExistence method start Time : {} ",
                System.currentTimeMillis());
        return callService("checkCustomerBasedExistence",
                citizenId, searchCustomerProspectService::validateCustomerExistOrNot);
    }

    @Operation(summary = "Delete CustomerProspect Data based on Prospect ID")
    @LogAround
    @DeleteMapping(value = "/customers/ekyc/prospect")
    public ResponseEntity<TmbServiceResponse<String>> deleteCustomerProspectData(
            @Parameter(description = "X-Correlation-ID", example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "Prospect ID", required = true)
            @RequestHeader("X-Prospect-ID") @RequestHeaderNonNull String prospectId)
            throws TMBCommonException {
        logger.info("CustomerEkycController deleteCustomerProspectData method start Time : {}, correlationId: {}",
                System.currentTimeMillis(), correlationId);
        HttpHeaders responseHeaders = new HttpHeaders();
        TmbServiceResponse<String> oneServiceResponse = new TmbServiceResponse<>();
        responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        try {
            deleteCustomerProspectService.deleteCustomerProspectData(prospectId);
            oneServiceResponse.setData(null);
            oneServiceResponse.setStatus(returnSuccessStatus());
            return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
        } catch (TMBCommonException error) {
            logger.error("deleteCustomerProspectData : TMBCommonException : {}", error);
            throw error;
        } catch (Exception e) {
            logger.error("deleteCustomerProspectData : Exception : {}", e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }

    @SuppressWarnings("SpellCheckingInspection")
    @DeleteMapping(value = "/customers/ekyc/prospect/citizenid")
    public ResponseEntity<TmbServiceResponse<String>> deleteCustomerProspectDataByCID(
            @Parameter(description = "X-Correlation-ID", example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "Card ID", example ="1111111111119", required = true)
            @RequestHeader("X-Citizen-ID") @RequestHeaderNonNull String citizenID)
            throws TMBCommonException {
        logger.info("CustomerEkycController deleteCustomerProspectDataByCID method start Time : {} , correlationId: {}",
                System.currentTimeMillis(), correlationId);
        HttpHeaders responseHeaders = new HttpHeaders();
        TmbServiceResponse<String> oneServiceResponse = new TmbServiceResponse<>();
        responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        try {
            for (EKYCProspectGetResponse prospect :
                    searchCustomerProspectService.searchCustomerProspectBasedOnCitizenID(citizenID)) {
                deleteCustomerProspectService.deleteCustomerProspectData(prospect.getProspectId());
            }
            oneServiceResponse.setData(null);
            oneServiceResponse.setStatus(returnSuccessStatus());
            return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
        } catch (TMBCommonException error) {
            logger.error("deleteCustomerProspectDataByCID : TMBCommonException : {}", error);
            throw error;
        } catch (Exception e) {
            logger.error("deleteCustomerProspectDataByCID : Exception : {}", e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }

    @Operation(summary = "Verify Biometric on Selfie")
    @LogAround
    @PostMapping(value = "/customers/ekyc/verify/biometric")
    public ResponseEntity<TmbServiceResponse<String>> verifyBioMetricOnSelfie(
            @Parameter(description = "X-Correlation-ID", example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
			@RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "X-Image-Enc", example ="5pQdE+RTplGl0jrNE5TBYlHxXGz21cq840tCI0om2wk=", required = true)
			@RequestHeader("X-Image-Enc") @RequestHeaderNonNull String imageEnc,
            @RequestBody EKYCVerifyBioMetericRequest reqBody) throws TMBCommonException {
        logger.info("CustomerEkycController verifyBioMetricOnSelfie method start Time : {} ",
                System.currentTimeMillis());
        HttpHeaders responseHeaders = new HttpHeaders();
        TmbServiceResponse<String> oneServiceResponse = new TmbServiceResponse<>();
        responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        try {
            if (!HMACSecurityUtil.checkImageByHMac(correlationId, imageEnc, reqBody.getSelfieData())) {
                throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                        ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
            }
            verifyCustomerBiometricService.verifyCustomerBiometric(correlationId, reqBody);
            oneServiceResponse.setData(null);
            oneServiceResponse.setStatus(returnSuccessStatus());
            return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
        } catch (TMBCommonException ex) {
            logger.error("verifyBioMetricOnSelfie : TMBCommonException : {}", ex);
            throw ex;
        } catch (Exception e) {
            logger.error("verifyBioMetricOnSelfie : Exception : {}", e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }

    @LogAround
    @SuppressWarnings("SpellCheckingInspection")
    @Operation(summary = "Verify Biometric at N.D.I.D service")
    @PostMapping(value = "/customers/ekyc/ndid/verifyBiometric")
    public ResponseEntity<TmbServiceResponse<EKYCVerifyBioMetricCustomerETEBiometricResult>> verifyBioMetric(
            @Parameter(description = "X-Correlation-ID", example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
			@RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @RequestBody EKYCVerifyBioMetericRequest reqBody) throws TMBCommonException {
        logger.info("CustomerEkycController verifyBioMetricOnSelfie method start Time : {} ",
                System.currentTimeMillis());
        HttpHeaders responseHeaders = new HttpHeaders();
        TmbServiceResponse<EKYCVerifyBioMetricCustomerETEBiometricResult> oneServiceResponse = new TmbServiceResponse<>();
        responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        try {
            oneServiceResponse.setData(verifyCustomerBiometricService
                    .verifyCustomerBiometricAtNDID(correlationId, reqBody));
            oneServiceResponse.setStatus(returnSuccessStatus());
            return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
        } catch (TMBCommonException ex) {
            logger.error("verifyBioMetricOnSelfie : TMBCommonException : {}", ex);
            throw ex;
        } catch (Exception e) {
            logger.error("verifyBioMetricOnSelfie : Exception : {}", e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }

    private Status returnSuccessStatus() {
        return new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(), CustomerServiceUtils.returnSuccessDescription());
    }

    @Operation(summary = "Search Prospect by Citizen-ID.")
    @LogAround
    @GetMapping(value = "/customers/search/prospect/citizen-id")
    public ResponseEntity<TmbServiceResponse<EKYCProspectGetResponse>> searchCustomerProspectByCitizenId(
            @Parameter(description = HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = HEADER_CITIZEN_ID, example ="1234567890123", required = true)
            @RequestHeader(HEADER_CITIZEN_ID) @RequestHeaderNonNull String citizenId)
            throws TMBCommonException {
        logger.debug("search prospect by citizen-id - correlationId: {}", correlationId);
        logger.info("searchCustomerProspectByCitizenId method start Time : {} ",
                System.currentTimeMillis());
        return callService("searchCustomerProspectByCitizenId", citizenId,
                searchCustomerProspectService::searchCustomerProspectByCitizenId);
    }

    @Operation(summary = "Verify Biometric at Open Second Account")
    @LogAround
    @PostMapping(value = "/customers/ekyc/openSecondAccount/verifyBiometric")
    public ResponseEntity<TmbServiceResponse<EKYCVerifyBioMetricCustomerETEBiometricResult>> verifyBioMetricOpenSecondAccount(
            @Parameter(description = "X-Correlation-ID", example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @RequestBody EKYCVerifyBioMetericRequest reqBody) throws TMBCommonException {
        logger.info("CustomerEkycController verifyBioMetricOpenSecondAccount method start Time : {} ",
                System.currentTimeMillis());
        HttpHeaders responseHeaders = new HttpHeaders();
        TmbServiceResponse<EKYCVerifyBioMetricCustomerETEBiometricResult> oneServiceResponse = new TmbServiceResponse<>();
        responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        try {
            oneServiceResponse.setData(verifyCustomerBiometricService
                    .verifyCustomerBiometricAtOpenSecondAccount(correlationId, reqBody));
            oneServiceResponse.setStatus(returnSuccessStatus());
            return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
        } catch (TMBCommonException ex) {
            logger.error("verifyBioMetricOpenSecondAccount : TMBCommonException : {}", ex);
            throw ex;
        } catch (Exception e) {
            logger.error("verifyBioMetricOpenSecondAccount : Exception : {}", e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, e);
        }
    }

}
