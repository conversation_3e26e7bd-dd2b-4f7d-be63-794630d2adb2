package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.controller.MyBenefitBaseController;
import com.tmb.oneapp.customerservice.model.MyBenefitEntryPointResponse;
import com.tmb.oneapp.customerservice.model.mybenefit.V1GetEntryPoint;
import com.tmb.oneapp.customerservice.model.mybenefit.V1GetEntryPointRequest;
import com.tmb.oneapp.customerservice.mongodb.customer.model.MyBenefitModule;
import com.tmb.oneapp.customerservice.mongodb.customer.model.MyBenefitModuleRequest;
import com.tmb.oneapp.customerservice.mongodb.customer.model.MyBenefitModuleResponse;
import com.tmb.oneapp.customerservice.service.MyBenefitModuleConfigService;
import com.tmb.oneapp.customerservice.service.MyBenefitService;
import feign.FeignException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.MY_BENEFIT_CONFIG_DATE_FORMAT;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.MY_BENEFIT_CONFIG_INCORRECT_PARAM_FORMAT;

@RequiredArgsConstructor
@RestController
public class V1MyBenefitController extends MyBenefitBaseController {
    private static final TMBLogger<V1MyBenefitController> logger = new TMBLogger<>(V1MyBenefitController.class);

    private final MyBenefitService myBenefitService;
    private final MyBenefitModuleConfigService myBenefitModuleConfigService;

    @LogAround
    @Operation(summary = "My Benefit get entry point")
    @PostMapping("/my-benefit/get-entry")
    public ResponseEntity<TmbOneServiceResponse<MyBenefitEntryPointResponse>> getEntryPoint(
            @Parameter(description = "CRMID", example = "001100000000000000000000092067", required = true) @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Accept Language", example = "th", required = true) @RequestHeader(name = "Accept-Language") String lang,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @RequestBody @Valid V1GetEntryPointRequest request
    ) throws TMBCommonException {
        try {
            V1GetEntryPoint req = getV1GetEntryPoint(crmId, lang, correlationId, request);
            MyBenefitEntryPointResponse response = myBenefitService.getEntryPointV1(req);

            TmbOneServiceResponse<MyBenefitEntryPointResponse> buildResponse = new TmbOneServiceResponse<>();
            buildResponse.setStatus(getResponseSuccess());
            buildResponse.setData(response);

            return ResponseEntity.ok().headers(getResponseHeaders()).body(buildResponse);
        } catch (FeignException e) {
            throw handleETEServiceException(e);
        } catch (Exception e) {
            throw genericTmbException(ResponseCode.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    @LogAround
    @Operation(summary = "Update my benefit config")
    @PostMapping("/my-benefit/my-benefit-config")
    public ResponseEntity<TmbOneServiceResponse<Object>> updateMyBenefitConfig(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @RequestBody @Valid MyBenefitModuleRequest request,
            BindingResult bindingResult
    ) throws TMBCommonException {
        try {
            logger.info("Start update my-benefit-config: {}", request);
            TmbOneServiceResponse<Object> response = new TmbOneServiceResponse<>();
            String errorMessage = validateMyBenefitConfigRequest(request, bindingResult);
            if (null != errorMessage) {
                logger.info("Request parameter error: {}", errorMessage);
                response = getObjectTmbOneServiceResponse(errorMessage);
                return ResponseEntity.ok().body(response);
            }
            MyBenefitModule result = myBenefitModuleConfigService.updateMyBenefitModuleConfig(request);
            response.setStatus(getResponseSuccess());
            logger.info("End update my-benefit-config: {}", result);
            return ResponseEntity.ok().body(response);
        } catch (Exception e) {
            logger.info("Failed to update my benefit config: {}", e);
            throw genericTmbException(ResponseCode.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    @LogAround
    @Operation(summary = "Get my benefit config")
    @GetMapping("/my-benefit/get-my-benefit-config")
    public ResponseEntity<TmbOneServiceResponse<MyBenefitModuleResponse>> getMyBenefitConfig(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId
    ) throws TMBCommonException {
        try {
            logger.info("Start get my-benefit-config");
            TmbOneServiceResponse<MyBenefitModuleResponse> response = new TmbOneServiceResponse<>();
            MyBenefitModule myBenefitModule = myBenefitModuleConfigService.getMyBenefitModuleConfig();
            MyBenefitModuleResponse myBenefitModuleResponse = mappedToMyBenefitModuleResponse(myBenefitModule);
            response.setStatus(getResponseSuccess());
            response.setData(myBenefitModuleResponse);
            logger.info("End get my-benefit-config");
            return ResponseEntity.ok().body(response);
        } catch (Exception e) {
            logger.info("Failed to get my benefit config: {}", e);
            throw genericTmbException(ResponseCode.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private static MyBenefitModuleResponse mappedToMyBenefitModuleResponse(MyBenefitModule config) {
        return new MyBenefitModuleResponse()
                .setUnavailablePlay(config.getUnavailablePlay())
                .setDisplayDateFromEN(config.getDisplayDateFromEN())
                .setUnavailableDateFrom(config.getUnavailableDateFrom())
                .setDisplayDateFromTH(config.getDisplayDateFromTH())
                .setDisplayDateToEN(config.getDisplayDateToEN())
                .setDisplayDateToTH(config.getDisplayDateToTH())
                .setUnavailableDateTo(config.getUnavailableDateTo())
                .setInApp(config.getInApp());
    }

    private static TmbOneServiceResponse<Object> getObjectTmbOneServiceResponse(String errorMessage) {
        TmbOneServiceResponse<Object> response = new TmbOneServiceResponse<>();
        TmbStatus status = new TmbStatus(
                ResponseCode.BAD_REQUEST.getCode(),
                "Request parameter error",
                ResponseCode.SUCCESS.getService(),
                errorMessage);
        response.setStatus(status);
        return response;
    }

    private String validateMyBenefitConfigRequest(MyBenefitModuleRequest config, BindingResult bindingResult) {
        List<String> errorMessages = bindingResult.getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.toList());
        errorMessages.addAll(validateDateFormats(config));
        return errorMessages.isEmpty() ? null : errorMessages.get(0);
    }

    private List<String> validateDateFormats(MyBenefitModuleRequest config) {
        List<String> dateFormatErrors = new ArrayList<>();

        String unavailableDateFromError = validateFormatDateMyBenefitConfig(
                config.getUnavailableDateFrom(),
                "unavailable_date_from"
        );
        if (unavailableDateFromError != null) {
            dateFormatErrors.add(unavailableDateFromError);
        }

        String unavailableDateToError = validateFormatDateMyBenefitConfig(
                config.getUnavailableDateTo(),
                "unavailable_date_to"
        );
        if (unavailableDateToError != null) {
            dateFormatErrors.add(unavailableDateToError);
        }

        return dateFormatErrors;
    }

    private String validateFormatDateMyBenefitConfig(String stringDate, String fieldName) {
        try {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(MY_BENEFIT_CONFIG_DATE_FORMAT);
            LocalDate.parse(stringDate, dateTimeFormatter);
            return null;
        } catch (Exception e) {
            return String.format(MY_BENEFIT_CONFIG_INCORRECT_PARAM_FORMAT, fieldName);
        }
    }

    private static TMBCommonException handleETEServiceException(FeignException e) {
        logger.error("FeignException::Failed to get entry point: {} - message {}", e, e.getMessage());
        return  new TMBCommonException(ResponseCode.ETE_SERVICE_ERROR.getCode(),
                ResponseCode.ETE_SERVICE_ERROR.getMessage() + "::" + e, ResponseCode.ETE_SERVICE_ERROR.getService(),
                HttpStatus.INTERNAL_SERVER_ERROR, null);
    }

    private static V1GetEntryPoint getV1GetEntryPoint(String crmId, String lang, String correlationId, V1GetEntryPointRequest request) {
        return new V1GetEntryPoint()
                .setCorrelationId(correlationId)
                .setLang(lang)
                .setCrmId(crmId)
                .setMenu(request.getMenu())
                .setSub(request.getSub());
    }
}
