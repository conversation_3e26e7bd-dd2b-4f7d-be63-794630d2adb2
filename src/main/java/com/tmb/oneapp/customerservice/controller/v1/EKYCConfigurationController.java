package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.mongodb.customer.model.EKYCConfig;
import com.tmb.oneapp.customerservice.service.EKYCConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import java.time.Instant;


import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_TIMESTAMP;

@RestController
public class EKYCConfigurationController {

    private static final TMBLogger<EKYCConfigurationController> logger = new TMBLogger<>(EKYCConfigurationController.class);

    private final EKYCConfigService ekycConfigService;

    public EKYCConfigurationController(EKYCConfigService ekycConfigService) {
        this.ekycConfigService = ekycConfigService;
    }

    @LogAround
    @GetMapping(value = {"/configuration/ekyc"})
    @Operation(summary = "Get EKYC Config by Module")
    public ResponseEntity<TmbOneServiceResponse<EKYCConfig>> getEKYCConfigByModule(
            @Parameter(description = "X-Correlation-ID", example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @RequestHeader("X-Correlation-ID") String correlationId ) throws TMBCommonException {

        TmbOneServiceResponse<EKYCConfig> response = new TmbOneServiceResponse<>();

        try {
            EKYCConfig ekycConfig = ekycConfigService.fetchEKYCConfiguration();
            response.setStatus(new TmbStatus(
                    ResponseCode.SUCCESS.getCode(),
                    ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(),
                    null));
            response.setData(ekycConfig);
        } catch (TMBCommonException e) {
            logger.error("Error in CommonConfigController : {} ", e);
            throw e;
        }

        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        return ResponseEntity.ok().headers(responseHeaders).body(response);
    }
}
