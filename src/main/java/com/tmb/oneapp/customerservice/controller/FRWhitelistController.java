package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.logger.TTBPayloadType;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.FRWhitelistResult;
import com.tmb.oneapp.customerservice.service.FRWhitelistService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@Tag(name = "Whitelist FR daily limit")
public class FRWhitelistController {
    private static final TMBLogger<FRWhitelistController> logger = new TMBLogger<>(FRWhitelistController.class);

    private final FRWhitelistService frWhitelistService;

    public FRWhitelistController(FRWhitelistService frWhitelistService) {
        this.frWhitelistService = frWhitelistService;
    }

    @LogAround
    @Operation(summary = "Get FR whitelist result based on CRM-ID")
    @GetMapping(value = "/customers/whitelist/fr-daily-limit", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<FRWhitelistResult>> getFrWhitelistResult(
            @Parameter(description = "CRMID", example ="001100000000000000000001184383", required = true)
            @RequestHeader(name = "X-CRMID", required = false) String crmId,
            @Parameter(description = "Correlation ID", example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @RequestHeader HttpHeaders headers) throws TMBCommonException {
        try {
            logger.payload(TTBPayloadType.INBOUND, headers, null);

            FRWhitelistResult frWhitelistResult = frWhitelistService.getFrWhitelistResult(crmId);

            TmbOneServiceResponse<FRWhitelistResult> response = new TmbOneServiceResponse<>();
            response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE,
                    CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMER_SERVICE));
            response.setData(frWhitelistResult);

            HttpHeaders responseHeader = TMBUtils.getResponseHeaders();
            logger.payload(TTBPayloadType.OUTBOUND, responseHeader, TMBUtils.convertJavaObjectToString(response));
            return ResponseEntity.ok().headers(responseHeader).body(response);
        } catch (Exception e) {
            logger.error("Error while checking fr whitelist for daily limit : {}", e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);
        }
    }
}




