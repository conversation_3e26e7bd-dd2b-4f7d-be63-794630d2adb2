package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.ekycverifybiometric.EkycVerifyBiometricRequest;
import com.tmb.oneapp.customerservice.model.ekycverifybiometric.EkycVerifyBiometricResponse;
import com.tmb.oneapp.customerservice.service.CustomerEkycBiometricCompareService2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

import java.util.Optional;

import static com.tmb.oneapp.customerservice.constant.CommonFRConstant.ACTION_TYPE_OTHER;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.ERROR_CODE_8200;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.ERROR_CODE_8299;

/**
 * Controller class responsible for Get Biometric Compare Data
 */
@RestController
@Tag(name = "Get Biometric Compare Data")
public class CustomerEkycBiometricCompareController2 {

    private static final TMBLogger<CustomerEkycBiometricCompareController2> logger = new TMBLogger<>(CustomerEkycBiometricCompareController2.class);
    private final CustomerEkycBiometricCompareService2 customerEkycBiometricCompareService;

    /**
     * Constructor
     *
     * @param customerEkycBiometricCompareService model
     */
    public CustomerEkycBiometricCompareController2(CustomerEkycBiometricCompareService2 customerEkycBiometricCompareService) {
        super();
        this.customerEkycBiometricCompareService = customerEkycBiometricCompareService;
    }

    /**
     * method : To call CustomerEkycBiometricGetService
     *
     * @param requestBody EkycBiometricGetRequestFormat
     * @return EkycBiometricCompareResponseFormat
     */
    @Operation(summary = "Get Ekyc Biometric Compare")
    @PostMapping(value = "/customers/v2/ekyc/biomatriccompare", produces = MediaType.APPLICATION_JSON_VALUE)
    @LogAround
    public ResponseEntity<TmbOneServiceResponse<EkycVerifyBiometricResponse>> getCustomerEkycBiometricCompare(
            @RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @Valid @RequestBody EkycVerifyBiometricRequest requestBody) {

        TmbOneServiceResponse<EkycVerifyBiometricResponse> ekycBiometricCompareResponse = new TmbOneServiceResponse<>();

        try {
            if (requestBody.getActionType() == null || requestBody.getActionType().isBlank()) {
                requestBody.setActionType(ACTION_TYPE_OTHER);
            }

            final EkycVerifyBiometricResponse ekycVerifyBiometricResponse = customerEkycBiometricCompareService.getCustomerEkycBiometricCompare2(correlationId, requestBody);

            ekycBiometricCompareResponse.setData(ekycVerifyBiometricResponse);
            ekycBiometricCompareResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(ekycBiometricCompareResponse);
        } catch (Exception e) {
            logger.error("Unable to getCustomerEkycBiometricCompare data : {} ", e);
            String errorCode = getErrorCode(e);
            ekycBiometricCompareResponse.setStatus(new TmbStatus(errorCode, ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(ekycBiometricCompareResponse);
        }
    }

    private static String getErrorCode(Exception e) {
        String error = Optional.ofNullable(e.getMessage()).orElse(ResponseCode.FAILED.getCode());
        return switch (error) {
            case ERROR_CODE_8299 -> ResponseCode.ERROR_8299.getCode();
            case ERROR_CODE_8200 -> ResponseCode.ERROR_8200.getCode();
            default -> ResponseCode.FAILED.getCode();
        };
    }

}
