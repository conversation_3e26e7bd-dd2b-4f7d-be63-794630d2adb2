package com.tmb.oneapp.customerservice.controller.v1;

import com.google.common.base.Strings;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.CustomerProfileStatus;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.service.DeviceNameStatusServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;


@RestController
/**
 * V1CustomerProfileController request mapping will handle apis call and then
 * navigate to respective method to get customer details
 *
 */
public class V1CustomerProfileController {
	private static final TMBLogger<V1CustomerProfileController> logger = new TMBLogger<>(V1CustomerProfileController.class);
	private final DeviceNameStatusServiceImpl deviceNameStatusServiceImpl;

	@Autowired
	public V1CustomerProfileController( DeviceNameStatusServiceImpl deviceNameStatusServiceImpl  ){
		super();
		this.deviceNameStatusServiceImpl = deviceNameStatusServiceImpl;
	}

	/**
	 * Description:- Inquiry deviceInfo and customer id
	 *
	 * @param deviceId
	 * @return return customer status data if found
	 */

	@LogAround
	@GetMapping(value = "/status/cache/{deviceId}")
	public ResponseEntity<TmbOneServiceResponse<CustomerProfileStatus>> getCustomerByDeviceId(
			@PathVariable("deviceId") String deviceId) {
		TmbOneServiceResponse<CustomerProfileStatus> oneServiceResponse = new TmbOneServiceResponse<>();
		logger.info("GetCustomerIdByDeviceId method device id: {}", deviceId);

		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			if (!Strings.isNullOrEmpty(deviceId)) {
				logger.info("inside GetCustomerIdByDeviceId method");
				CustomerProfileStatus deviceNameStatus = deviceNameStatusServiceImpl
						.getCustomerDeviceNameStatus(deviceId , true);
				oneServiceResponse.setData(deviceNameStatus);
				oneServiceResponse
						.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
								ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
				return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);

			} else {
				logger.info("GetCustomerIdByDeviceId method deviceId is empty");
				oneServiceResponse.setStatus(new TmbStatus(ResponseCode.CUSTOMER_NOT_FOUND.getCode(),
						ResponseCode.CUSTOMER_NOT_FOUND.getMessage(), ResponseCode.CUSTOMER_NOT_FOUND.getService(),
						ResponseCode.CUSTOMER_NOT_FOUND.getDesc()));
				return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
			}

		} catch (Exception e) {
			logger.error("Unable to fetch data from oneapp db : {}", e);
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService()));

			return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
		}

	}

}