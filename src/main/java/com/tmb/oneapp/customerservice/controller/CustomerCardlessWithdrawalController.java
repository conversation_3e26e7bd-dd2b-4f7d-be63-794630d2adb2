package com.tmb.oneapp.customerservice.controller;


import java.time.Instant;

import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.CardlessAccumulateRequest;
import com.tmb.oneapp.customerservice.service.CreateCustomerServiceDB;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

/**
 * Controller responsible for Customer EKYC
 *
 */
@RestController
@Tag(name = "Customer Cardless withdrawal Api")
public class CustomerCardlessWithdrawalController {
	private static final TMBLogger<CustomerCardlessWithdrawalController> logger =
			new TMBLogger<>(CustomerCardlessWithdrawalController.class);
	private final CreateCustomerServiceDB createCustomerServiceDB;

	/**
	 * Constructor
	 */
	@Autowired
	public CustomerCardlessWithdrawalController(CreateCustomerServiceDB createCustomerServiceDB) {
		this.createCustomerServiceDB = createCustomerServiceDB;
	}

	/**
	 *
	 * Controller responsible for fetching citizen ID based on image
	 */
	@Operation(summary = "Update cardless accumulate")
	@LogAround
	@PostMapping(value = "/customers/cardless/accumulate")
	public ResponseEntity<TmbServiceResponse<String>> accumulateDailyUsg(
			@Parameter(description = "X-Correlation-ID",
					example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
			@Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull
			@SuppressWarnings("unused") String correlationId,
			@RequestBody CardlessAccumulateRequest reqBody) throws TMBCommonException {
		logger.info("accumulateDailyUsg method start Time : {} ",
				System.currentTimeMillis());
		TmbServiceResponse<String> oneServiceResponse = new TmbServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			this.createCustomerServiceDB.updateAccumulateLimit(reqBody.getCrmId(), Double.valueOf(reqBody.getAmount()).intValue());
			oneServiceResponse.setStatus( new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
					ResponseCode.SUCCESS.getService(), CustomerServiceUtils.returnSuccessDescription()));
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (TMBCommonException tmbcommon) {
			logger.error("accumulateDailyUsg : TMBCommonException : {}", tmbcommon);
			throw tmbcommon;
		} catch (Exception e) {
			logger.error("accumulateDailyUsg : Exception : {}", e);
			throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
		}
	}

}
