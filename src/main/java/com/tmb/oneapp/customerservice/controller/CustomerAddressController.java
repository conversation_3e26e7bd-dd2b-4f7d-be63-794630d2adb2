package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.CustomerSearchRequest;
import com.tmb.oneapp.customerservice.model.profile.Customer;
import com.tmb.oneapp.customerservice.service.CustomerAddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
/**
 * CustomerAddressController request mapping will handle apis call and then
 * navigate to respective method to get customer address
 *
 */
public class CustomerAddressController {

    private static final TMBLogger<CustomerAddressController> logger = new TMBLogger<>(CustomerAddressController.class);

    private final CustomerAddressService customerAddressService;

    @Autowired
    public CustomerAddressController(CustomerAddressService customerAddressService) {
        this.customerAddressService = customerAddressService;
    }

    /**
     * Description:- Inquiry  customer address
     *
     * @param customerSearchRequest the customer serach request
     * @return return customer address data
     */
    @LogAround
    @PostMapping(value = "/customers/address", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<Customer>> fetchCustomerAddress(
            @RequestBody CustomerSearchRequest customerSearchRequest) {
        TmbOneServiceResponse<Customer> customerAddressResponseTmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            Customer customerAddressResponse = customerAddressService.getCustomerAddress(customerSearchRequest);
            customerAddressResponseTmbOneServiceResponse.setData(customerAddressResponse);
            customerAddressResponseTmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(customerAddressResponseTmbOneServiceResponse);
        }catch (Exception e){
            logger.error("Unable to getCustomerAddress data : {} ", e);
            customerAddressResponseTmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(customerAddressResponseTmbOneServiceResponse);
        }
    }
}
