package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.exception.model.TMBBusinessException;
import com.tmb.oneapp.customerservice.model.CustomerAuthenDetailsRequest;
import com.tmb.oneapp.customerservice.model.CustomerAuthenDetailsResponse;
import com.tmb.oneapp.customerservice.service.CustomerAuthenDetailService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Customer Authen Details")
@RestController
@RequiredArgsConstructor
public class CustomerAuthenDetailController {

    private final CustomerAuthenDetailService customerAuthenDetailService;

    @Operation(summary = "Get customer authen details")
    @PostMapping(value = "/authen-details")
    public ResponseEntity<TmbOneServiceResponse<CustomerAuthenDetailsResponse>> getCustomerAuthenDetails(
            @RequestBody CustomerAuthenDetailsRequest loginDetailsRequest
    ) throws TMBBusinessException {

        if (StringUtils.isBlank(loginDetailsRequest.getDeviceId()) && StringUtils.isBlank(loginDetailsRequest.getCrmId())) {
            throw new TMBBusinessException(ResponseCode.INVALID_REQUEST_FIELDS, HttpStatus.BAD_REQUEST);
        }

        CustomerAuthenDetailsResponse loginDetails = customerAuthenDetailService.getCustomerAuthenDetails(loginDetailsRequest);

        TmbOneServiceResponse<CustomerAuthenDetailsResponse> response = new TmbOneServiceResponse<>();
        response.setData(loginDetails);
        response.setStatus(CustomerServiceUtils.getResponseFromStatus(ResponseCode.SUCCESS));
        return ResponseEntity.ok().headers(TMBUtils.getResponseHeaders()).body(response);
    }
}
