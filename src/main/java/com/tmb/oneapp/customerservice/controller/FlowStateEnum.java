package com.tmb.oneapp.customerservice.controller;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.tmb.oneapp.customerservice.constant.FlowStates;
import com.tmb.oneapp.customerservice.model.EKYCProspectETECustomer;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlowStateEnum extends EKYCProspectETECustomer {
    @JsonProperty("flow_state")
    private FlowStates flowState = null;
}
