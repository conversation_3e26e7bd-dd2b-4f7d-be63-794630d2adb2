package com.tmb.oneapp.customerservice.controller.v1;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Description;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.commonfr.CommonFRConfirmUpliftRequest;
import com.tmb.oneapp.customerservice.model.commonfr.CommonFRConfirmUpliftResponse;
import com.tmb.oneapp.customerservice.model.commonfr.CommonFRConsentBaselineDataResponse;
import com.tmb.oneapp.customerservice.model.commonfr.CommonFRFaceMatchingRequest;
import com.tmb.oneapp.customerservice.model.commonfr.CommonFRFaceMatchingResponse;
import com.tmb.oneapp.customerservice.model.commonfr.CommonFRGeneralConfigResponse;
import com.tmb.oneapp.customerservice.model.commonfr.CommonFRGenerateLivenessKeyRequest;
import com.tmb.oneapp.customerservice.model.commonfr.CommonFRGenerateLivenessKeyResponse;
import com.tmb.oneapp.customerservice.model.commonfr.CommonFRValidationRequest;
import com.tmb.oneapp.customerservice.model.commonfr.CommonFRValidationResponse;
import com.tmb.oneapp.customerservice.model.commonfr.CommonFRVerifyFRRequest;
import com.tmb.oneapp.customerservice.model.commonfr.CommonFRVerifyFRResponse;
import com.tmb.oneapp.customerservice.model.commonfr.CommonFRVerifyLivenessRequest;
import com.tmb.oneapp.customerservice.model.commonfr.FaceRecognitionFeature;
import com.tmb.oneapp.customerservice.model.commonfr.CommonFRUpdateBlockRequest;
import com.tmb.oneapp.customerservice.model.commonfr.CommonFRUpdateBlockResponse;
import com.tmb.oneapp.customerservice.service.CommonFRGeneralService;
import com.tmb.oneapp.customerservice.service.CommonFRService;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
public class CommonFRController {
    private static final TMBLogger<CommonFRController> logger = new TMBLogger<>(CommonFRController.class);
    @Autowired
    private CommonFRService commonFRService;
    @Autowired
    private CommonFRGeneralService commonFRGeneralService;

    @LogAround
    @Parameters({
            @Parameter(name = CustomerServiceConstant.X_CRMID, description = CustomerServiceConstant.X_CRMID, example ="001100000000000000000012003333", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="c28f91e4-881e-4387-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, description = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, example ="th", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_APP_VERSION, description = CustomerServiceConstant.HEADER_APP_VERSION, example ="th", in = ParameterIn.HEADER, required = true)
    })
    @GetMapping(value = "/common-fr/general-config", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<CommonFRGeneralConfigResponse>> getCommonFRGeneralConfig(
            @RequestHeader HttpHeaders headers,
            @Parameter(description = "filterType") @RequestParam(value = "filterType") String filterType,
            @Parameter(description = "configType") @RequestParam(value = "configType", required = false) String configType,
            @Parameter(description = "configCode") @RequestParam(value = "configCode", required = false) String configCode,
            @Parameter(description = "flow") @RequestParam(value = "flow", required = false) String flow,
            @Parameter(description = "activeFlag") @RequestParam(value = "activeFlag") Boolean activeFlag

    ) throws TMBCommonException {
        TmbServiceResponse<CommonFRGeneralConfigResponse> response = new TmbServiceResponse<>();
        logger.info("Start getCommonFRGeneralConfig with correlation: {} config code: {}", headers.get(CustomerServiceConstant.HEADER_CORRELATION_ID), configType);
        CommonFRGeneralConfigResponse commonFRGeneralConfigResponse = commonFRGeneralService.getCommonFRGeneralConfig(filterType, configType, configCode, flow, activeFlag);
        response.setData(commonFRGeneralConfigResponse);
        response.setStatus(getStatusSuccess());
        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "Validate customer IAL and photo")
    @PostMapping("/common-fr/request")
    public ResponseEntity<TmbServiceResponse<CommonFRValidationResponse>> postCommonFRRequest(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = CustomerServiceConstant.X_CRMID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.X_CRMID) @RequestHeaderNonNull String crmId,
            @Parameter(description = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, example ="th", required = true) @RequestHeader(CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE) @RequestHeaderNonNull String acceptLanguage,
            @Parameter(description = CustomerServiceConstant.HEADER_APP_VERSION, example ="4.0.0", required = true) @RequestHeader(CustomerServiceConstant.HEADER_APP_VERSION) @RequestHeaderNonNull String appVersion,
            @Parameter(description = CustomerServiceConstant.HEADERS_DEVICE_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADERS_DEVICE_ID) @RequestHeaderNonNull String deviceId,
            @Parameter(description = CustomerServiceConstant.HEADER_X_FORWARD_FOR, example ="localhost", required = true) @RequestHeader(CustomerServiceConstant.HEADER_X_FORWARD_FOR) @RequestHeaderNonNull String ipAddress,
            @RequestBody @Valid CommonFRValidationRequest request
    ) throws TMBCommonException, TMBCommonExceptionWithResponse {
        logger.info("##### START /common-fr/request featureId:{}, flow:{}", request.getFeatureId(), request.getFlow());
        TmbServiceResponse<CommonFRValidationResponse> response = new TmbServiceResponse<>();
        try {
            response.setStatus(getStatusSuccess());
            response.setData(commonFRService.postCommonFRRequest(ipAddress, correlationId, crmId, acceptLanguage, deviceId, appVersion, request));
        } catch (TMBCommonExceptionWithResponse exr){
            logger.error("Error with body when /common-fr/request with correlationId: {}, crmId: {}, error: {}", correlationId, crmId, exr);
            throw exr;
        }catch (Exception ex) {
            logger.error("Error when /common-fr/request with correlationId: {}, crmId: {}, error: {}", correlationId, crmId, ex);
            throw ex;
        }
        logger.info("##### END /common-fr/request");
        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "Get consent baseline")
    @GetMapping(value = "/common-fr/consent/baseline", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<CommonFRConsentBaselineDataResponse>> getCommonFRConsentBaseline(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = CustomerServiceConstant.X_CRMID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.X_CRMID) @RequestHeaderNonNull String crmId,
            @Parameter(description = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, example ="th", required = true) @RequestHeader(CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE) @RequestHeaderNonNull String acceptLanguage,
            @Parameter(description = CustomerServiceConstant.HEADER_APP_VERSION, example ="4.0.0", required = true) @RequestHeader(CustomerServiceConstant.HEADER_APP_VERSION) @RequestHeaderNonNull String appVersion
    ) throws TMBCommonException {
        TmbServiceResponse<CommonFRConsentBaselineDataResponse> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        response.setData(commonFRService.getCommonFRConsentBaseline(correlationId, crmId, acceptLanguage, appVersion));
        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "Request face matching for common FR")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="c28f91e4-881e-4387-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.X_CRMID, description = CustomerServiceConstant.X_CRMID, example ="c28f91e4-881e-4387-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, description = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, example ="th", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_APP_VERSION, description = CustomerServiceConstant.HEADER_APP_VERSION, example ="4.0.0", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADERS_DEVICE_ID, description = CustomerServiceConstant.DEVICE_ID, example ="c28f91e4-881e-4387-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_X_FORWARD_FOR, description = CustomerServiceConstant.HEADER_X_FORWARD_FOR, example ="localhost", in = ParameterIn.HEADER, required = true)
    })
    @PostMapping(value = "/common-fr/face-matching/request", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<CommonFRFaceMatchingResponse>> postCommonFRFaceMatchingRequest(
            @RequestHeader HttpHeaders headers,
            @RequestBody @Valid CommonFRFaceMatchingRequest request
    ) throws TMBCommonException, TMBCommonExceptionWithResponse {
        logger.info("### START /common-fr/face-matching/request ###");
        logger.info("x-crmid={},deviceId={},X-Forward-For={}",
                headers.getFirst(CustomerServiceConstant.HEADERS_X_CRMID),
                headers.getFirst(CustomerServiceConstant.DEVICE_ID),
                headers.getFirst(CustomerServiceConstant.HEADER_X_FORWARD_FOR));
        TmbServiceResponse<CommonFRFaceMatchingResponse> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        response.setData(commonFRService.postCommonFRFaceMatchingRequest(headers, request));
        logger.info("### END /common-fr/face-matching/request");
        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "Request to get consent")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.X_CRMID, description = CustomerServiceConstant.X_CRMID, example ="c28f91e4-881e-4387-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="c28f91e4-881e-4387-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, description = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, example ="th", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_APP_VERSION, description = CustomerServiceConstant.HEADER_APP_VERSION, example ="4.0.0", in = ParameterIn.HEADER, required = true)
    })
    @PostMapping(value = "/common-fr/verify-fr", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<CommonFRVerifyFRResponse>> postCommonFRVerifyFR(
            @RequestHeader HttpHeaders headers,
            @RequestBody @Valid CommonFRVerifyFRRequest request
    ) throws TMBCommonException {
        logger.info("##### START /common-fr/verify-fr flow={} uuid={} featureId={} ", request.getFlow(), request.getUuid(), request.getFeatureId());
        TmbServiceResponse<CommonFRVerifyFRResponse> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        response.setData(commonFRService.postCommonFRVerifyFR(headers, request));
        logger.info("##### END /common-fr/verify-fr");

        return ResponseEntity.ok(response);
    }

    private Status getStatusSuccess() {
        Description description = new Description(ResponseCode.SUCCESS.getDesc(), ResponseCode.SUCCESS.getDesc());
        return new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), description);
    }

    @LogAround
    @Operation(summary = "Request face matching for common FR")
    @PostMapping(value = "/common-fr/confirm-uplift/request", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<CommonFRConfirmUpliftResponse>> postCommonFrConfirmUpliftRequest(
            @Parameter(description = CustomerServiceConstant.X_CRMID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.X_CRMID) @RequestHeaderNonNull String crmId,
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, example ="th", required = true) @RequestHeader(CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE) @RequestHeaderNonNull String acceptLanguage,
            @Parameter(description = CustomerServiceConstant.HEADER_APP_VERSION, example ="4.0.0", required = true) @RequestHeader(CustomerServiceConstant.HEADER_APP_VERSION) @RequestHeaderNonNull String appVersion,
            @Parameter(description = CustomerServiceConstant.HEADER_X_FORWARD_FOR, example ="127.0.0.1", required = true) @RequestHeader(CustomerServiceConstant.HEADER_X_FORWARD_FOR) @RequestHeaderNonNull String ipAddress,
            @Parameter(description = CustomerServiceConstant.HEADERS_DEVICE_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da") @RequestHeader(name = CustomerServiceConstant.HEADERS_DEVICE_ID, required = false) String deviceId,
            @RequestBody @Valid CommonFRConfirmUpliftRequest commonFRConfirmUpliftRequest
    ) throws TMBCommonException {
        logger.info("##### START /common-fr/confirm-uplift/request correlationId : {}", correlationId);
        TmbServiceResponse<CommonFRConfirmUpliftResponse> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        response.setData(commonFRService.postCommonFrConfirmUpliftRequest(crmId, deviceId, correlationId, acceptLanguage, appVersion, ipAddress, commonFRConfirmUpliftRequest));
        logger.info("##### END /common-fr/confirm-uplift/request status code : {}"+response.getStatus().getCode());
        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "Get Feature")
    @GetMapping(value = "/common-fr/feature", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<FaceRecognitionFeature>> getFeature(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = "featureId") @RequestParam(value = "featureId") Integer featureId
    ) throws TMBCommonException {
        logger.info("Start CommonFRController.getFeature with correlationId: {} ", correlationId);
        TmbServiceResponse<FaceRecognitionFeature> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        response.setData(commonFRService.getFeature(featureId));
        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "Request for insert or update common-fr total block into MongoDB")
    @PostMapping(value = "/common-fr/update-block", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<CommonFRUpdateBlockResponse>> commonFRUpdateBlock(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = CustomerServiceConstant.X_CRMID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.X_CRMID) @RequestHeaderNonNull String crmId,
            @RequestBody @Valid CommonFRUpdateBlockRequest commonFRUpdateBlockRequest
    ) throws TMBCommonException {
        logger.info("Start CommonFRController.commonFRUpdateBlock with correlationId: {} ", correlationId);
        TmbServiceResponse<CommonFRUpdateBlockResponse> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        response.setData(commonFRService.postCommonFRUpdateBlock(crmId, correlationId, commonFRUpdateBlockRequest));
        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "generate liveness reference key")
    @PostMapping(value = "/common-fr/generate-liveness-key", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<CommonFRGenerateLivenessKeyResponse>> commonFRGenerateLivenessKey(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = CustomerServiceConstant.HEADERS_DEVICE_ID, example ="device-id", required = false) @RequestHeader(name = CustomerServiceConstant.HEADERS_DEVICE_ID, required = false) String deviceId,
            @Parameter(description = CustomerServiceConstant.AUTH_PUBLIC_KEY, example ="auth-public-key", required = false) @RequestHeader(name = CustomerServiceConstant.AUTH_PUBLIC_KEY, required = false) String authPublicKey,
            @RequestBody CommonFRGenerateLivenessKeyRequest request
    ) throws TMBCommonException, JsonProcessingException {
        logger.info("Start CommonFRController.commonFRGenerateLivenessKey with correlationId: {} ", correlationId);
        TmbServiceResponse<CommonFRGenerateLivenessKeyResponse> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        response.setData(commonFRService.commonFRGenerateLivenessKey(correlationId, deviceId, authPublicKey, request));
        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "generate liveness reference key")
    @PostMapping(value = "/common-fr/verify-liveness", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<String>> commonFRVerifyLiveness(
            @Parameter(description = CustomerServiceConstant.X_CRMID, example = "001100000000000000000001184383", required = false)
            @RequestHeader(name = CustomerServiceConstant.X_CRMID, required = false) String crmId,
            @Parameter(description = CustomerServiceConstant.TEMP_ID, example = "tempId", required = false)
            @RequestHeader(name = CustomerServiceConstant.TEMP_ID, required = false) String tempId,
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @RequestBody CommonFRVerifyLivenessRequest request
    ) throws TMBCommonException, JsonProcessingException {
        logger.info("Start CommonFRController.commonFRGenerateLivenessKey with correlationId: {} ", correlationId);
        TmbServiceResponse<String> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        commonFRService.commonFRVerifyLiveness(crmId, tempId, correlationId, request);
        return ResponseEntity.ok(response);
    }
}
