package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.TaxCertificateConsentsRequest;
import com.tmb.oneapp.customerservice.model.TaxCertificateConsentsResponse;
import com.tmb.oneapp.customerservice.model.TaxCertificateConsentsUpdateRequest;
import com.tmb.oneapp.customerservice.model.TaxCertificateUpdateResponse;
import com.tmb.oneapp.customerservice.service.TaxCertificateConsentService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_PARAM_X_COR_ID_DEFAULT;
import static com.tmb.oneapp.customerservice.constant.ResponseCode.SUCCESS;

@RestController
@Tag(name = "TaxCertificateConsent Controller")
public class TaxCertificateConsentController {
    private static final TMBLogger<TaxCertificateConsentController> logger = new TMBLogger<>(TaxCertificateConsentController.class);
    private final TaxCertificateConsentService taxCertificateConsentService;

    @Autowired
    public TaxCertificateConsentController(TaxCertificateConsentService taxCertificateConsentService) {
        this.taxCertificateConsentService = taxCertificateConsentService;
    }

    @LogAround
    @PostMapping(value = "/customers/homeloan/tax/get-consents")
    public ResponseEntity<TmbOneServiceResponse<TaxCertificateConsentsResponse>> getTaxCertificateConsent(
            @Parameter(description = HEADER_CORRELATION_ID, example =HEADER_PARAM_X_COR_ID_DEFAULT, required = true)
            @RequestHeader(HEADER_CORRELATION_ID) String correlationId,
            @RequestBody TaxCertificateConsentsRequest requests) {
        logger.info("getTaxCertificateConsent method start time {}", System.currentTimeMillis());
        TmbOneServiceResponse<TaxCertificateConsentsResponse> serviceResponse = new TmbOneServiceResponse<>();

        try {
            TaxCertificateConsentsResponse response = taxCertificateConsentService
                    .getConsent(correlationId, requests);
            serviceResponse.setData(response);

            serviceResponse.setStatus(new TmbStatus(SUCCESS.getCode(), SUCCESS.getMessage(),
                    SUCCESS.getService(), SUCCESS.getDesc()));

            return ResponseEntity.ok().body(serviceResponse);
        } catch (Exception e) {
            serviceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(serviceResponse);
        }
    }

    @LogAround
    @PostMapping(value = "/customers/homeloan/tax/update-consents")
    public ResponseEntity<TmbOneServiceResponse<TaxCertificateUpdateResponse>> updateTaxCertificateConsent(
            @Parameter(description = HEADER_CORRELATION_ID, example =HEADER_PARAM_X_COR_ID_DEFAULT, required = true)
            @RequestHeader(HEADER_CORRELATION_ID) String correlationId,
            @RequestBody TaxCertificateConsentsUpdateRequest requests
    ) {
        logger.info("updateTaxCertificateConsent method start time {}", System.currentTimeMillis());
        TmbOneServiceResponse<TaxCertificateUpdateResponse> serviceResponse = new TmbOneServiceResponse<>();

        try {
            TaxCertificateUpdateResponse response = taxCertificateConsentService
                    .updateConsent(correlationId, requests);

            serviceResponse.setData(response);

            serviceResponse.setStatus(new TmbStatus(SUCCESS.getCode(), SUCCESS.getMessage(),
                    SUCCESS.getService(), SUCCESS.getDesc()));

            return ResponseEntity.ok().body(serviceResponse);
        } catch (Exception e) {
            serviceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(serviceResponse);
        }
    }

}