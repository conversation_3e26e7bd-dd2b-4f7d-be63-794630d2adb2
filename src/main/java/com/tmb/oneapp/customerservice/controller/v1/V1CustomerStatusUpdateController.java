package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.UpdateStatusRequestV2;
import com.tmb.oneapp.customerservice.model.UpdateStatusResponse;
import com.tmb.oneapp.customerservice.service.CustomerStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

/**
 * Class responsible for updating the customer status in crm_cust_profile table
 */
@RestController
@Tag(name = "To update customer status by CRM ID")
public class V1CustomerStatusUpdateController {
    private final CustomerStatusService customerStatusService;
    private static final TMBLogger<V1CustomerStatusUpdateController> logger = new TMBLogger<>(V1CustomerStatusUpdateController.class);

    /**
     * Constructor
     *
     * @param customerStatusService
     */
    @Autowired
    public V1CustomerStatusUpdateController(CustomerStatusService customerStatusService) {
        this.customerStatusService = customerStatusService;
    }

    /**
     * Method to update customer Status
     *
     * @param header
     * @param updateStatusRequest
     * @return
     */
    @Operation(summary = "Api to update customer status")
    @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = "Transations ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da")
    @PatchMapping(value = "/customers/profile-status")
    @LogAround
    public ResponseEntity<TmbServiceResponse<UpdateStatusResponse>> updateCustomerStatus(@RequestHeader HttpHeaders header, @Valid @RequestBody UpdateStatusRequestV2 updateStatusRequest) {
        TmbServiceResponse<UpdateStatusResponse> response = new TmbServiceResponse<>();
        String errorCode = "";
        if(StringUtils.isNotBlank(updateStatusRequest.getMbStatus()) && StringUtils.isNotBlank(updateStatusRequest.getEbStatus())){
            errorCode = customerStatusService.updateMBAndEBCustomerStatus(updateStatusRequest);
        }else if (StringUtils.isNotBlank(updateStatusRequest.getMbStatus())){
            errorCode = customerStatusService.updateMBCustomerStatus(updateStatusRequest);
        }else if (StringUtils.isNotBlank(updateStatusRequest.getEbStatus())){
            errorCode = customerStatusService.updateEBCustomerStatus(updateStatusRequest);
        }else{
            logger.info("no found condition to update the customer status for {}",updateStatusRequest.getCrmId());
        }
        if (CustomerServiceConstant.SUCCESS_CODE_NEW.equals(errorCode)) {
            response.setStatus(new Status(CustomerServiceConstant.SUCCESS_CODE_NEW, CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMERS_SERVICE, null));
            response.setData(buildUpdateStatusResponse(updateStatusRequest));
        } else {
            response.setStatus(new Status(CustomerServiceConstant.UNABLE_TO_UPDATE, CustomerServiceConstant.UNABLE_TO_UPDATE_MESSAGE, CustomerServiceConstant.CUSTOMERS_SERVICE, null));
        }
        return ResponseEntity.ok().headers(TMBUtils.getResponseHeaders()).body(response);
    }

    private UpdateStatusResponse buildUpdateStatusResponse(UpdateStatusRequestV2 updateStatusRequest) {
        UpdateStatusResponse updateStatusResponse = new UpdateStatusResponse();
        if(StringUtils.isNotBlank(updateStatusRequest.getEbStatus())) {
            updateStatusResponse.setEbStatus(updateStatusRequest.getEbStatus());
            String ebStatusDesc = customerStatusService.getStatusDesc(updateStatusRequest.getEbStatus());
            updateStatusResponse.setEbDescription(ebStatusDesc);
        }
        if (StringUtils.isNotBlank(updateStatusRequest.getMbStatus())) {
            updateStatusResponse.setMbStatus(updateStatusRequest.getMbStatus());
            String mbStatusDesc = customerStatusService.getStatusDesc(updateStatusRequest.getMbStatus());
            updateStatusResponse.setMbDescription(mbStatusDesc);
        }
        return updateStatusResponse;
    }
}
