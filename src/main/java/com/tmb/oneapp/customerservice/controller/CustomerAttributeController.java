package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.BillPayAccumulateUsageRequest;
import com.tmb.oneapp.customerservice.model.CustomerAppLevelAttribute;
import com.tmb.oneapp.customerservice.model.CustomerAppMaskAccountIdFlag;
import com.tmb.oneapp.customerservice.repository.CustomerAttributeRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

import java.sql.Date;
import java.time.Instant;
import java.util.Optional;

/**
 * Controller class responsible for fetching masking flag
 */
@RestController
@Tag(name = "Customer Attribute Service Controller for Masking")
public class CustomerAttributeController {

    private static final TMBLogger<CustomerAttributeController> logger = new TMBLogger<>(CustomerAttributeController.class);
    private final CustomerAttributeRepository customerAttributeRepository;

    /**
     * Constructor
     *
     * @param customerAttributeRepository
     */
    public CustomerAttributeController(CustomerAttributeRepository customerAttributeRepository) {
    	super();
        this.customerAttributeRepository = customerAttributeRepository;
    }

    /**
     * Method to fetch masking flag based on crm ID
     *
     * @param crmId
     * @return
     */
    @LogAround
    @Operation(summary = "Fetch Masking Flag based on CRM-ID")
    @GetMapping(value = "/customers/fetchmasking")
    public ResponseEntity<TmbOneServiceResponse<CustomerAppMaskAccountIdFlag>> getCustomerCrmAppLevelATTR(@Parameter(description = "CRMID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = "crmId") String crmId) {
        
    	logger.info("Get CRM ID ===> {} ", crmId);
    	Optional<CustomerAppLevelAttribute> customerAppLevelAttribute = customerAttributeRepository.findAllByCrmIdAndAppId(crmId, CustomerServiceConstant.APP_ID_01);
        TmbOneServiceResponse<CustomerAppMaskAccountIdFlag> response = new TmbOneServiceResponse();
        if (customerAppLevelAttribute.isPresent()) {
        	logger.info("Get data from customerAppLevelAttribute ===> {}", customerAppLevelAttribute);
        	CustomerAppMaskAccountIdFlag customerAppMaskAccountIdFlag = new CustomerAppMaskAccountIdFlag();
        	customerAppMaskAccountIdFlag.setMaskAcctIdFlag(customerAppLevelAttribute.get().getMaskAcctIdFlag());
        	 response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE, CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMER_EXP_SERVICE));
             response.setData(customerAppMaskAccountIdFlag);
            return ResponseEntity.ok().body(response);
        } else {
            response.setStatus(new TmbStatus(CustomerServiceConstant.FAILED_CODE, CustomerServiceConstant.FAILED_MESSAGE, CustomerServiceConstant.CUSTOMER_EXP_SERVICE));
            logger.info("Get data from customerAppLevelAttribute ===> {} not found", crmId);
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Method update field BILLPAY_ACCU_USG_AMT
     *
     * @param crmId
     * @param correlationId
     * @param request
     * @return
     * @throws TMBCommonException
     */
    @LogAround
    @Operation(summary = "Update Bill pay accumulate usage amount")
    @PostMapping(value = "/customers/update-billpay-accu-usage", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<String>> updateBillPayAccumulateUsage(
            @Parameter(description = "X-CRMID", example = "001100000000000000000001184383", required = true)
            @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @RequestBody BillPayAccumulateUsageRequest request) throws TMBCommonException {
        logger.info("[START] :: updateBillPayAccumulateUsage, [Correlation-ID, {}]", correlationId);
        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        try {
            customerAttributeRepository.updateBillPayAccumulateUsage(crmId, request.getBillPayAccuUsgAmt());
            response.setStatus(new TmbStatus(
                    ResponseCode.SUCCESS.getCode(),
                    ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService()
            ));
            response.setData("Update billPayAccumulateUsage successfully");
            logger.info("[OUT] :: updateBillPayAccumulateUsage successfully, [Correlation-ID = {}]", correlationId);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to process updatePaymentAccumuldateUsage", e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    e);
        }
    }
    @LogAround
    @Operation(summary = "Clear default account")
    @DeleteMapping(value = "/customers/clear-default-account", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<String>> clearDefaultAccount(
            @RequestHeader(name = "X-CRMID") @NotNull String crmId,
            @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId) throws TMBCommonException {
        logger.info("[START] :: clearDefaultAccount, [Correlation-ID, {}]", correlationId);
        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        try {
            customerAttributeRepository.clearDefaultAcctId(new Date(Instant.now().toEpochMilli()),
                    CustomerServiceConstant.TMB_ONE_APP_ID, crmId);
            response.setStatus(new TmbStatus(
                    ResponseCode.SUCCESS.getCode(),
                    ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService()
            ));
            response.setData("Clear Default Account Number successfully");
            logger.info("[OUT] :: clearDefaultAccount successfully, [Correlation-ID = {}]", correlationId);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to process clearDefaultAccount", e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    e);
        }
    }
}
