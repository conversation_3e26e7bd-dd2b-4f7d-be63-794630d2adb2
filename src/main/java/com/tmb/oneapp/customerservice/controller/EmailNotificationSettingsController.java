package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.EmailNotificationSettingsRequest;
import com.tmb.oneapp.customerservice.model.EmailNotificationSettingsResponse;
import com.tmb.oneapp.customerservice.service.EmailNotificationSettingsService;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.Optional;

@RestController
@RequestMapping("/customers/settings/notification")
@Tag(name = "APIs to manage email notification settings")
public class EmailNotificationSettingsController {

    private final EmailNotificationSettingsService emailNotificationSettingsService;

    public EmailNotificationSettingsController(EmailNotificationSettingsService emailNotificationSettingsService) {
        this.emailNotificationSettingsService = emailNotificationSettingsService;
    }

    /**
     * Method to get the current email notification settings
     * @param crmId
     * @return
     * @throws TMBCommonException
     */
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Transaction ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da")
    })
    @GetMapping("/email")
    @Operation(summary = "API to get current email notification settings")
    @LogAround
    public ResponseEntity<TmbOneServiceResponse<EmailNotificationSettingsResponse>> getEmailNotificationSettings(
            @Parameter(description = "X-CRMID", example = "001100000000000000000000200015", required = true)
            @Valid @RequestHeader("X-CRMID") @RequestHeaderNonNull String crmId) throws TMBCommonException {
        TmbOneServiceResponse<EmailNotificationSettingsResponse> response = new TmbOneServiceResponse<>();
        Optional<EmailNotificationSettingsResponse> optionalEmailNotificationSettingsResponse = emailNotificationSettingsService.getEmailNotificationSettings(crmId);
        if (optionalEmailNotificationSettingsResponse.isPresent()) {
            response.setData(optionalEmailNotificationSettingsResponse.get());
            response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE, CustomerServiceConstant.SUCCESS_MESSAGE,
                    CustomerServiceConstant.CUSTOMERS_SERVICE, null));
        } else {
            throw new TMBCommonException(CustomerServiceConstant.EMAIL_NOTI_ERROR_CODE, CustomerServiceConstant.FAILED_MESSAGE, CustomerServiceConstant.CUSTOMERS_SERVICE, HttpStatus.OK, null);
        }
        return ResponseEntity.ok().headers(TMBUtils.getResponseHeaders()).body(response);
    }


    /**
     * Method to update email notification settings
     * @param crmId
     * @param emailNotificationSettingsRequest : request from mobile
     * @return
     * @throws TMBCommonException
     */
    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Transaction ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da")
    })

    @PostMapping("/email")
    @Operation(summary = "API to get update email notification settings")
    public ResponseEntity<TmbOneServiceResponse<String>> updateEmailNotificationSettings(
            @Parameter(description = "X-CRMID", example = "001100000000000000000000200015", required = true)
            @Valid @RequestHeader("X-CRMID") @RequestHeaderNonNull String crmId,
            @RequestBody EmailNotificationSettingsRequest emailNotificationSettingsRequest) throws TMBCommonException {
        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        String errorCode =
                emailNotificationSettingsService.updateEmailNotificationSettings(crmId, emailNotificationSettingsRequest);
        if (CustomerServiceConstant.SUCCESS_CODE.equals(errorCode)) {
            response.setData(CustomerServiceConstant.EMAIL_NOTI_SUCCESS_MESSAGE);
            response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE,
                    CustomerServiceConstant.SUCCESS_MESSAGE,
                    CustomerServiceConstant.CUSTOMERS_SERVICE,
                    null));
        } else {
            throw new TMBCommonException(CustomerServiceConstant.EMAIL_NOTI_ERROR_CODE,
                    CustomerServiceConstant.FAILED_MESSAGE,
                    CustomerServiceConstant.CUSTOMERS_SERVICE,
                    HttpStatus.OK, null);
        }
        return ResponseEntity.ok().headers(TMBUtils.getResponseHeaders()).body(response);
    }

    @Parameters({
            @Parameter(name = "X-Correlation-ID", description = "Transaction ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da")
    })

    @PostMapping("/email/migrate")
    @Operation(summary = "API to migrate email notification settings from touch db to Oneapp db")
    public ResponseEntity<TmbOneServiceResponse<String>> migrateEmailNotificationSettings(
            @Parameter(description = "X-CRMID", example = "001100000000000000000000200015", required = true)
            @Valid @RequestHeader("X-CRMID") @RequestHeaderNonNull String crmId) {
        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        boolean isMigrated =
                emailNotificationSettingsService.migrateEmailSettings(crmId);
        if (isMigrated) {
            response.setData(CustomerServiceConstant.EMAIL_NOTI_SUCCESS_MESSAGE);
            response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE,
                    CustomerServiceConstant.SUCCESS_MESSAGE,
                    CustomerServiceConstant.CUSTOMERS_SERVICE,
                    null));
        } else {
            response.setStatus(new TmbStatus(CustomerServiceConstant.FAILED_CODE,
                    CustomerServiceConstant.FAILED_MESSAGE,
                    CustomerServiceConstant.CUSTOMERS_SERVICE,
                    null));
        }
        return ResponseEntity.ok().headers(TMBUtils.getResponseHeaders()).body(response);
    }
}
