package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.ExchangeRate;
import com.tmb.oneapp.customerservice.service.ExchangeRateService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Controller for obtaining ODS Exchange rate
 */
@RestController
@Tag(name = "ODS Exchange rate controller")
public class ExchangeRateController {
    private static final TMBLogger<ExchangeRateController> logger = new TMBLogger<>(ExchangeRateController.class);
    private final ExchangeRateService exchangeRateService;

    @Autowired
    public ExchangeRateController(ExchangeRateService exchangeRateService) {
        this.exchangeRateService = exchangeRateService;

    }

    /**
     * Get odf exchange rate
     *
     * @return return response
     */
    @Operation(summary = "Get ODS Exchange rate")
    @LogAround
    @GetMapping(value = "/customers/exchangerates")
    public ResponseEntity<TmbOneServiceResponse<List<ExchangeRate>>> getExchangeRates() {

        TmbOneServiceResponse<List<ExchangeRate>> response = new TmbOneServiceResponse<>();

        try {
            List<ExchangeRate> responseData = exchangeRateService.getExchangeRates();
            response.setData(responseData);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(),
                    ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));

            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(response);
        } catch (Exception e) {
            logger.error("Unexpected error when calling GET /customers/exchangerates : {} ", e);
        }

        response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .headers(TMBUtils.getResponseHeaders())
                .body(response);
    }

}





