package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.EkycBiometricUpdateConsentBodyRequest;
import com.tmb.oneapp.customerservice.model.PostBiometricUpdateConsentResponse;
import com.tmb.oneapp.customerservice.service.CustomerEkycBiometricUpdateConsentService;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import jakarta.validation.Valid;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller class responsible for Get Biometric Get Data
 */
@RestController
@Tag(name = "Biometric Update Consent")
public class CustomerEkycBiometricUpdateConsentController {

    private static final TMBLogger<CustomerEkycBiometricUpdateConsentController> logger = new TMBLogger<>(CustomerEkycBiometricUpdateConsentController.class);
    private final CustomerEkycBiometricUpdateConsentService customerEkycBiometricUpdateConsentService;

    /**
     * Constructor
     *
     * @param customerEkycBiometricUpdateConsentService model
     */
    public CustomerEkycBiometricUpdateConsentController(CustomerEkycBiometricUpdateConsentService customerEkycBiometricUpdateConsentService) {
    	super();
        this.customerEkycBiometricUpdateConsentService = customerEkycBiometricUpdateConsentService;
    }

    /**
     * method : To call CustomerEkycBiometricGetService
     *
     * @param requestBody EkycBiometricGetRequestFormat
     *
     * @return EkycBiometricGetResponseFormat
     */
    @Operation(summary = "Update Consent Biometric Get")
    @PostMapping(value = "/customers/ekyc/biometric/consent/update", produces = MediaType.APPLICATION_JSON_VALUE)
    @LogAround
    public ResponseEntity<TmbOneServiceResponse<PostBiometricUpdateConsentResponse>> updateEkycBiometricConsent(
    		@Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Valid @RequestBody EkycBiometricUpdateConsentBodyRequest requestBody) {

        TmbOneServiceResponse<PostBiometricUpdateConsentResponse> customerEkycBiometricResponse = new TmbOneServiceResponse<>();

        try {
            final PostBiometricUpdateConsentResponse ekycGetBiometricResponse = customerEkycBiometricUpdateConsentService.updateBiometricConsent(correlationId, requestBody);

            customerEkycBiometricResponse.setData(ekycGetBiometricResponse);
            customerEkycBiometricResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                            ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(customerEkycBiometricResponse);
        } catch (Exception e) {
            logger.error("Unable to update biometric consent : {} ", e);
            customerEkycBiometricResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(),
                    "Can not update biometric consent: " + e.getMessage(),
                    ResponseCode.FAILED.getService(),
                    ResponseCode.FAILED.getDesc()));
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(customerEkycBiometricResponse);
        }
    }
}
