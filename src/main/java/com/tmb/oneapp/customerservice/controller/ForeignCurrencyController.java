package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.foreigncurrency.ExchangeDailyLimitRequest;
import com.tmb.oneapp.customerservice.model.foreigncurrency.ExchangeDailyLimitResponse;
import com.tmb.oneapp.customerservice.service.ForeignCurrencyService;
import com.tmb.oneapp.customerservice.utils.TtbOneUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.time.Instant;

@RestController
@RequestMapping("/customers")
@RequiredArgsConstructor
@Tag(name = "to manage foreign currency")
public class ForeignCurrencyController {
    private static final TMBLogger<ForeignCurrencyController> logger = new TMBLogger<>(ForeignCurrencyController.class);
    private final ForeignCurrencyService foreignCurrencyService;

    @LogAround
    @GetMapping("/fcd/exchange-limit")
    @Operation(summary = "API Get Exchange Daily limit")
    public ResponseEntity<TmbServiceResponse<ExchangeDailyLimitResponse>> getExchangeDailyLimit(
            @Parameter(description = CustomerServiceConstant.HEADERS_X_CRMID, example ="001100000000000000000012017238") @Valid @RequestHeader("X-CRMID") @NonNull String crmId,
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @RequestHeader("X-Correlation-ID") String correlationId)
            throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        try {
            logger.info("ForeignCurrencyController  getExchangeDailyLimit method start Time : {} ",
                    System.currentTimeMillis());
            return getTmbOneServiceResponseEntitySuccess(foreignCurrencyService.getExchangeDailyLimit(crmId, correlationId));
        } catch (Exception e) {
            logger.error("getExchangeDailyLimit has error :", e);
            throw TtbOneUtils.handleException(e);
        }
    }

    @LogAround
    @PostMapping("/fcd/update-exchange-usage")
    @Operation(summary = "API Update Exchange Usage")
    public ResponseEntity<TmbServiceResponse<String>> updateExchangeDailyLimit(
            @Parameter(description = CustomerServiceConstant.HEADERS_X_CRMID, example ="001100000000000000000012017238") @RequestHeader("X-CRMID") String crmId,
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @RequestHeader("X-Correlation-ID") String correlationId,
            @RequestBody ExchangeDailyLimitRequest exchangeDailyLimitRequest)
            throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        try {
            logger.info("ForeignCurrencyController  updateExchangeDailyLimit method start Time : {} ",
                    System.currentTimeMillis());
            foreignCurrencyService.updateExchangeUsageAmount(crmId, exchangeDailyLimitRequest.getAmount());
            return getTmbOneServiceResponseEntitySuccess("Update Success");
        } catch (Exception e) {
            logger.error("getExchangeDailyLimit has error :", e);
            throw TtbOneUtils.handleException(e);
        }
    }

    @LogAround
    private <T> ResponseEntity<TmbServiceResponse<T>> getTmbOneServiceResponseEntitySuccess(T data) {
        TmbServiceResponse<T> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), null));
        tmbServiceResponse.setData(data);
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set("Timestamp", String.valueOf(Instant.now().toEpochMilli()));
        ResponseEntity<TmbServiceResponse<T>> responseEntity = ResponseEntity.ok().headers(responseHeaders).body(tmbServiceResponse);
        return responseEntity;
    }
}
