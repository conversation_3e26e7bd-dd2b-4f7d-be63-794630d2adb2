package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.logger.LogAround;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.cache.AddFavoriteCacheRequest;
import com.tmb.oneapp.customerservice.model.cache.CacheResponse;
import com.tmb.oneapp.customerservice.model.cache.KycDataCacheRequest;
import com.tmb.oneapp.customerservice.service.V1CacheService;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/cache")
public class V1CacheController {
    @Autowired
    private V1CacheService v1CacheService;

    @LogAround
    @GetMapping("/kyc-data")
    public ResponseEntity<TmbOneServiceResponse<CacheResponse>> getKycDataCache(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = CustomerServiceConstant.ACCEPT_LANGUAGE, example ="th", required = false) @RequestHeader(value = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, required = false) @RequestHeaderNonNull String acceptLanguage,
            @Parameter(description = CustomerServiceConstant.APP_VERSION, example ="4.0.0", required = false) @RequestHeader(value = CustomerServiceConstant.APP_VERSION, required = false) @RequestHeaderNonNull String appVersion,
            @RequestParam(value = "crmId") String crmId
    ) {
        return v1CacheService.getKycDataCache(crmId);
    }

    @LogAround
    @PostMapping("/kyc-data")
    public ResponseEntity<TmbOneServiceResponse<Void>> postKycDataCache(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = CustomerServiceConstant.ACCEPT_LANGUAGE, example ="th", required = false) @RequestHeader(value = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, required = false) @RequestHeaderNonNull String acceptLanguage,
            @Parameter(description = CustomerServiceConstant.APP_VERSION, example ="4.0.0", required = false) @RequestHeader(value = CustomerServiceConstant.APP_VERSION, required = false) @RequestHeaderNonNull String appVersion,
            @RequestBody KycDataCacheRequest request
    ) {
        return v1CacheService.postKycDataCache(request);
    }

    @LogAround
    @DeleteMapping("/kyc-data")
    public ResponseEntity<TmbOneServiceResponse<Void>> deleteKycDataCache(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = CustomerServiceConstant.ACCEPT_LANGUAGE, example ="th", required = false) @RequestHeader(value = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, required = false) @RequestHeaderNonNull String acceptLanguage,
            @Parameter(description = CustomerServiceConstant.APP_VERSION, example ="4.0.0", required = false) @RequestHeader(value = CustomerServiceConstant.APP_VERSION, required = false) @RequestHeaderNonNull String appVersion,
            @RequestParam(value = "crmId") String crmId
    ) {
        return v1CacheService.deleteKycDataCache(crmId);
    }

    @LogAround
    @GetMapping("/add-favorite")
    public ResponseEntity<TmbOneServiceResponse<CacheResponse>> getAddFavoriteCache(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String xCorrelationId,
            @Parameter(description = CustomerServiceConstant.ACCEPT_LANGUAGE, example ="th", required = false) @RequestHeader(value = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, required = false) @RequestHeaderNonNull String acceptLanguage,
            @Parameter(description = CustomerServiceConstant.APP_VERSION, example ="4.0.0", required = false) @RequestHeader(value = CustomerServiceConstant.APP_VERSION, required = false) @RequestHeaderNonNull String appVersion,
            @RequestParam(value = "correlationId") String correlationId
    ) {
        return v1CacheService.getAddFavoriteCache(correlationId);
    }

    @LogAround
    @PostMapping("/add-favorite")
    public ResponseEntity<TmbOneServiceResponse<Void>> postAddFavoriteCache(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = CustomerServiceConstant.ACCEPT_LANGUAGE, example ="th", required = false) @RequestHeader(value = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, required = false) @RequestHeaderNonNull String acceptLanguage,
            @Parameter(description = CustomerServiceConstant.APP_VERSION, example ="4.0.0", required = false) @RequestHeader(value = CustomerServiceConstant.APP_VERSION, required = false) @RequestHeaderNonNull String appVersion,
            @RequestBody AddFavoriteCacheRequest request
    ) {
        return v1CacheService.postAddFavoriteCache(request);
    }

    @LogAround
    @DeleteMapping("/add-favorite")
    public ResponseEntity<TmbOneServiceResponse<Void>> deleteAddFavoriteCache(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String xCorrelationId,
            @Parameter(description = CustomerServiceConstant.ACCEPT_LANGUAGE, example ="th", required = false) @RequestHeader(value = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, required = false) @RequestHeaderNonNull String acceptLanguage,
            @Parameter(description = CustomerServiceConstant.APP_VERSION, example ="4.0.0", required = false) @RequestHeader(value = CustomerServiceConstant.APP_VERSION, required = false) @RequestHeaderNonNull String appVersion,
            @RequestParam(value = "correlationId") String correlationId
    ) {
        return v1CacheService.deleteAddFavoriteCache(correlationId);
    }

    @LogAround
    @GetMapping("/update-favorite")
    public ResponseEntity<TmbOneServiceResponse<CacheResponse>> getUpdateFavoriteCache(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String xCorrelationId,
            @Parameter(description = CustomerServiceConstant.ACCEPT_LANGUAGE, example ="th", required = false) @RequestHeader(value = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, required = false) @RequestHeaderNonNull String acceptLanguage,
            @Parameter(description = CustomerServiceConstant.APP_VERSION, example ="4.0.0", required = false) @RequestHeader(value = CustomerServiceConstant.APP_VERSION, required = false) @RequestHeaderNonNull String appVersion,
            @RequestParam(value = "correlationId") String correlationId
    ) {
        return v1CacheService.getUpdateFavoriteCache(correlationId);
    }

    @LogAround
    @PostMapping("/update-favorite")
    public ResponseEntity<TmbOneServiceResponse<Void>> postUpdateFavoriteCache(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = CustomerServiceConstant.ACCEPT_LANGUAGE, example ="th", required = false) @RequestHeader(value = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, required = false) @RequestHeaderNonNull String acceptLanguage,
            @Parameter(description = CustomerServiceConstant.APP_VERSION, example ="4.0.0", required = false) @RequestHeader(value = CustomerServiceConstant.APP_VERSION, required = false) @RequestHeaderNonNull String appVersion,
            @RequestBody AddFavoriteCacheRequest request
    ) {
        return v1CacheService.postUpdateFavoriteCache(request);
    }

    @LogAround
    @DeleteMapping("/update-favorite")
    public ResponseEntity<TmbOneServiceResponse<Void>> deleteUpdateFavoriteCache(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String xCorrelationId,
            @Parameter(description = CustomerServiceConstant.ACCEPT_LANGUAGE, example ="th", required = false) @RequestHeader(value = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, required = false) @RequestHeaderNonNull String acceptLanguage,
            @Parameter(description = CustomerServiceConstant.APP_VERSION, example ="4.0.0", required = false) @RequestHeader(value = CustomerServiceConstant.APP_VERSION, required = false) @RequestHeaderNonNull String appVersion,
            @RequestParam(value = "correlationId") String correlationId
    ) {
        return v1CacheService.deleteUpdateFavoriteCache(correlationId);
    }

}
