package com.tmb.oneapp.customerservice.controller;


import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.EKYCVerifyBioMetricCustomerETEBiometricResult;
import com.tmb.oneapp.customerservice.model.ekycverifywithseven.Biometric;
import com.tmb.oneapp.customerservice.model.ekycverifywithseven.EKYCInquiryResponse;
import com.tmb.oneapp.customerservice.model.ekycverifywithseven.EKYCSubRequest;
import com.tmb.oneapp.customerservice.model.ekycverifywithseven.NDIDVerifyQRResponse;
import com.tmb.oneapp.customerservice.model.ekycverifywithseven.EKYCCancelResponse;
import com.tmb.oneapp.customerservice.model.ekycverifywithseven.NDIDVerifyResultRequest;
import com.tmb.oneapp.customerservice.service.CustomerEKYCVerifyService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class CustomerEKYCVerifyController {
    private static final TMBLogger<CustomerEKYCVerifyController> logger = new TMBLogger<>(CustomerEKYCVerifyController.class);
    private final CustomerEKYCVerifyService customerEKYCVerifyService;

    public CustomerEKYCVerifyController(CustomerEKYCVerifyService customerEKYCVerifyService) {
        this.customerEKYCVerifyService = customerEKYCVerifyService;
    }

    @LogAround
    @Operation(summary = "To inquiry status from EKYC service")
    @PostMapping(value = "/customers/ekyc/inquiry_status", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<EKYCInquiryResponse.NDIDInquiryResult>> inquiryStatus(
            @RequestBody EKYCSubRequest request
    ) throws TMBCommonException {
        try {
            logger.info("");
            TmbOneServiceResponse<EKYCInquiryResponse.NDIDInquiryResult> response = new TmbOneServiceResponse<>();
            HttpHeaders responseHeaders = new HttpHeaders();
            EKYCInquiryResponse.NDIDInquiryResult result = customerEKYCVerifyService.inquiryStatus(request);
            response.setStatus(generateSuccessTmbStatusHeader());
            response.setData(result);
            return ResponseEntity.ok().headers(responseHeaders).body(response);
        } catch (Exception e) {
            logger.error("Unable to inquiry qr code status from EKYC :{}", e.getMessage());
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }

    @LogAround
    @Operation(summary = "Verify result qr code from EKYC Service")
    @PostMapping(value = "/customers/ekyc/verify_result_qr", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<NDIDVerifyQRResponse>> verifyResultQRCode(
            @RequestBody NDIDVerifyResultRequest request
    ) throws TMBCommonException {
        try {
            logger.info("start verify result qr code >>>>>>>>>>");
            TmbOneServiceResponse<NDIDVerifyQRResponse> response = new TmbOneServiceResponse<>();
            HttpHeaders responseHeaders = new HttpHeaders();
            NDIDVerifyQRResponse result = customerEKYCVerifyService.verifyResultQr(request);
            response.setStatus(generateSuccessTmbStatusHeader());
            response.setData(result);
            return ResponseEntity.ok().headers(responseHeaders).body(response);
        } catch (Exception e) {
            logger.error("Unable to verify result qr from EKYC:{}", e.getMessage());
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }

    @LogAround
    @Operation(summary = "Verify add qr code from EKYC Service")
    @PostMapping(value = "/customers/ekyc/verify_add_qr", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<NDIDVerifyQRResponse>> verifyAddQRCode(
            @RequestBody EKYCSubRequest request
    ) throws TMBCommonException {
        try {
            logger.info("start verify add qr code >>>>>>>>>>");
            TmbOneServiceResponse<NDIDVerifyQRResponse> response = new TmbOneServiceResponse<>();
            HttpHeaders responseHeaders = new HttpHeaders();
            NDIDVerifyQRResponse result = customerEKYCVerifyService.verifyAddQrCode(request);
            response.setStatus(generateSuccessTmbStatusHeader());
            response.setData(result);
            return ResponseEntity.ok().headers(responseHeaders).body(response);
        } catch (Exception e) {
            logger.error("Unable to verify add qrcode from EKYC:{}", e.getMessage());
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }

    @LogAround
    @Operation(summary = "To inquiry status from EKYC service")
    @PostMapping(value = "/customers/ekyc/biometric_compare_sevenEleven", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<EKYCVerifyBioMetricCustomerETEBiometricResult>> biometricCompare(
            @RequestHeader(value = "X-Correlation-ID", required = false) String correlationId,
            @RequestBody Biometric biometric
            ) throws TMBCommonException {
        try {
            TmbOneServiceResponse<EKYCVerifyBioMetricCustomerETEBiometricResult> response = new TmbOneServiceResponse<>();
            HttpHeaders responseHeaders = new HttpHeaders();
            EKYCVerifyBioMetricCustomerETEBiometricResult result =
                    customerEKYCVerifyService.biometricCompare(correlationId, biometric);
            response.setStatus(generateSuccessTmbStatusHeader());
            response.setData(result);
            return ResponseEntity.ok().headers(responseHeaders).body(response);
        } catch (Exception e) {
            logger.error("Unable to biometric compare from EKYC :{}", e.getMessage());
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }

    @LogAround
    @Operation(summary = "Cancel request with 7-11")
    @PostMapping(value = "/customers/ekyc/cancel_request", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<EKYCCancelResponse.NDIDCancelResult>> cancelRequest(
            @RequestBody EKYCSubRequest request
    ) throws TMBCommonException {
        try {
            TmbOneServiceResponse<EKYCCancelResponse.NDIDCancelResult> response = new TmbOneServiceResponse<>();
            HttpHeaders responseHeaders = new HttpHeaders();
            EKYCCancelResponse.NDIDCancelResult result = customerEKYCVerifyService.cancelRequest(request);
            response.setStatus(generateSuccessTmbStatusHeader());
            response.setData(result);
            return ResponseEntity.ok().headers(responseHeaders).body(response);
        } catch (Exception e) {
            logger.error("Unable to cancel request from EKYC :{}", e.getMessage());
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }


    TmbStatus generateSuccessTmbStatusHeader() {
        return new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService());
    }

}
