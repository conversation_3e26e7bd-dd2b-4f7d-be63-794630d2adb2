package com.tmb.oneapp.customerservice.controller;

import java.time.Instant;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.base.Strings;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.CustomerProfileImage;
import com.tmb.oneapp.customerservice.service.CustomerProfileImageServiceImpl;

@RestController
/**
 * CustomerProfileImageController request mapping will handle apis call and then
 * navigate to respective method to get customer image url
 *
 */
public class CustomerProfileImageController {
	private static final TMBLogger<CustomersDetailController> logger = new TMBLogger<>(CustomersDetailController.class);
	private final CustomerProfileImageServiceImpl customerProfileImageServiceImpl;

	/**
	 * Constructor
	 * 
	 * @param customerProfileImageRepo
	 */
	@Autowired
	public CustomerProfileImageController(CustomerProfileImageServiceImpl customerProfileImageServiceImpl) {
		super();
		this.customerProfileImageServiceImpl = customerProfileImageServiceImpl;
	}

	/**
	 * Description:- Inquiry crmId
	 * 
	 * @param crmId
	 * @return return customer image url if found
	 */
	@LogAround
	@GetMapping(value = "customer/imageUrl/{crmId}")
	public ResponseEntity<TmbOneServiceResponse<String>> getCustProfileImage(@PathVariable("crmId") String crmId) {
		TmbOneServiceResponse<String> oneServiceResponse = new TmbOneServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		logger.info("CustomerProfileImageController CRM_ID : {}", crmId);
		try {
			if (!Strings.isNullOrEmpty(crmId)) {
				CustomerProfileImage profileImage = customerProfileImageServiceImpl.getCustomerProfileImage(crmId);
				String data = profileImage != null ? profileImage.getImageurl() : CustomerServiceConstant.BLANK;
				oneServiceResponse.setData(data);
				oneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(),
						ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
				return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);

			} else {
				oneServiceResponse.setData(CustomerServiceConstant.CRMID_DATA_NOT_FOUND);
				oneServiceResponse.setStatus(new TmbStatus(ResponseCode.DATA_NOT_FOUND.getCode(),
						ResponseCode.DATA_NOT_FOUND.getMessage(), ResponseCode.DATA_NOT_FOUND.getService()));
				return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
			}

		} catch (Exception e) {
			logger.info("Unable to fetch data from mongo db : {}", e);
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService()));
			return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
		}
	}

}