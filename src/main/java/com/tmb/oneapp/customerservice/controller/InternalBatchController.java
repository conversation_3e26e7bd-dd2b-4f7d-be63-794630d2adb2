package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.service.InternalBatchService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.time.Instant;

@RestController
@Tag(name = "Internal controller for batch job")
public class InternalBatchController {

    private final InternalBatchService internalBatchService;

    @Autowired
    public InternalBatchController(InternalBatchService internalBatchService) {
        this.internalBatchService = internalBatchService;
    }

    @LogAround
    @GetMapping("/customers/internal/check-readiness")
    public ResponseEntity<TmbOneServiceResponse> checkRefIdReadiness(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId
    ) throws TMBCommonException {
        try {
            internalBatchService.checkRefIdReadiness(correlationId);

            TmbOneServiceResponse<String> buildResponse = new TmbOneServiceResponse<>();

            buildResponse.setStatus(getResponseSuccess());
            buildResponse.setData(null);

            return ResponseEntity.ok().headers(getResponseHeaders()).body(buildResponse);
        } catch (Exception e) {
            throw genericTmbException(ResponseCode.FAILED, "Something went wrong, cannot fetch data or send notification", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private HttpHeaders getResponseHeaders() {
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        return responseHeaders;
    }

    private TmbStatus getResponseSuccess() {
        return new TmbStatus(
                ResponseCode.SUCCESS.getCode(),
                ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(),
                null);
    }

    private TMBCommonException genericTmbException(ResponseCode responseCode, String message, HttpStatus status) {
        return new TMBCommonException(
                responseCode.getCode(),
                message,
                responseCode.getService(),
                status,
                null);
    }
}
