package com.tmb.oneapp.customerservice.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.logger.TTBPayloadType;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.CustomerAppLevelAttribute;
import com.tmb.oneapp.customerservice.model.SaveQuickBalanceRequest;
import com.tmb.oneapp.customerservice.service.QuickBalanceService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@Tag(name = "Controller for Customer Settings")
public class SettingController {
    private static final TMBLogger<SettingController> logger = new TMBLogger<>(SettingController.class);

    private QuickBalanceService quickBalanceService;

    public SettingController(QuickBalanceService quickBalanceService) {
        this.quickBalanceService = quickBalanceService;
    }

    @LogAround
    @Operation(summary = "Get Quick balance settings from Customer app level attributes table")
    @GetMapping(value = "/customers/setting/quickbalance", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<CustomerAppLevelAttribute>> getQuickBalance(
            @Parameter(description = "CRMID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = "X-CRMID", required = false) String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @RequestHeader HttpHeaders headers) throws TMBCommonException, JsonProcessingException {

        logger.payload(TTBPayloadType.INBOUND, headers, null);

        CustomerAppLevelAttribute customerAppLevelAttribute = quickBalanceService.get(crmId);
        logger.info("Get quick balance settings success");

        TmbOneServiceResponse<CustomerAppLevelAttribute> response = new TmbOneServiceResponse<>();
        response.setStatus(this.getResponseSuccess());
        response.setData(customerAppLevelAttribute);

        HttpHeaders responseHeader = CustomerServiceUtils.getResponseHeaders();
        logger.payload(TTBPayloadType.OUTBOUND, responseHeader, TMBUtils.convertJavaObjectToString(response));
        return ResponseEntity.ok().headers(responseHeader).body(response);
    }

    @LogAround
    @Operation(summary = "Save quick balance settings for customer")
    @PostMapping(value = "/customers/setting/quickbalance", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<CustomerAppLevelAttribute>> saveQuickBalance(
            @Parameter(description = "CRMID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = "X-CRMID", required = false) String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @RequestBody SaveQuickBalanceRequest saveQuickBalanceRequest,
            @RequestHeader HttpHeaders headers) throws TMBCommonException, JsonProcessingException {

        logger.payload(TTBPayloadType.INBOUND, headers, TMBUtils.convertJavaObjectToString(saveQuickBalanceRequest));

        TmbOneServiceResponse<CustomerAppLevelAttribute> response = new TmbOneServiceResponse<>();
        response.setStatus(this.getResponseSuccess());
        response.setData(quickBalanceService.saveQuickBalance(crmId, saveQuickBalanceRequest));

        HttpHeaders responseHeader = CustomerServiceUtils.getResponseHeaders();

        logger.payload(TTBPayloadType.OUTBOUND, responseHeader, TMBUtils.convertJavaObjectToString(response));
        return ResponseEntity.ok().headers(responseHeader).body(response);
    }

    private TmbStatus getResponseSuccess() {
        return new TmbStatus(
                ResponseCode.SUCCESS.getCode(),
                ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(),
                null);
    }
}
