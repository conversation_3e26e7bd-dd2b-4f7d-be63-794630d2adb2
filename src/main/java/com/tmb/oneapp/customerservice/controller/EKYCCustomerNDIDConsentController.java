package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.EKYCAcceptNdidConsent;
import com.tmb.oneapp.customerservice.service.EKYCCustomerNDIDConsentService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.time.Instant;

/**
 * Controller Class responsible for EKYC Customer NDID Consent
 */
@RestController
public class EKYCCustomerNDIDConsentController {
    private static TMBLogger<EKYCCustomerNDIDConsentController> logger = new TMBLogger<>(
            EKYCCustomerNDIDConsentController.class);
    private final EKYCCustomerNDIDConsentService ekycCustomerNDIDConsentService;

    /**
     * Constructor
     *
     * @param ekycCustomerNDIDConsentService
     */
    @Autowired
    public EKYCCustomerNDIDConsentController(EKYCCustomerNDIDConsentService ekycCustomerNDIDConsentService) {
        this.ekycCustomerNDIDConsentService = ekycCustomerNDIDConsentService;
    }

    @Operation(summary = "Update NDID Consent Result")
    @LogAround
    @PostMapping(value = "/customers/ekyc/prospect/update-NDIDConsent")
    public ResponseEntity<TmbServiceResponse<String>> updateEKYCNDIDConsent(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Valid @RequestBody EKYCAcceptNdidConsent request) throws TMBCommonException {
        logger.info("EKYCCustomerNDIDConsentController  updateEKYCNDIDConsent method start Time : {} ",
                System.currentTimeMillis());
        TmbServiceResponse<String> oneServiceResponse = new TmbServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        try {
            oneServiceResponse.setData(ekycCustomerNDIDConsentService.updateEKYCNdidConsent(request, correlationId));
            oneServiceResponse.setStatus(CustomerServiceUtils.returnSuccessStatus());
            return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
        } catch (TMBCommonException e) {
            logger.error("updateEKYCNDIDConsent : TMBCommonException : {}", e);
            throw e;
        } catch (Exception ex) {
            logger.error("updateEKYCNDIDConsent : Exception : {}", ex);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }

}