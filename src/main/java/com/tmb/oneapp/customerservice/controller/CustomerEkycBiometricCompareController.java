package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.EkycBiometricCompareRequestFormat;
import com.tmb.oneapp.customerservice.model.EkycBiometricCompareResponseFormat;
import com.tmb.oneapp.customerservice.model.ndid.PostBiometricComparisonResponse;
import com.tmb.oneapp.customerservice.model.ndid.EkycBiometricsComparisonRequest;
import com.tmb.oneapp.customerservice.service.CustomerEkycBiometricCompareService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * Controller class responsible for Get Biometric Compare Data
 */
@RestController
@Tag(name = "Get Biometric Compare Data")
public class CustomerEkycBiometricCompareController {

    private static final TMBLogger<CustomerEkycBiometricCompareController> logger = new TMBLogger<>(CustomerEkycBiometricCompareController.class);
    private final CustomerEkycBiometricCompareService customerEkycBiometricCompareService;

    /**
     * Constructor
     *
     * @param customerEkycBiometricCompareService model
     */
    public CustomerEkycBiometricCompareController(CustomerEkycBiometricCompareService customerEkycBiometricCompareService) {
        super();
        this.customerEkycBiometricCompareService = customerEkycBiometricCompareService;
    }

    /**
     * method : To call CustomerEkycBiometricGetService
     *
     * @param requestBody EkycBiometricGetRequestFormat
     * @return EkycBiometricCompareResponseFormat
     */
    @Operation(summary = "Get Ekyc Biometric Compare")
    @PostMapping(value = {"/customers/ekyc/faces-biometric-compare",
            "/customers/ekyc/biomatriccompare"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    @LogAround
    public ResponseEntity<TmbOneServiceResponse<EkycBiometricCompareResponseFormat>> compareEkycFacesBiometric(
            @Parameter(description = "X-Correlation-ID", example = "c28f91e4-881e-4387-a597-4a39c2822b3c")
            @RequestHeader(value = CustomerServiceConstant.HEADER_CORRELATION_ID, required = false) final String correlationId,
            @Valid @RequestBody EkycBiometricCompareRequestFormat requestBody) {

        TmbOneServiceResponse<EkycBiometricCompareResponseFormat> ekycBiometricCompareResponse = new TmbOneServiceResponse<>();

        try {
            requestBody.setCorrelationId(correlationId);
            final EkycBiometricCompareResponseFormat ekycBiometricCompareResponseFormat =
                    customerEkycBiometricCompareService.compareEkycFacesBiometric(requestBody);

            ekycBiometricCompareResponse.setData(ekycBiometricCompareResponseFormat);
            ekycBiometricCompareResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(ekycBiometricCompareResponse);
        } catch (Exception e) {
            logger.error("Unable to getCustomerEkycBiometricCompare data : {} ", e);
            ekycBiometricCompareResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(ekycBiometricCompareResponse);
        }
    }

    @LogAround
    @Operation(summary = "Get Ekyc Prospect Biometric Compare")
    @PostMapping(value = "/customers/ekyc/prospect-biometric-compare",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<PostBiometricComparisonResponse>> compareEkycProspectBiometric(
            @Parameter(description = "X-Correlation-ID", example = "c28f91e4-881e-4387-a597-4a39c2822b3c")
            @RequestHeader(value = CustomerServiceConstant.HEADER_CORRELATION_ID, required = false) final String correlationId,
            @Valid @RequestBody EkycBiometricsComparisonRequest requestBody) {

        TmbOneServiceResponse<PostBiometricComparisonResponse> ekycBiometricCompareResponse = new TmbOneServiceResponse<>();

        try {
            final PostBiometricComparisonResponse ekycBiometricCompareResponseFormat =
                    customerEkycBiometricCompareService.compareEkycProspectBiometric(correlationId, requestBody);

            ekycBiometricCompareResponse.setData(ekycBiometricCompareResponseFormat);
            ekycBiometricCompareResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(ekycBiometricCompareResponse);
        } catch (Exception e) {
            logger.error("Unable to getCustomerEkycBiometricCompare data : {} ", e);
            ekycBiometricCompareResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(ekycBiometricCompareResponse);
        }
    }
}
