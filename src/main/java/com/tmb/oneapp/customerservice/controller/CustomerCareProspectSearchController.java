package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Description;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.prospect.EKYCProspectSearchProspectCustomerDataResponse;
import com.tmb.oneapp.customerservice.model.prospect.ProspectCustomerNotPassVerificationUpdateRequest;
import com.tmb.oneapp.customerservice.model.prospect.ProspectCustomerSearchRequest;
import com.tmb.oneapp.customerservice.service.CustomerEkycProspectService;
import com.tmb.oneapp.customerservice.service.SearchCustomerProspectService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.List;

/**
 * Controller responsible for Customer EKYC
 */
@RestController
@Tag(name = "Customer EKYC Api")
public class CustomerCareProspectSearchController {
    private static TMBLogger<CustomerCareProspectSearchController> logger = new TMBLogger<>(CustomerCareProspectSearchController.class);

    private final SearchCustomerProspectService searchCustomerProspectService;
    private final CustomerEkycProspectService customerEkycProspectService;


    /**
     * Constructor
     *
     * @param searchCustomerProspectService
     * @param customerEkycProspectService
     */


    @Autowired
    public CustomerCareProspectSearchController(SearchCustomerProspectService searchCustomerProspectService, CustomerEkycProspectService customerEkycProspectService) {

        this.searchCustomerProspectService = searchCustomerProspectService;

        this.customerEkycProspectService = customerEkycProspectService;
    }

    /**
     * customer prospect search api
     **/
    @Operation(summary = " Customer care Prospect  Search based on Citizen id , mobile-no and name(by both Thai and Englis).")
    @LogAround
    @PostMapping(value = "/customers/prospect/search")
    public ResponseEntity<TmbServiceResponse<List<EKYCProspectSearchProspectCustomerDataResponse>>> prospectCustomerSearch(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @RequestBody ProspectCustomerSearchRequest request)
            throws TMBCommonException {
        logger.info("CustomerEkycController  searchCustomerProspectBasedOnDeviceData method start Time : {} ",
                System.currentTimeMillis());
        HttpHeaders responseHeaders = new HttpHeaders();
        TmbServiceResponse<List<EKYCProspectSearchProspectCustomerDataResponse>> oneServiceResponse = new TmbServiceResponse<>();
        responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        List<EKYCProspectSearchProspectCustomerDataResponse> res = null;
        Status status;

        if (CustomerServiceConstant.MOBILE_NO.equals(request.getProspectSearchType())
                && !request.getProspectSearchValue().matches("^(06|08|09)\\d{8}$")) {
            oneServiceResponse.setStatus(returnFailStatus(ResponseCode.INVALID_DATA_ERROR));
            return ResponseEntity.badRequest().body(oneServiceResponse);
        }

        if (CustomerServiceConstant.PROSPECT_ID_SEARCH.equals(request.getProspectSearchType())) {
            res = customerEkycProspectService.getCustomerProspectDataByProspectId(request.getProspectSearchValue());
        } else {
            res = searchCustomerProspectService.searchCustomerProspect(request);
        }

        if (res.stream().count() == 0) {
            status = returnFailStatus(ResponseCode.DATA_NOT_FOUND);
        }else{
            status = returnSuccessStatus();
        }
        oneServiceResponse.setData(res);
        oneServiceResponse.setStatus(status);

        if (status.getCode().equals(ResponseCode.SUCCESS.getCode())) {
            return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
        }


        return ResponseEntity.badRequest().body(oneServiceResponse);
    }

    private Status returnSuccessStatus() {
        return new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(), CustomerServiceUtils.returnSuccessDescription());
    }

    private Status returnFailStatus(ResponseCode r) {
        return new Status(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                ResponseCode.FAILED.getService(), new Description(r.getMessage(), r.getMessage()));
    }

}
