package com.tmb.oneapp.customerservice.controller;

import com.google.common.base.Strings;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.EbStatuDetailsModel;
import com.tmb.oneapp.customerservice.model.activity.ActivityLogSearchRequest;
import com.tmb.oneapp.customerservice.service.CustomerStatusDetailsServiceImpl;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
public class CustomerEbStatusDetailController {

    private static final TMBLogger<CustomerEbStatusDetailController> logger = new TMBLogger<>(
            CustomerEbStatusDetailController.class);
    private final CustomerStatusDetailsServiceImpl customerStatusDetailsServiceImpl;

    /**
     * Constructor customerStatusDetailsServiceImpl
     *
     * @param customerStatusDetailsServiceImpl;
     */
    @Autowired
    public CustomerEbStatusDetailController(CustomerStatusDetailsServiceImpl customerStatusDetailsServiceImpl) {
        super();
        this.customerStatusDetailsServiceImpl = customerStatusDetailsServiceImpl;
    }

    /**
     * @param request Crm Id
     * @return get the status detail
     */
    @PostMapping(value = "/customers/eb-status/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<EbStatuDetailsModel>> getEbStatusDetailsCrmId(
            @Parameter(description = "X-Correlation-ID", example = "c28f91e4-881e-4387-a597-4a39c2822b3c", required = true)
            @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) final String correlationId,
            @RequestBody @Valid ActivityLogSearchRequest request) throws TMBCommonException {

        HttpHeaders responseHeaders = new HttpHeaders();
        TmbOneServiceResponse<EbStatuDetailsModel> oneServiceResponse = new TmbOneServiceResponse<>();
        logger.info("CustomerEbStatusDetailController request : {}", request);

        String crmId = request.getCrmId();
        if (!Strings.isNullOrEmpty(crmId)) {
            crmId = CustomerServiceUtils.fillUpCrmIdFormat(crmId);
            logger.info("CustomerEbStatusDetailController crmId : {}", crmId);

            // call database
            EbStatuDetailsModel data = customerStatusDetailsServiceImpl.getHistoryForChangeStatus(correlationId, request);

            oneServiceResponse.setData(data);
            oneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));

            return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);


        } else {
            logger.error("Received Invalid crmId {}", crmId);
            oneServiceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService()));
            return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
        }

    }

}
