package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Description;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.commonfr.CommonFRBlockResponse;
import com.tmb.oneapp.customerservice.service.CommonFRBlockService;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
public class CommonFRGetBlockController {
    private static final TMBLogger<CommonFRGetBlockController> logger = new TMBLogger<>(CommonFRGetBlockController.class);
    private final CommonFRBlockService commonFRBlockService;

    public CommonFRGetBlockController(CommonFRBlockService commonFRBlockService) {
        this.commonFRBlockService = commonFRBlockService;
    }


    @LogAround
    @GetMapping(value = "/common-fr/check-block", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<CommonFRBlockResponse>> getCommonFRCheckBlock(
            @Parameter(description = CustomerServiceConstant.HEADERS_X_CRMID, example ="001100000000000000000001184383", required = true) @Valid @RequestHeader(name = CustomerServiceConstant.HEADERS_X_CRMID) String crmId,
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId
    ) throws TMBCommonException {
        TmbServiceResponse<CommonFRBlockResponse> response = new TmbServiceResponse<>();
        logger.info("### START /common-fr/check-block ###");
        response.setData(commonFRBlockService.getCommonFRCheckBlock(crmId, correlationId));
        response.setStatus(getStatusSuccess());
        logger.info("### END /common-fr/check-block ###");
        return ResponseEntity.ok(response);
    }

    private Status getStatusSuccess() {
        Description description = new Description(ResponseCode.SUCCESS.getDesc(), ResponseCode.SUCCESS.getDesc());
        return new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), description);
    }
}
