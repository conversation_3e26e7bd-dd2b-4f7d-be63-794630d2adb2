package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.mongodb.customer.model.NdidInfoEntity;
import com.tmb.oneapp.customerservice.service.EkycNDIDService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.CORRELATION_ID;

@RestController
public class V1EKYCNdidController {
    private static final TMBLogger<V1EKYCNdidController> logger = new TMBLogger<>(V1EKYCNdidController.class);
    private final EkycNDIDService ekycNDIDService;

    public V1EKYCNdidController(EkycNDIDService ekycNDIDService) {
        this.ekycNDIDService = ekycNDIDService;
    }


    @Operation(summary = "Get NDID conditions")
    @LogAround
    @PostMapping(value = "/ekyc/get-ndid-info")
    public ResponseEntity<TmbOneServiceResponse<List<NdidInfoEntity>>> getNdidInfo(
            @Parameter(description = CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @RequestHeader(CORRELATION_ID) String correlationId) {

        TmbOneServiceResponse<List<NdidInfoEntity>> response = new TmbOneServiceResponse<>();

        try {
            List<NdidInfoEntity> res = ekycNDIDService.getNdidInfo(correlationId);

            response.setData(res);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(),
                    ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));

            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(response);

        } catch (Exception e) {
            logger.error("Error calling GET /customers/ekyc/ndidCondition : {}", e);
            response.setStatus(new TmbStatus(ResponseCode.GENERAL_ERROR.getCode(),
                    ResponseCode.GENERAL_ERROR.getMessage(), ResponseCode.GENERAL_ERROR.getService()));
            return ResponseEntity.badRequest().headers(TMBUtils.getResponseHeaders()).body(response);
        }

    }
}
