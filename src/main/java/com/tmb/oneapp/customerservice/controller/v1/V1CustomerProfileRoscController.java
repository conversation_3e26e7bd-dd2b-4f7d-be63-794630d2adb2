package com.tmb.oneapp.customerservice.controller.v1;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.rosccustomerprofile.ProfileRoscResponse;
import com.tmb.oneapp.customerservice.model.rosccustomerprofile.creation.RoscCreationRequest;
import com.tmb.oneapp.customerservice.service.V1CreateProfileRoscService;
import com.tmb.oneapp.customerservice.model.rosccustomerprofile.maintenance.RoscMaintenanceRequest;
import com.tmb.oneapp.customerservice.service.V1CustomerProfileRoscMaintenanceService;
import com.tmb.oneapp.customerservice.service.V1CustomerProfileRoscService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import com.google.common.base.Strings;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import javax.xml.stream.XMLStreamException;
import java.io.IOException;

@RestController
@Tag(name = "Customer Profile ROSC Api V1")
public class V1CustomerProfileRoscController {

    private final V1CustomerProfileRoscService v1CustomerProfileRoscService;
    private final V1CustomerProfileRoscMaintenanceService v1CustomerProfileRoscMaintenanceService;
    private final V1CreateProfileRoscService v1CreateProfileRoscService;

    public static final TMBLogger<V1CustomerProfileRoscController> logger = new TMBLogger<>(V1CustomerProfileRoscController.class);
    public V1CustomerProfileRoscController(V1CustomerProfileRoscService v1CustomerProfileRoscService,
                                           V1CustomerProfileRoscMaintenanceService v1CustomerProfileRoscMaintenanceService, V1CreateProfileRoscService v1CreateProfileRoscService) {
        this.v1CustomerProfileRoscService = v1CustomerProfileRoscService;
        this.v1CustomerProfileRoscMaintenanceService = v1CustomerProfileRoscMaintenanceService;
        this.v1CreateProfileRoscService = v1CreateProfileRoscService;
    }

    @Operation(summary = "V1 Customer Profile ROSC")
    @LogAround
    @GetMapping(value = "/profiles/rosc")
    public ResponseEntity<TmbServiceResponse<ProfileRoscResponse>> getCustomerProfileRosc(
            @Parameter(description = "x-correlation-id", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @Valid @RequestHeader(CustomerServiceConstant.CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = "CRM ID", example = "001100000000000000000012004030", required = true)
            @Valid @RequestHeader(CustomerServiceConstant.X_HEADER_CRMID) @RequestHeaderNonNull String crmId) throws TMBCommonException, XMLStreamException, IOException {

        this.validateHeader(CustomerServiceConstant.CORRELATION_ID, CustomerServiceConstant.X_COR_LENGTH, correlationId);
        this.validateHeader(CustomerServiceConstant.X_HEADER_CRMID, CustomerServiceConstant.RM_ID_LENGTH, crmId);
        return ResponseEntity.ok()
                .headers(TMBUtils.getResponseHeaders())
                .body(v1CustomerProfileRoscService.getCustomerProfileRosc(correlationId, crmId));

    }

    @Operation(summary = "V1 Customer Profile ROSC Creation")
    @LogAround
    @PostMapping(value = "/profiles/rosc-creation")
    public ResponseEntity<TmbServiceResponse<String>> createCustomerProfileRosc(
            @Parameter(description = "x-correlation-id", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader(CustomerServiceConstant.CORRELATION_ID) String correlationId,
            @RequestBody RoscCreationRequest roscCreationRequest) throws TMBCommonException, XMLStreamException, JsonProcessingException {
        this.validateHeader(CustomerServiceConstant.CORRELATION_ID, CustomerServiceConstant.X_COR_LENGTH, correlationId);

        this.validateRequest(CustomerServiceConstant.RM_ID_VALUE, CustomerServiceConstant.RM_ID_LENGTH, roscCreationRequest.getRmId());
        this.validateRequest(CustomerServiceConstant.FULL_NAME, CustomerServiceConstant.FULL_NAME_LENGTH, roscCreationRequest.getFullName());
        this.validateRequest(CustomerServiceConstant.CARD_TYPE, CustomerServiceConstant.CARD_TYPE_LENGTH, roscCreationRequest.getCardType());
        this.validateRequest(CustomerServiceConstant.ID_CARD, CustomerServiceConstant.ID_CARD_LENGTH, roscCreationRequest.getIdCard());


        return ResponseEntity.ok()
                .headers(TMBUtils.getResponseHeaders())
                .body(v1CreateProfileRoscService.createCustomerProfileRosc(correlationId, roscCreationRequest));
    }

    @Operation(summary = "V1 ROSC Maintenance")
    @LogAround
    @PostMapping(value = "/profiles/rosc-maintenance", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<String>> getProfilesRoscMaintenance(
            @Parameter(description = "x-correlation-id", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader(CustomerServiceConstant.CORRELATION_ID) String correlationId,
            @RequestBody RoscMaintenanceRequest request) throws TMBCommonException {

        try {
            this.validateHeader(CustomerServiceConstant.CORRELATION_ID, CustomerServiceConstant.X_COR_LENGTH, correlationId);

            this.validateRequest(CustomerServiceConstant.CUST_ID, CustomerServiceConstant.CUST_ID_LENGTH, request.getCustId());
            this.validateRequest(CustomerServiceConstant.RM_ID_VALUE, CustomerServiceConstant.RM_ID_LENGTH, request.getRmId());
            this.validateRequest(CustomerServiceConstant.CSTA_PREVIOUS, CustomerServiceConstant.CSTA_LENGTH, request.getCustStatus().getCstaStatusPrevious());
            this.validateRequest(CustomerServiceConstant.CSTA_CURREENT, CustomerServiceConstant.CSTA_LENGTH, request.getCustStatus().getCstaStatusCurrent());

            return ResponseEntity.ok()
                    .headers(TMBUtils.getResponseHeaders())
                    .body(v1CustomerProfileRoscMaintenanceService.getRoscMaintenance(request, correlationId));

        } catch (TMBCommonException ex) {
            throw ex;
        } catch (Exception e) {
            throw new TMBCommonException(ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK, null);
        }
    }

    void validateRequest(String fieldName, int size, String request) throws TMBCommonException {
        if (Strings.isNullOrEmpty(request) || request.length() > size) {
            logger.error("ERROR invalid body parameter");
            throw new TMBCommonException(ResponseCode.ERROR_8000.getCode(),
                    String.format(ResponseCode.ERROR_8000.getMessage(), fieldName),
                    ResponseCode.ERROR_8000.getService(),
                    HttpStatus.BAD_REQUEST, null);
        }
    }
    void validateHeader(String fieldName, int size, String request) throws TMBCommonException {
        if (Strings.isNullOrEmpty(request) || request.length() > size) {
            logger.error("ERROR invalid header parameter");
            throw new TMBCommonException(ResponseCode.ERROR_8001.getCode(),
                    String.format(ResponseCode.ERROR_8001.getMessage(), fieldName),
                    ResponseCode.ERROR_8001.getService(),
                    HttpStatus.BAD_REQUEST, null);
        }
    }
}
