package com.tmb.oneapp.customerservice.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.ActivityFatcaCalRequest;
import com.tmb.oneapp.customerservice.model.CustomerAccountETERequest;
import com.tmb.oneapp.customerservice.model.CustomerAccountETEResponse;
import com.tmb.oneapp.customerservice.model.CustomerAccountNoETERequest;
import com.tmb.oneapp.customerservice.model.CustomerUtilFatcaRequest;
import com.tmb.oneapp.customerservice.model.GenerateAccountNoResponse;
import com.tmb.oneapp.customerservice.model.activity.DynamicActivityLogRequest;
import com.tmb.oneapp.customerservice.service.CreateCustomerAccountNoService;
import com.tmb.oneapp.customerservice.service.CustomerCreateAccountService;
import com.tmb.oneapp.customerservice.service.CustomerUtilLogService;
import com.tmb.oneapp.customerservice.service.CustomerUtilService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.time.Instant;

@RestController
@Tag(name = "Customer Utilities Api")
public class CustomerUtilController {
    private static final TMBLogger<CustomerUtilController> logger = new TMBLogger<>(CustomerUtilController.class);
    private final CustomerUtilService customerUtilService;
    private final CustomerUtilLogService customerUtilLogService;
    private final CreateCustomerAccountNoService createCustomerAccountNoService;
    private final CustomerCreateAccountService customerCreateAccountService;

    @Autowired
    public CustomerUtilController(CustomerUtilService customerUtilService,
                                  CustomerUtilLogService customerUtilLogService,
                                  CreateCustomerAccountNoService createCustomerAccountNoService,
                                  CustomerCreateAccountService customerCreateAccountService) {
        this.customerUtilService = customerUtilService;
        this.customerUtilLogService = customerUtilLogService;
        this.createCustomerAccountNoService = createCustomerAccountNoService;
        this.customerCreateAccountService = customerCreateAccountService;
    }

    @Operation(summary = "Calculate Fatca answer")
    @LogAround
    @PostMapping(value = "/customers/util/fatca/calculate")
    public ResponseEntity<TmbServiceResponse<String>> calculateFatcaAnswer(
            @RequestBody CustomerUtilFatcaRequest request) throws TMBCommonException {
        logger.info("Calculate Fatca answer request : {} ", request);
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        try {
            customerUtilService.calculateFatcaAwser(request);
            return ResponseEntity.ok().headers(responseHeaders).body(createResponseSuccess());
        } catch (TMBCommonException e) {
            logger.error("Calculate Fatca answer : TMBCommonException : {}", e);
            throw e;
        } catch (Exception e) {
            logger.error("Calculate Fatca answer : Exception : {}", e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }

    @Operation(summary = "Calculate Fatca answer - Activity Log")
    @LogAround
    @PostMapping(value = "/customers/util/fatca/activityLog")
    public ResponseEntity<TmbServiceResponse<String>> calculateFatcaAnswerActLog(
            @Parameter(description = "X-Correlation-ID",
                    example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "CRMID", example = "001100000000000000000012017238", required = true)
            @RequestHeader(name = "X-CRMID") String crmId,
            @RequestBody ActivityFatcaCalRequest request,
            @RequestHeader HttpHeaders header
    ) throws TMBCommonException {
        logger.info("Validate and write activity log : {} ", System.currentTimeMillis());
        HttpHeaders responseHeaders = new HttpHeaders();
        try {
            logger.info("logForFatcaCalOpenAccount : {} ", crmId + " " + correlationId);
            customerUtilLogService.logForFatcaCalOpenAccount
                    (header, request.getFatcaFlag(), request.getProductCode(), request.getFlowName());
            return ResponseEntity.ok().headers(responseHeaders).body(createResponseSuccess());
        } catch (Exception e) {
            logger.error("calculateFatcaAnswerActLog : Exception : {}", e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }

    @Operation(summary = "Activity log deeplink  - Activity Log")
    @LogAround
    @PostMapping(value = "/customers/util/deeplink/activityLog")
    public ResponseEntity<TmbServiceResponse<String>> deepLinkActLog(
            @Parameter(description = "X-Correlation-ID",
                    example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "CRMID", example = "001100000000000000000012017238", required = true)
            @RequestHeader(name = "X-CRMID") String crmId,
            @RequestBody ActivityFatcaCalRequest request,
            @RequestHeader HttpHeaders header
    ) throws TMBCommonException {
        HttpHeaders responseHeaders = new HttpHeaders();
        try {
            logger.info("deepLinkActLog : {} ", crmId + " " + correlationId);
            customerUtilLogService.logForDeeplinkOpenAccount
                    (header, request.getLinkFrom(), request.getFlowName(), request.getProductCode());
            return ResponseEntity.ok().headers(responseHeaders).body(createResponseSuccess());
        } catch (Exception e) {
            logger.error("deepLinkActLog : Exception : {}", e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }

    @Operation(summary = "Generate Deposit Account No")
    @LogAround
    @PostMapping("/customers/openAccount/generate_account_number")
    public ResponseEntity<TmbOneServiceResponse<GenerateAccountNoResponse>> generateAccNo(
            @Valid @RequestBody CustomerAccountNoETERequest request) throws TMBCommonException {

        logger.info("START Generate Deposit Acct No : {} ", System.currentTimeMillis());
        TmbOneServiceResponse<GenerateAccountNoResponse> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        try {
            GenerateAccountNoResponse data = createCustomerAccountNoService.generateAccNo(request);
            logger.info("[OUT] :: Generate Account Number : {}", data.toString());
            response.setData(data);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
            return ResponseEntity.ok().headers(responseHeaders).body(response);
        } catch (Exception e) {
            logger.error("[Error] :: Generate Account Number  : Exception : {}", e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }

    @Operation(summary = "Call ETE for Create Deposit Account")
    @LogAround
    @PostMapping("/customers/openAccount/create_account")
    public ResponseEntity<TmbOneServiceResponse<CustomerAccountETEResponse>> createDepositAccountCallETE(
            @Valid @RequestBody CustomerAccountETERequest request) throws TMBCommonException, JsonProcessingException {
        logger.info("[START] Create Deposit Account : {} ", System.currentTimeMillis());
        TmbOneServiceResponse<CustomerAccountETEResponse> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        CustomerAccountETEResponse result = customerCreateAccountService.createDepositAccount(request);
        response.setData(result);
        logger.info("[OUT] :: Create Account : SUCCESS");
        response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
        return ResponseEntity.ok().headers(responseHeaders).body(response);

    }

    @Operation(summary = "Call ETE for Create Account")
    @LogAround
    @PostMapping(value = {"/customers/openDreamSavingAccount/create_account",
            "/customers/openSecondAccount/create_account"})
    public ResponseEntity<TmbOneServiceResponse<CustomerAccountETEResponse>> createAccountCallETE(
            @Valid @RequestBody CustomerAccountETERequest request) throws TMBCommonException, JsonProcessingException {
        logger.info("[START] Create Dream Saving Account : {} ", System.currentTimeMillis());
        TmbOneServiceResponse<CustomerAccountETEResponse> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        CustomerAccountETEResponse result = customerCreateAccountService.createAccount(request);
        response.setData(result);
        logger.info("[OUT] :: Create Dream Saving : SUCCESS");
        response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
        return ResponseEntity.ok().headers(responseHeaders).body(response);
    }

    @Operation(summary = "Call ETE for Create FCD Account")
    @LogAround
    @PostMapping("/customers/openFcdAccount/create_account")
    public ResponseEntity<TmbOneServiceResponse<CustomerAccountETEResponse>> createFcdAccountCallETE(
            @Valid @RequestBody CustomerAccountETERequest request) throws TMBCommonException, JsonProcessingException {
        logger.info("[START] Create Deposit Account : {} ", System.currentTimeMillis());
        TmbOneServiceResponse<CustomerAccountETEResponse> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeaders = new HttpHeaders();
        CustomerAccountETEResponse result = customerCreateAccountService.createFcdAccount(request);
        response.setData(result);
        logger.info("[OUT] :: Create Account : SUCCESS");
        response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
        return ResponseEntity.ok().headers(responseHeaders).body(response);

    }

    @Operation(summary = "Dynamic activity log service")
    @LogAround
    @PostMapping(value = "/customers/util/dynamic/activityLog")
    public ResponseEntity<TmbServiceResponse<String>> dynamicActivity(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid
            @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
            @Parameter(description = "CRMID", example = "001100000000000000000012017238", required = true)
            @RequestHeader(name = "X-CRMID") String crmId,
            @RequestBody DynamicActivityLogRequest request,
            @RequestHeader HttpHeaders header
    ) throws TMBCommonException {
        HttpHeaders responseHeaders = new HttpHeaders();
        try {
            logger.info("dynamicActivity : {} ", crmId + " " + correlationId);
            customerUtilLogService.dynamicActivityLog(header, request);
            return ResponseEntity.ok().headers(responseHeaders).body(createResponseSuccess());
        } catch (Exception e) {
            logger.error("dynamicActivity : Exception : {}", e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }

    public TmbServiceResponse<String> createResponseSuccess() {
        TmbServiceResponse<String> oneServiceResponse = new TmbServiceResponse<>();
        logger.info("createResponseSuccess : Success");
        oneServiceResponse.setStatus(new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(), CustomerServiceUtils.returnSuccessDescription()));
        oneServiceResponse.setData(CustomerServiceConstant.CUSTOMER_STATUS_RESPONSE_SUSSESS);

        return oneServiceResponse;
    }

}
