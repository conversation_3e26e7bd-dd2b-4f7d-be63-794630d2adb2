package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Description;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.personalizedinformation.PersonalizedInformationMapper;
import com.tmb.oneapp.customerservice.model.personalizedinformation.model.PersonalizedInformationRequest;
import com.tmb.oneapp.customerservice.model.personalizedinformation.model.PersonalizedInformationResponse;
import com.tmb.oneapp.customerservice.service.PersonalizedInformationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import jakarta.validation.constraints.NotNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/personalized-information")
public class V1PersonalizedInformationController {
    private static final TMBLogger<V1PersonalizedInformationController> logger = new TMBLogger<>(V1PersonalizedInformationController.class);
    private final PersonalizedInformationService personalizedInformationService;

    public V1PersonalizedInformationController(PersonalizedInformationService personalizedInformationService) {
        this.personalizedInformationService = personalizedInformationService;
    }

    @LogAround
    @Operation(summary = "get personalized information")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.X_CRMID, description = CustomerServiceConstant.X_CRMID, example ="c28f91e4-881r-4387-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="c28f91e5-881e-4386-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_APP_VERSION, description = CustomerServiceConstant.HEADER_APP_VERSION, example ="8.0.0", in = ParameterIn.HEADER, required = true)
    })
    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<List<PersonalizedInformationResponse>>> getPersonalizedInformation(
            @RequestHeader HttpHeaders headers,
            @RequestHeader(CustomerServiceConstant.X_CRMID) @NotNull String crmId
    ) {
        logger.info("##### START Get Personalized Information [CRM ID: {}] ", crmId);
        TmbServiceResponse<List<PersonalizedInformationResponse>> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        response.setData(PersonalizedInformationMapper.INSTANCE.toPersonalizedInformationResponseList(personalizedInformationService.getPersonalizedInformationByCrmId(crmId)));
        logger.info("##### END /personalized-information");

        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "get personalized information by id")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.X_CRMID, description = CustomerServiceConstant.X_CRMID, example ="c28f91e4-881r-4387-a597-4a39c2822b2c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="c28f91e5-883e-4386-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_APP_VERSION, description = CustomerServiceConstant.HEADER_APP_VERSION, example ="8.0.0", in = ParameterIn.HEADER, required = true)
    })
    @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<PersonalizedInformationResponse>> getPersonalizedInformationById(
            @RequestHeader HttpHeaders headers,
            @RequestHeader(CustomerServiceConstant.X_CRMID) @NotNull String crmId,
            @PathVariable("id")String id
    ) {
        logger.info("##### START Get Personalized Information By ID [CRM ID: {}, ID: {}] ", crmId, id);
        TmbServiceResponse<PersonalizedInformationResponse> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        response.setData(PersonalizedInformationMapper.INSTANCE.toPersonalizedInformationResponse(personalizedInformationService.getPersonalizedInformationByCrmIdAndPersonalizedId(crmId, id)));
        logger.info("##### END /personalized-information/{id}");

        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "create personalized information")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.X_CRMID, description = CustomerServiceConstant.X_CRMID, example ="c28f91e4-881r-4387-a597-4a39c2842b2c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="c28f91e7-883e-4386-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_APP_VERSION, description = CustomerServiceConstant.HEADER_APP_VERSION, example ="8.0.0", in = ParameterIn.HEADER, required = true)
    })
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<String>> createPersonalizedInformationById(
            @RequestHeader HttpHeaders headers,
            @RequestHeader(CustomerServiceConstant.X_CRMID) @NotNull String crmId
    ) {
        logger.info("##### START Create Personalized Information [CRM ID: {}] ", crmId);
        personalizedInformationService.createPersonalizedInformation(crmId);
        TmbServiceResponse<String> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        response.setData(ResponseCode.SUCCESS.getMessage());
        logger.info("##### END /personalized-information   create");

        return ResponseEntity.ok(response);
    }


    @LogAround
    @Operation(summary = "update personalized information by id")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.X_CRMID, description = CustomerServiceConstant.X_CRMID, example ="c28f91e4-881r-4387-a597-4a39c2822b2c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="c28f91e7-183e-4386-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_APP_VERSION, description = CustomerServiceConstant.HEADER_APP_VERSION, example ="8.0.0", in = ParameterIn.HEADER, required = true)
    })
    @PutMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<String>> updatePersonalizedInformationById(
            @RequestHeader HttpHeaders headers,
            @RequestHeader(CustomerServiceConstant.X_CRMID) @NotNull String crmId,
            @RequestBody PersonalizedInformationRequest personalizedInformationRequest,
            @PathVariable("id") String id
            ) throws TMBCommonException {
        logger.info("##### START Create Personalized Information [CRM ID: {}] ", crmId);
        personalizedInformationService.updatePersonalizedInformation(crmId, id, personalizedInformationRequest);
        TmbServiceResponse<String> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        response.setData(ResponseCode.SUCCESS.getMessage());
        logger.info("##### END /personalized-information   create");

        return ResponseEntity.ok(response);
    }


    @LogAround
    @Operation(summary = "delete personalized information")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.X_CRMID, description = CustomerServiceConstant.X_CRMID, example ="c28f91e4-881r-4387-a597-4a39c5842b2c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="c28f91g7-883e-4386-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_APP_VERSION, description = CustomerServiceConstant.HEADER_APP_VERSION, example ="8.0.0", in = ParameterIn.HEADER, required = true)
    })
    @DeleteMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<String>> deletePersonalizedInformationById(
            @RequestHeader HttpHeaders headers,
            @RequestHeader(CustomerServiceConstant.X_CRMID) @NotNull String crmId,
            @PathVariable("id") String id
    ) {
        logger.info("##### START Delete Personalized Information by Id [CRM ID: {}, ID: {}] ", crmId, id);
        personalizedInformationService.deletePersonalizedInformation(crmId, id);
        TmbServiceResponse<String> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        response.setData(ResponseCode.SUCCESS.getMessage());
        logger.info("##### END /personalized-information   delete");

        return ResponseEntity.ok(response);
    }

    private Status getStatusSuccess() {
        Description description = new Description(ResponseCode.SUCCESS.getDesc(), ResponseCode.SUCCESS.getDesc());
        return new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), description);
    }
}
