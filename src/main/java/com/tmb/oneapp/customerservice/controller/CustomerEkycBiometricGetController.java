package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.EKYCGetImageETEBiometricResponse;
import com.tmb.oneapp.customerservice.model.EkycBiometricGetRequestFormat;
import com.tmb.oneapp.customerservice.model.EkycBiometricGetResponseFormat;
import com.tmb.oneapp.customerservice.service.CustomerEkycBiometricGetService;
import com.tmb.oneapp.customerservice.service.GetCustomerImageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller class responsible for Get Biometric Get Data
 */
@RestController
@Tag(name = "Get Biometric Get Data")
public class CustomerEkycBiometricGetController {

    private static final TMBLogger<CustomerEkycBiometricGetController> logger =
            new TMBLogger<>(CustomerEkycBiometricGetController.class);
    private final CustomerEkycBiometricGetService customerEkycBiometricGetService;
    private final GetCustomerImageService getCustomerImageService;

    /**
     * Constructor
     *
     * @param customerEkycBiometricGetService model
     */
    public CustomerEkycBiometricGetController(CustomerEkycBiometricGetService customerEkycBiometricGetService,
                                              GetCustomerImageService getCustomerImageService) {
        super();
        this.customerEkycBiometricGetService = customerEkycBiometricGetService;
        this.getCustomerImageService = getCustomerImageService;
    }

    /**
     * method : To call CustomerEkycBiometricGetService
     *
     * @param requestBody EkycBiometricGetRequestFormat
     * @return EkycBiometricGetResponseFormat
     */
    @Operation(summary = "Get Ekyc Biometric Get")
    @PostMapping(value = "/customers/ekyc/biometric", produces = MediaType.APPLICATION_JSON_VALUE)
    @LogAround
    public ResponseEntity<TmbOneServiceResponse<EkycBiometricGetResponseFormat>> getEkycFacesBiometricComparison(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da")
            @Valid @RequestHeader(value = "X-Correlation-ID", required = false) String correlationId,
            @Valid @RequestBody EkycBiometricGetRequestFormat requestBody) {

        TmbOneServiceResponse<EkycBiometricGetResponseFormat> customerEkycBiometricGetResponse = new TmbOneServiceResponse<>();

        try {
            requestBody.setCorrelationId(correlationId);
            final EkycBiometricGetResponseFormat ekycBiometricGetResponseFormat =
                    customerEkycBiometricGetService.getEkycFacesBiometricComparison(requestBody);

            customerEkycBiometricGetResponse.setData(ekycBiometricGetResponseFormat);
            customerEkycBiometricGetResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(),
                    ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(customerEkycBiometricGetResponse);
        } catch (Exception e) {
            logger.error("Unable to getCustomerEkycBiometricGet data : {} ", e);
            customerEkycBiometricGetResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(customerEkycBiometricGetResponse);
        }
    }

    @Operation(summary = "Get Ekyc Biometric Get")
    @GetMapping(value = "/customers/ekyc/get-image", produces = MediaType.APPLICATION_JSON_VALUE)
    @LogAround
    public ResponseEntity<TmbOneServiceResponse<EKYCGetImageETEBiometricResponse>> getCustomerEkycGetImage(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da")
            @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @Valid @RequestHeader("Citizen-Id") String citizenId) {

        TmbOneServiceResponse<EKYCGetImageETEBiometricResponse> response = new TmbOneServiceResponse<>();

        try {
            final EKYCGetImageETEBiometricResponse result =
                    getCustomerImageService.getCustomerImage(correlationId, citizenId);

            response.setData(result);
            response.setStatus
                    (new TmbStatus(ResponseCode.SUCCESS.getCode(),
                            ResponseCode.SUCCESS.getMessage(),
                            ResponseCode.SUCCESS.getService(),
                            ResponseCode.SUCCESS.getDesc()));
            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(response);
        } catch (Exception e) {
            logger.error("Unable to getCustomerEkycGetImage data : {} ", e);
            response.setStatus
                    (new TmbStatus(ResponseCode.FAILED.getCode(),
                            ResponseCode.FAILED.getMessage(),
                            ResponseCode.FAILED.getService(),
                            ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(response);
        }
    }

}
