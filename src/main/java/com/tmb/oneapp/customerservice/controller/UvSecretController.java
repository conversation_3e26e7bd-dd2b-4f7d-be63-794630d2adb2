package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.GetSecretRequest;
import com.tmb.oneapp.customerservice.model.GetSecretResponse;
import com.tmb.oneapp.customerservice.model.GetSecretUvFeignRequest;
import com.tmb.oneapp.customerservice.service.CustomerUvService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * Class responsible to handles to get the secret key from uv service
 */
@RestController
@Tag(name = "To get secreat key by getSecret request object")
public class UvSecretController {
    private final CustomerUvService customerUvService;
    private static final TMBLogger<UvSecretController> logger = new TMBLogger<>(UvSecretController.class);

    /**
     * @param customerUvService Constructor to invoke the service
     */
    public UvSecretController(CustomerUvService customerUvService) {
        this.customerUvService = customerUvService;
    }


    /**
     * @param header pass header values
     * @return uv secret details from uv service
     */
    @Operation(summary = "Api to get customer uv service")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = "Correlation ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da")})
    @PostMapping(value = "/customers/uvsecret", produces = MediaType.APPLICATION_JSON_VALUE)
    @LogAround
    public ResponseEntity<TmbOneServiceResponse<GetSecretResponse>> getUvsecret(@RequestHeader HttpHeaders header,
                                                                                        @RequestBody @Valid GetSecretRequest getSecretRequest) throws TMBCommonException {

        TmbOneServiceResponse<GetSecretResponse> getSecretTmbOneServiceResponseResponse = new TmbOneServiceResponse<>();
        try {
            String uvUserName = header.getFirst(CustomerServiceConstant.HEADER_USER_NAME);
            GetSecretUvFeignRequest getSecretUvFeignRequest = new GetSecretUvFeignRequest();
            getSecretUvFeignRequest.setUserName(uvUserName);
            getSecretUvFeignRequest.setRmNumber(getSecretRequest.getUvRmNumber());
            getSecretUvFeignRequest.setRole(getSecretRequest.getUvRole());

            GetSecretResponse getUvSecretResponse = customerUvService.customerUvGetSecreatCall(getSecretUvFeignRequest);

            if (getUvSecretResponse != null) {
                getSecretTmbOneServiceResponseResponse.setData(getUvSecretResponse);
                getSecretTmbOneServiceResponseResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                        ResponseCode.SUCCESS.getService()));
            } else {
                getSecretTmbOneServiceResponseResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                        ResponseCode.FAILED.getService()));
            }

            return ResponseEntity.ok().headers(TMBUtils.getResponseHeaders()).body(getSecretTmbOneServiceResponseResponse);

        } catch (TMBCommonException te) {
            logger.info(" Exception has been raiased for Customer UvSecret for customer profile details : {}", te.getMessage());
            throw new TMBCommonException(te.getMessage());
        } catch (Exception e) {
            logger.error("Unable to fetch data from UV Service : {}", e);
            getSecretTmbOneServiceResponseResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService()));

            return ResponseEntity.badRequest().headers(header).body(getSecretTmbOneServiceResponseResponse);
        }
    }
}
