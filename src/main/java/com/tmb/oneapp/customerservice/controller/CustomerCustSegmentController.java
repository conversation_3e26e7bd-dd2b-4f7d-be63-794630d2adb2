package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.GetCustSegmentResponse;
import com.tmb.oneapp.customerservice.service.CustomerCustSegmentService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Tag(name = "Checks customer's segment data")
public class CustomerCustSegmentController {

    private static final TMBLogger<CustomerCustSegmentController> logger =
            new TMBLogger<>(CustomerCustSegmentController.class);

    private final CustomerCustSegmentService customerCustSegmentService;

    @Autowired
    public CustomerCustSegmentController(CustomerCustSegmentService customerCustSegmentService){
        this.customerCustSegmentService = customerCustSegmentService;
    }

    /**
     * Checks customer's segment, wealth flag, etc
     *
     * @return return response
     */
    @Operation(summary = "Checks customer's segment, wealth info")
    @LogAround
    @GetMapping(value = "/customers/getCustSegment/{crmId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<GetCustSegmentResponse>> getCustSegment(
            @Parameter(description = "crmId", example = "001100000000000000000001184383", required = true)
            @PathVariable("crmId") String crmId) throws TMBCommonException {
        TmbOneServiceResponse<GetCustSegmentResponse> response = new TmbOneServiceResponse<>();
        try {
            GetCustSegmentResponse responseData = customerCustSegmentService.getCustSegment(crmId);

            if(responseData == null) {
                response.setStatus(new TmbStatus(ResponseCode.DATA_NOT_FOUND_ERROR.getCode(),
                        ResponseCode.DATA_NOT_FOUND_ERROR.getMessage(),
                        ResponseCode.DATA_NOT_FOUND_ERROR.getService(),
                        ResponseCode.DATA_NOT_FOUND_ERROR.getDesc()));
            } else {
                response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(),
                        ResponseCode.SUCCESS.getMessage(),
                        ResponseCode.SUCCESS.getService(),
                        ResponseCode.SUCCESS.getDesc()));
            }

            response.setData(responseData);
            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(response);
        } catch (Exception e) {
            logger.error("Unexpected error when calling GET /customers/getCustSegment/{crmId} : {} ", e);

            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(response);
        }
    }
}
