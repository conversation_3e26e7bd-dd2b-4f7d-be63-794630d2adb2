package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.service.UpdateCustomerService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.*;

@RestController
public class UpdateCustomerController {
    private static final TMBLogger<UpdateCustomerController> logger =
            new TMBLogger<>(UpdateCustomerController.class);
    private final UpdateCustomerService updateCustomerService;

    public UpdateCustomerController(UpdateCustomerService updateCustomerService) {
        this.updateCustomerService = updateCustomerService;
    }

    @Operation(summary = "Update Customer IAL Value")
    @LogAround
    @PostMapping(value = "/customers/uplift")
    public ResponseEntity<TmbServiceResponse<Void>> updateCustomerIALValue(
            @Parameter(description = CORRELATION_ID,
                    example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da",
                    required = true)
            @RequestHeader(CORRELATION_ID) String ignoreCorrelationId,
            @Parameter(name = HEADERS_X_CRMID,
                    example = "001100000000000000000000000100",
                    required = true)
            @RequestHeader(HEADERS_X_CRMID) String crmId,
            @RequestBody Map<String, String> ignoreBody)
            throws TMBCommonException {
        TmbServiceResponse<Void> response = new TmbServiceResponse<>();
        try {
            updateCustomerService.updateCustomerIALValue(crmId, CREATE_CUSTOMER_IAL_VALUE);
            response.setStatus(CustomerServiceUtils.returnSuccessStatus());
            return ResponseEntity.status(HttpStatus.OK).headers(TMBUtils.getResponseHeaders()).body(response);
        } catch (Exception e) {
            logger.error("Error calling GET /customers/uplift : {}", e.getMessage());
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }

    }
}
