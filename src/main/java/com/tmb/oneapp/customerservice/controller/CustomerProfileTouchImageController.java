package com.tmb.oneapp.customerservice.controller;

import java.time.Instant;
import java.util.Map;

import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.base.Strings;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.service.CustomerTouchProfileImageService;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

/**
 * class for getting Customer touch profile Image Url
 *
 */
@Tag(name = "Customer Touch Image Url Api")
@RestController
public class CustomerProfileTouchImageController {

	private static final TMBLogger<CustomerProfileTouchImageController> logger = new TMBLogger<>(
			CustomerProfileTouchImageController.class);
	private final CustomerTouchProfileImageService customerTouchProfileImageService;
	private final HttpHeaders responseHeaders;

	@Autowired
	public CustomerProfileTouchImageController(CustomerTouchProfileImageService customerTouchProfileImageService,
			HttpHeaders responseHeaders) {
		super();
		this.customerTouchProfileImageService = customerTouchProfileImageService;
		this.responseHeaders = responseHeaders;
	}

	/**
	 * method for getting Customer touch profile Image Url
	 * 
	 * @param crmId
	 * @param headers
	 * @return
	 */
	@LogAround
	@Operation(summary = "Customer Touch Image Url")
	@Parameters({
			@Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = "Transations ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da") })
	@GetMapping(value = "customers/touch/image/{crmId}")
	public ResponseEntity<TmbOneServiceResponse<String>> getCustTouchProfileImage(
			@Parameter(description = "CRMID", example = "001100000000000000000001184383", required = true) @PathVariable("crmId") String crmId,
			@Parameter(hidden = true) @RequestHeader Map<String, String> headers) {
		TmbOneServiceResponse<String> oneServiceResponse = new TmbOneServiceResponse<>();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		logger.info("getCustTouchProfileImage CRM_ID : {}", crmId);
		try {
			if (!Strings.isNullOrEmpty(crmId)) {
				String profileImageUrl = customerTouchProfileImageService.getCustomerTouchProfileImageUrl(crmId,
						headers.get(CustomerServiceConstant.CORRELATION_ID));
				if (!Strings.isNullOrEmpty(profileImageUrl)) {
					oneServiceResponse.setData(profileImageUrl);
					oneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(),
							ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
					return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
				} else {
					this.commonDataNotFound(responseHeaders, oneServiceResponse);
				}
			} else {
				this.commonDataNotFound(responseHeaders, oneServiceResponse);
			}
		} catch (Exception e) {
			logger.info("getCustTouchProfileImage Unable to fetch data : {}", e);
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService()));
		}
		return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
	}

	private ResponseEntity<TmbOneServiceResponse<String>> commonDataNotFound(HttpHeaders responseHeaders,
			TmbOneServiceResponse<String> oneServiceResponse) {
		oneServiceResponse.setData(CustomerServiceConstant.DATA_NOT_FOUND);
		oneServiceResponse.setStatus(new TmbStatus(ResponseCode.DATA_NOT_FOUND.getCode(),
				ResponseCode.DATA_NOT_FOUND.getMessage(), ResponseCode.DATA_NOT_FOUND.getService()));
		return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
	}
}
