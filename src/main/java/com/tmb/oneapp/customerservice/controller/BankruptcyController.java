package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.BankruptcyStatusRequest;
import com.tmb.oneapp.customerservice.model.BankruptcyValidateResponse;
import com.tmb.oneapp.customerservice.service.BankruptcyService;
import com.tmb.oneapp.customerservice.utils.TtbOneUtils;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
public class BankruptcyController {

    private static final TMBLogger<BankruptcyController> logger = new TMBLogger<>(BankruptcyController.class);

    private final BankruptcyService bankruptcyService;

    @Autowired
    public BankruptcyController(BankruptcyService bankruptcyService) {
        this.bankruptcyService = bankruptcyService;
    }

    @LogAround
    @PostMapping(value = "customers/bankruptcy/validation", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<BankruptcyValidateResponse>> validationBankruptcy(
            @Parameter(description = "X-Correlation-ID", example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @RequestBody BankruptcyStatusRequest bankruptcyRequest
    ) throws TMBCommonException {
        TmbOneServiceResponse<BankruptcyValidateResponse> response = new TmbOneServiceResponse<>();
        try {
            logger.info("validationBankruptcy request: {}", TMBUtils.convertJavaObjectToString(bankruptcyRequest));
            BankruptcyValidateResponse bankruptcyJudgementStatus = bankruptcyService.validationBankruptcy(correlationId, bankruptcyRequest);
            response.setData(bankruptcyJudgementStatus);
            response.setStatus(TtbOneUtils.toTtbStatus(ResponseCode.SUCCESS));
            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(response);
        } catch (TMBCommonException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Unable to validationBankruptcy : {} ", e);
            response.setStatus(TtbOneUtils.toTtbStatus(ResponseCode.FAILED));
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(response);
        }
    }
}
