package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.ekycgetbiometric.EkycGetBiometricRequest;
import com.tmb.oneapp.customerservice.model.ekycgetbiometric.EkycGetBiometricResponse;
import com.tmb.oneapp.customerservice.service.CustomerEkycBiometricService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

import java.util.Optional;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.ERROR_CODE_8200;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.ERROR_CODE_8201;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.ERROR_CODE_8202;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.ERROR_CODE_8299;

/**
 * Controller class responsible for Get Biometric Get Data
 */
@RestController
@Tag(name = "Get Biometric Get Data")
public class CustomerEkycBiometricController {

    private static final TMBLogger<CustomerEkycBiometricController> logger = new TMBLogger<>(CustomerEkycBiometricController.class);
    private final CustomerEkycBiometricService customerEkycBiometricService;

    /**
     * Constructor
     *
     * @param customerEkycBiometricService model
     */
    public CustomerEkycBiometricController(CustomerEkycBiometricService customerEkycBiometricService) {
        super();
        this.customerEkycBiometricService = customerEkycBiometricService;
    }

    /**
     * method : To call CustomerEkycBiometricGetService
     *
     * @param requestBody EkycBiometricGetRequestFormat
     * @return EkycBiometricGetResponseFormat
     */
    @Operation(summary = "Get Ekyc Biometric Get")
    @PostMapping(value = "/customers/v2/ekyc/biometric", produces = MediaType.APPLICATION_JSON_VALUE)
    @LogAround
    public ResponseEntity<TmbOneServiceResponse<EkycGetBiometricResponse>> getCustomerEkycBiometric(
            @RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @Valid @RequestBody EkycGetBiometricRequest requestBody) throws TMBCommonException {

        TmbOneServiceResponse<EkycGetBiometricResponse> customerEkycBiometricResponse = new TmbOneServiceResponse<>();

        try {
            final EkycGetBiometricResponse ekycGetBiometricResponse = customerEkycBiometricService.getCustomerEkycBiometric(correlationId, requestBody);

            customerEkycBiometricResponse.setData(ekycGetBiometricResponse);
            customerEkycBiometricResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(customerEkycBiometricResponse);
        } catch (Exception e) {
            logger.error("Unable to getCustomerEkycBiometric data : {} ", e);
            String errorCode = getErrorCode(e);

            if (ERROR_CODE_8202.equals(errorCode)) {
                throw new TMBCommonException(ResponseCode.ERROR_8202.getCode(), ResponseCode.ERROR_8202.getMessage(), ResponseCode.ERROR_8202.getService(), HttpStatus.BAD_REQUEST, null);
            }

            customerEkycBiometricResponse.setStatus(new TmbStatus(errorCode, ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(customerEkycBiometricResponse);
        }
    }
    private static String getErrorCode(Exception e) {
        String error = Optional.ofNullable(e.getMessage()).orElse(ResponseCode.FAILED.getCode());
        return switch (error) {
            case ERROR_CODE_8299 -> ResponseCode.ERROR_8299.getCode();
            case ERROR_CODE_8200 -> ResponseCode.ERROR_8200.getCode();
            case ERROR_CODE_8201 -> ResponseCode.ERROR_8201.getCode();
            case ERROR_CODE_8202 -> ResponseCode.ERROR_8202.getCode();
            default -> ResponseCode.FAILED.getCode();
        };
    }
}
