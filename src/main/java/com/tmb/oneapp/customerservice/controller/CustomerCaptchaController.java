package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.logger.TTBPayloadType;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.CustomerCaptcha;
import com.tmb.oneapp.customerservice.service.CustomerCaptchaService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import jakarta.validation.Valid;

@RestController
@Tag(name = "Customer captcha counter")
public class CustomerCaptchaController {
    private static final TMBLogger<CustomerCaptchaController> logger = new TMBLogger<>(CustomerCaptchaController.class);
    private CustomerCaptchaService customerCaptchaService;

    @Autowired
    public CustomerCaptchaController(CustomerCaptchaService customerCaptchaService) {
        this.customerCaptchaService = customerCaptchaService;
    }

    @LogAround
    @Operation(summary = "Get customer captcha count")
    @GetMapping(value = "/customers/captcha/{CRM_ID}")
    public ResponseEntity<TmbOneServiceResponse<CustomerCaptcha>> getCaptchaCount(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @Parameter(description = "CRMID", example = "001100000000000000000001184383", required = true) @PathVariable("CRM_ID") String crmId,
            @RequestHeader HttpHeaders headers) throws TMBCommonException {
        try {
            logger.payload(TTBPayloadType.INBOUND, headers, null);

            logger.info("Get customer captcha counter");
            CustomerCaptcha counter = customerCaptchaService.count(crmId);

            TmbOneServiceResponse<CustomerCaptcha> response = new TmbOneServiceResponse<>();
            response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE, CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMER_EXP_SERVICE));
            response.setData(counter);

            HttpHeaders responseHeader = TMBUtils.getResponseHeaders();
            logger.payload(TTBPayloadType.OUTBOUND, responseHeader, TMBUtils.convertJavaObjectToString(response));
            return ResponseEntity.ok().headers(responseHeader).body(response);
        } catch (Exception e) {
            logger.error("Unable to get captcha counter from db: {}", e);
            throw CustomerServiceUtils.getTMBCommonException(ResponseCode.DATA_NOT_FOUND);
        }
    }

    @LogAround
    @Operation(summary = "Upsert customer captcha count")
    @PostMapping(value = "/customers/captcha")
    public ResponseEntity<TmbOneServiceResponse<String>> updateCaptchaCount(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @Parameter(description = "CRMID", example = "001100000000000000000001184383", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CUSTOMER_CRM_ID) String crmId,
            @Parameter(description = "Device model", example = "SM-G955F", required = true) @RequestHeader(CustomerServiceConstant.DEVICE_MODEL) String deviceModel,
            @Parameter(description = "Captcha Request", required = true) @RequestBody CustomerCaptcha upsertCaptchaRequest,
            @RequestHeader HttpHeaders headers) throws TMBCommonException {
        try {
            logger.payload(TTBPayloadType.INBOUND, headers, TMBUtils.convertJavaObjectToString(upsertCaptchaRequest));

            logger.info("Update customer captcha");
            boolean upsert = customerCaptchaService.upsert(crmId, deviceModel, upsertCaptchaRequest.getInvalidCounter());

            if (!upsert) {
                logger.error("Fail to upsert data");
                throw CustomerServiceUtils.getTMBCommonException(ResponseCode.FAILED);
            }

            TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
            response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE, CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMER_EXP_SERVICE));

            HttpHeaders responseHeader = TMBUtils.getResponseHeaders();
            logger.payload(TTBPayloadType.OUTBOUND, responseHeader, TMBUtils.convertJavaObjectToString(response));
            return ResponseEntity.ok().headers(responseHeader).body(response);
        } catch (Exception e) {
            logger.error("Unable to upsert captcha counter: {}", e);
            throw CustomerServiceUtils.getTMBCommonException(ResponseCode.DATA_NOT_FOUND);
        }
    }
}

