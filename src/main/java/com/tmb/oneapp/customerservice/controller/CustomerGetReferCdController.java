package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.repository.CustomerDetailByUserIdRepository;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;

/**
 * CustomerTouchMigrationController request mapping will handle apis call and
 * then navigate to respective method to get migration details
 *
 */
@RestController
@Tag(name = "Customer Get Refer CD Api")
public class CustomerGetReferCdController {
	private final CustomerDetailByUserIdRepository customerDetailByUserIdRepository;
	private static final TMBLogger<CustomerGetReferCdController> logger = new TMBLogger<>(
			CustomerGetReferCdController.class);

	/**
	 * Constructor
	 *
	 * @param customerDetailByUserIdRepository
	 */
	@Autowired
	public CustomerGetReferCdController(CustomerDetailByUserIdRepository customerDetailByUserIdRepository) {
		super();
		this.customerDetailByUserIdRepository = customerDetailByUserIdRepository;
	}

	/**
	 * Description:- Inquiry customer status
	 * 
	 * @param crmId
	 * @return customer status description
	 */
	@LogAround
	@Operation(summary = "Get Customer Refer CD Data")
	@GetMapping(value = {"/customers/refer-cd/get/{CRM_ID}"})
	public TmbOneServiceResponse<String> getCustomerReferCd(
			@Parameter(description = "CRMID", example = "001100000000000000000001184383", required = true) @PathVariable("CRM_ID") String crmId,
			@Parameter(description = "X-Correlation-ID", example = "b605a35e-106b-4ab2-a53c-30ac8908133b", required = true) @RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId) {
		TmbOneServiceResponse<String> oneServiceResponse = new TmbOneServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

		try {
			String referCd = customerDetailByUserIdRepository.findReferCdByCrmId(crmId);
			oneServiceResponse.setData(referCd);
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
					ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
			return oneServiceResponse;
		} catch (Exception e) {
			logger.error("Unable to fetch data from crm db : {}", e);
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.DATABASE_CONNECTION_ERROR.getCode(),
					ResponseCode.DATABASE_CONNECTION_ERROR.getMessage(),
					ResponseCode.DATABASE_CONNECTION_ERROR.getService(),
					ResponseCode.DATABASE_CONNECTION_ERROR.getDesc()));
			return oneServiceResponse;
		}
	}

	@LogAround
	@Operation(summary = "Get crm-id by wow ref-id")
	@GetMapping(value = {"/customers/refer-cd/{wow_ref_id}"})
	public TmbOneServiceResponse<String> getCustomerIdByReferCd(
			@Parameter(description = "wow_ref_id", example = "P00000055000039", required = true) @PathVariable("wow_ref_id") String wowRefId,
			@Parameter(description = "X-Correlation-ID", example = "b605a35e-106b-4ab2-a53c-30ac8908133b", required = true) @RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId) throws TMBCommonException {
		TmbOneServiceResponse<String> oneServiceResponse = new TmbOneServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

		try {
			logger.info("Fetch crmId by wow ref id x-correlation-id {}", correlationId);
			String crmId = customerDetailByUserIdRepository.findCrmIdByReferCd(wowRefId);

			logger.info("Successfully get crmId {} by {}", crmId, wowRefId);
			oneServiceResponse.setData(crmId);
			oneServiceResponse.setStatus(CustomerServiceUtils.getResponseFromStatus(ResponseCode.SUCCESS));
			return oneServiceResponse;
		} catch (Exception e) {
			logger.error("Unable to fetch data from crm db : {}", e);
			throw CustomerServiceUtils.getTMBCommonException(ResponseCode.DATA_NOT_FOUND);
		}
	}
}
