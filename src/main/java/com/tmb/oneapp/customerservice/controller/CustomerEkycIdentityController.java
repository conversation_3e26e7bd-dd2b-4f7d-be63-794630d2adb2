package com.tmb.oneapp.customerservice.controller;

import java.time.Instant;

import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.EKYCCheckStatusResponse;
import com.tmb.oneapp.customerservice.model.EKYCIdentityETEResponse;
import com.tmb.oneapp.customerservice.model.EKYCIdentityProviderResponse;
import com.tmb.oneapp.customerservice.service.CustomerEkycIdentityService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

/**
 * Controller responsible for Customer EKYC
 *
 */
@RestController
@Tag(name = "Customer EKYC Identity Api")
public class CustomerEkycIdentityController {
	private static final TMBLogger<CustomerEkycIdentityController> logger = new TMBLogger<>(CustomerEkycIdentityController.class);
	private final CustomerEkycIdentityService customerEkycIdentityService;

	/**
	 * Constructor
	 */
	@Autowired
	public CustomerEkycIdentityController(CustomerEkycIdentityService customerEkycIdentityService) {
		this.customerEkycIdentityService = customerEkycIdentityService;
	}

	/**
	 * 
	 * Controller responsible for check ekyc request status
	 */
	@Operation(summary = "check eKyc status for request NDID")
	@LogAround
	@GetMapping(value = "/customers/ekyc/status/{referenceId}")
	public ResponseEntity<TmbServiceResponse<EKYCCheckStatusResponse>> checkEkycRequestStatus(
			@Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID")  String correlationId,
			@Parameter(description = "X-Agent", example = "N", required = true) @Valid @RequestHeader("X-Agent")  String agent,
			@Parameter(description = "reference_id", example = "dbb2fd6f-f5c5-4833-8e88-d2a8847ced97", required = true) @Valid @PathVariable("referenceId") String referenceId)
			throws TMBCommonException {
		logger.info("CustomerEkycIdentityController  checkEkycStatus method start Time : {} ",
				System.currentTimeMillis());
		TmbServiceResponse<EKYCCheckStatusResponse> oneServiceResponse = new TmbServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			EKYCIdentityETEResponse response = customerEkycIdentityService.checkRequestStatus(correlationId, referenceId, agent);
			oneServiceResponse.setData(new EKYCCheckStatusResponse(response.getRequestStatus(), response.getCustomer(),
					new EKYCIdentityProviderResponse(response.getProvider())));
			oneServiceResponse
					.setStatus(returnSuccessStatus(response.getRequestStatus().getResponse().getDescriptionEng(),
							response.getRequestStatus().getResponse().getDescriptionThai()));
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (TMBCommonException ce) {
			logger.error("Check ekyc status : TMBCommonException : {}", ce);
			oneServiceResponse.setStatus(
					new Status(
							ce.getErrorCode(),
							ce.getErrorMessage(),
							ResponseCode.FAILED.getService(),
							null)
			);
			return ResponseEntity.badRequest().body(oneServiceResponse);
		} catch (Exception e) {
			logger.error("Check ekyc status : Exception : {}", e);
			throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
		}
	}

	/**
	 * 
	 * Controller responsible for check ekyc request status
	 */
	@Operation(summary = "request close NDID")
	@LogAround
	@GetMapping(value = "/customers/ekyc/close/{referenceId}")
	public ResponseEntity<TmbServiceResponse<String>> requestCloseNDID(
			@Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
			@Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
			@Parameter(description = "reference_id", example = "dbb2fd6f-f5c5-4833-8e88-d2a8847ced97", required = true)
			@Valid @PathVariable("referenceId") String referenceId)
			throws TMBCommonException {
		logger.debug(correlationId);
		logger.info("CustomerEkycIdentityController requestCloseNDID method start Time : {} ",
				System.currentTimeMillis());
		TmbServiceResponse<String> oneServiceResponse = new TmbServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			customerEkycIdentityService.requestCloseNDID(referenceId);
			oneServiceResponse.setStatus(returnSuccessStatus());
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (TMBCommonException e) {
			logger.error("Close NDID : TMBCommonException : {}", e);
			throw e;
		} catch (Exception e) {
			logger.error("Close NDID : Exception : {}", e);
			throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
		}
	}

	private Status returnSuccessStatus() {
		return new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
				ResponseCode.SUCCESS.getService(), CustomerServiceUtils.returnSuccessDescription());
	}

	private Status returnSuccessStatus(String descEn, String descTh) {
		return new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
				ResponseCode.SUCCESS.getService(), CustomerServiceUtils.returnSuccessDescription(descEn, descTh));
	}
}