package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.data.DailyLimitGroup;
import com.tmb.oneapp.customerservice.model.data.UpdateCustomerDailyLimitGroupRequest;
import com.tmb.oneapp.customerservice.model.data.UpdateEligibleGroupRequest;
import com.tmb.oneapp.customerservice.model.data.UpdateEligibleGroupResponse;
import com.tmb.oneapp.customerservice.service.CustomerTransactionLimitService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Tag(name = "Customer transaction limit")
public class CustomerTransactionLimitController {

    private static final TMBLogger<CustomerTransactionLimitController> logger =
            new TMBLogger<>(CustomerTransactionLimitController.class);
    private final CustomerTransactionLimitService customerTransactionLimitService;

    @LogAround
    @Operation(summary = "Get customer daily limit group")
    @GetMapping(value = "/customers/daily-limit/get-group",
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<DailyLimitGroup>> getDailyLimitGroup(
            @Parameter(description = "Correlation ID",
                    example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @Parameter(description = "CRM ID", example = "001100000000000000000006534675")
            @Valid @RequestHeader("X-CRMID") String crmId) throws TMBCommonException {
        try {

            DailyLimitGroup dailyLimitGroup =
                    customerTransactionLimitService.getDailyLimitGroup(crmId, correlationId);
            TmbOneServiceResponse<DailyLimitGroup> response = new TmbOneServiceResponse<>();
            response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE,
                    CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMER_SERVICE));
            response.setData(dailyLimitGroup);
            return ResponseEntity.ok().body(response);

        } catch (Exception e) {
            logger.error("Error while get crm group limit: {}", e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    e);
        }
    }

    @LogAround
    @Operation(summary = "Update customer daily limit group")
    @PostMapping(value = "/customers/daily-limit/update-group",
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<String>> updateDailyLimitGroup(
            @Parameter(description = "Correlation ID",
                    example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @RequestHeader HttpHeaders ignoredHeader,
            @RequestBody UpdateCustomerDailyLimitGroupRequest request) throws TMBCommonException {
        try {
            String group =
                    customerTransactionLimitService.updateDailyLimitGroup(request, correlationId);
            logger.info("Update crm group limit in controller {}",group);
            TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
            response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE,
                    CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMER_SERVICE));
            response.setData(group);
            return ResponseEntity.ok().body(response);

        } catch (Exception e) {
            logger.error("Error while update crm group limit: {}", e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    e);
        }
    }

    @LogAround
    @Operation(summary = "Update eligible customer daily limit group")
    @PostMapping(value = "/customers/daily-limit/update-eligible",
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<UpdateEligibleGroupResponse>> updateEligibleGroup(
            @Parameter(description = "Correlation ID",
                    example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @RequestHeader HttpHeaders header,
            @RequestBody UpdateEligibleGroupRequest request) throws TMBCommonException {
        try {
            String group =
                    customerTransactionLimitService.updateEligibleGroup(header, request);

            UpdateEligibleGroupResponse res = new UpdateEligibleGroupResponse();
            res.setCrmId(header.getFirst(CustomerServiceConstant.HEADERS_X_CRMID));
            res.setEligibleGroupId(group);
            logger.info("Update eligible crm group limit in controller {}",group);
            TmbOneServiceResponse<UpdateEligibleGroupResponse> response = new TmbOneServiceResponse<>();
            response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE,
                    CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMER_SERVICE));
            response.setData(res);
            return ResponseEntity.ok().body(response);

        } catch (Exception e) {
            logger.error("Error while update eligible crm group limit: {}", e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    e);
        }
    }

}
