package com.tmb.oneapp.customerservice.controller;

import com.tmb.oneapp.customerservice.client.CustomerExpFeignClient;
import com.tmb.oneapp.customerservice.client.TMBGetFavoriteImageServiceClient;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.ShowAllAccountEvent;
import com.tmb.oneapp.customerservice.repository.PersonalizedAcctRelationRepo;
import com.tmb.oneapp.customerservice.repository.PersonalizedFavoriteRepository;
import com.tmb.oneapp.customerservice.service.SetShowAllAccountEventHandlerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class HelloController {

    @Autowired
    PersonalizedAcctRelationRepo personalizedAcctRelationRepo;
    @Autowired
    PersonalizedFavoriteRepository personalizedFavoriteRepository;
    @Autowired
    TMBGetFavoriteImageServiceClient tmbGetFavoriteImageServiceClient;
    @Autowired
    CustomerExpFeignClient customerExpFeignClient;

    @Operation(summary = "Test migrate account")
    @GetMapping(value = "/customers/hello/migrate-account")
    public void migrateAccount(@Parameter(description = "Correlation ID", example = "swagger_32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
                               @Parameter(description = "CRM ID", example = "001100000000000000000018595952") @RequestHeader(value = CustomerServiceConstant.HEADERS_X_CRMID, required = false) String crmId) {
        ShowAllAccountEvent showAllAccountEvent = new ShowAllAccountEvent();
        showAllAccountEvent.setCorrelationId(correlationId);
        showAllAccountEvent.setCrmId(crmId);
        SetShowAllAccountEventHandlerService setShowAllAccountEventHandlerService = new SetShowAllAccountEventHandlerService(showAllAccountEvent, personalizedAcctRelationRepo, personalizedFavoriteRepository, tmbGetFavoriteImageServiceClient, customerExpFeignClient);
        setShowAllAccountEventHandlerService.run();
    }
}
