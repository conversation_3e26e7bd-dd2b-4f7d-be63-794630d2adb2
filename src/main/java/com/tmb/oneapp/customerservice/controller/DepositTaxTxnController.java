package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.model.DepositTaxRequest;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.DepositTaxTxnResponse;
import com.tmb.oneapp.customerservice.model.TaxYearRequest;
import com.tmb.oneapp.customerservice.service.DepositTaxTxnService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_PARAM_CRM_ID_DEFAULT;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.X_HEADER_CRMID;
import static com.tmb.oneapp.customerservice.constant.ResponseCode.SUCCESS;

@RestController
@Tag(name = "Deposit Controller")
public class DepositTaxTxnController {
    private static final TMBLogger<DepositTaxTxnController> logger = new TMBLogger<>(DepositTaxTxnController.class);

    private final DepositTaxTxnService depositTaxTxnService;

    @Autowired
    public DepositTaxTxnController(DepositTaxTxnService depositTaxTxnService) {
        this.depositTaxTxnService = depositTaxTxnService;
    }

    @LogAround
    @PostMapping(value = "/customers/deposit/tax")
    public ResponseEntity<TmbOneServiceResponse<List<DepositTaxTxnResponse>>> getDepositTaxTxn(
            @Parameter(description = X_HEADER_CRMID, example =HEADER_PARAM_CRM_ID_DEFAULT, required = true)
            @RequestHeader(X_HEADER_CRMID) String crmId,
            @RequestBody TaxYearRequest taxYear
    )  {
        logger.info("getDepositTaxTxn method start time {}", System.currentTimeMillis());
        TmbOneServiceResponse<List<DepositTaxTxnResponse>> serviceResponse = new TmbOneServiceResponse<>();

        try {
            List<DepositTaxTxnResponse> response = depositTaxTxnService
                    .getDepositTaxTxn(
                            crmId.trim(),
                            taxYear.getTaxYear().trim());
            serviceResponse.setData(response);

            serviceResponse.setStatus(new TmbStatus(SUCCESS.getCode(), SUCCESS.getMessage(),
                    SUCCESS.getService(), SUCCESS.getDesc()));

            return ResponseEntity.ok().body(serviceResponse);
        } catch (Exception e) {
            serviceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(serviceResponse);
        }
    }

    @LogAround
    @PostMapping(value = "/customers/deposit/request/count")
    public ResponseEntity<TmbOneServiceResponse<Integer>> getDepositRequestCount(
            @Parameter(description = X_HEADER_CRMID, example =HEADER_PARAM_CRM_ID_DEFAULT, required = true)
            @RequestHeader(X_HEADER_CRMID) String crmId,
            @RequestBody DepositTaxRequest request
    )  {
        logger.info("getDepositValidation method start time {}", System.currentTimeMillis());
        TmbOneServiceResponse<Integer> serviceResponse = new TmbOneServiceResponse<>();

        try {
            serviceResponse.setData(Integer.valueOf(depositTaxTxnService.getDepositRequestCount(crmId, request)));
            serviceResponse.setStatus(new TmbStatus(SUCCESS.getCode(), SUCCESS.getMessage(),
                    SUCCESS.getService(), SUCCESS.getDesc()));
            return ResponseEntity.ok().body(serviceResponse);
        } catch (Exception e) {
            serviceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(serviceResponse);
        }
    }

    @LogAround
    @PostMapping(value = "/customers/deposit/duplicate/count")
    public ResponseEntity<TmbOneServiceResponse<Integer>> getDepositDuplicateCount(
            @RequestBody DepositTaxRequest request
    )  {
        logger.info("getDepositValidation method start time {}", System.currentTimeMillis());
        TmbOneServiceResponse<Integer> serviceResponse = new TmbOneServiceResponse<>();

        try {
            serviceResponse.setData(Integer.valueOf(depositTaxTxnService.getDepositDuplicateCount(request)));
            serviceResponse.setStatus(new TmbStatus(SUCCESS.getCode(), SUCCESS.getMessage(),
                    SUCCESS.getService(), SUCCESS.getDesc()));
            return ResponseEntity.ok().body(serviceResponse);
        } catch (Exception e) {
            serviceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(serviceResponse);
        }
    }

    @LogAround
    @PostMapping(value = "/customers/deposit/tax/req")
    public ResponseEntity<TmbOneServiceResponse<Integer>> addDepositTexReq(
            @RequestBody DepositTaxRequest request
    )  {
        logger.info("addDepositTexReq method start time {}", System.currentTimeMillis());
        TmbOneServiceResponse<Integer> serviceResponse = new TmbOneServiceResponse<>();

        try {
            Integer response = depositTaxTxnService.addDepositTexReq(request);

            serviceResponse.setData(response);
            serviceResponse.setStatus(new TmbStatus(SUCCESS.getCode(), SUCCESS.getMessage(),
                    SUCCESS.getService(), SUCCESS.getDesc()));
            return ResponseEntity.ok().body(serviceResponse);
        } catch (Exception e) {
            serviceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(serviceResponse);
        }
    }

}
