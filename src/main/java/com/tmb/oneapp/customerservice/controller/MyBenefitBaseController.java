package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import java.time.Instant;

public class MyBenefitBaseController {
    protected <T> TmbOneServiceResponse<T> buildResponseSuccess(T data) {
        TmbOneServiceResponse<T> buildResponse = new TmbOneServiceResponse<>();
        buildResponse.setStatus(getResponseSuccess());
        buildResponse.setData(data);
        return buildResponse;
    }

    protected HttpHeaders getResponseHeaders() {
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        return responseHeaders;
    }

    protected TmbStatus getResponseSuccess() {
        return new TmbStatus(
                ResponseCode.SUCCESS.getCode(),
                ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(),
                null);
    }

    protected TMBCommonException genericTmbException(ResponseCode responseCode, HttpStatus status) {
        return new TMBCommonException(
                responseCode.getCode(),
                responseCode.getMessage(),
                responseCode.getService(),
                status,
                null);
    }
}
