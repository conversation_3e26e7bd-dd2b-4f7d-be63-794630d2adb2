package com.tmb.oneapp.customerservice.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.Strings;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.logger.TTBPayloadType;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.client.BiometricVerificationHistoryClient;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.BiometricVerificationHistoryModel;
import com.tmb.oneapp.customerservice.model.BiometricVerificationHistoryRequest;
import com.tmb.oneapp.customerservice.model.CustomerDetailResponse;
import com.tmb.oneapp.customerservice.model.CustomerSearchRequest;
import com.tmb.oneapp.customerservice.service.CustomerSearchServiceImpl;
import io.swagger.v3.oas.annotations.Parameter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.time.Instant;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@RestController
/**
 * CustomerSearchByMblController request mapping will handle apis call and then
 * navigate to respective method to get customer details
 *
 */
public class CustomerSearchController {

    private static final TMBLogger<CustomerSearchController> logger = new TMBLogger<>(
            CustomerSearchController.class);
    private final CustomerSearchServiceImpl customerSearchServiceImpl;
    private final BiometricVerificationHistoryClient biometricVerificationHistoryClient;

    /**
     * Constructor
     *
     * @param customerSearchServiceImpl
     */
    @Autowired
    public CustomerSearchController(CustomerSearchServiceImpl customerSearchServiceImpl,
                                    BiometricVerificationHistoryClient biometricVerificationHistoryClient) {
        super();
        this.customerSearchServiceImpl = customerSearchServiceImpl;
        this.biometricVerificationHistoryClient = biometricVerificationHistoryClient;
    }

    /**
     * Description:- Inquiry  rm-id, mobile-no, citizen-id, passport-no and customer Details
     *
     * @param reqBodyData
     * @return return customer Details
     */
    @PostMapping(value = {"/customers/search", "/customers/ecprofile"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public TmbOneServiceResponse<List<CustomerDetailResponse>> fetchCustomerSearch(
            @Valid @RequestBody CustomerSearchRequest reqBodyData) throws TMBCommonException {
        TmbOneServiceResponse<List<CustomerDetailResponse>> oneServiceResponse = new TmbOneServiceResponse<>();
        logger.info("CustomerSearchController : {}", reqBodyData);
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

        // call core bank service
        String searchType = reqBodyData.getSearchType();
        String searchValue = reqBodyData.getSearchValue();
        if (!Strings.isNullOrEmpty(searchType) && !Strings.isNullOrEmpty(searchValue)) {
            List<CustomerDetailResponse> data = customerSearchServiceImpl
                    .customersServiceCall(searchType, searchValue, false);
            oneServiceResponse.setData(data);
            oneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
        }


        return oneServiceResponse;
    }

    @PostMapping(value = {"/customers/ecprofile/realtime"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<List<CustomerDetailResponse>>> fetchCustomerSearchRealtime(
            @Valid @RequestBody CustomerSearchRequest reqBodyData,
            @RequestHeader HttpHeaders headers) throws TMBCommonException, JsonProcessingException {
        TmbOneServiceResponse<List<CustomerDetailResponse>> oneServiceResponse = new TmbOneServiceResponse<>();
        logger.payload(TTBPayloadType.INBOUND, headers, TMBUtils.convertJavaObjectToString(reqBodyData));
        logger.info("CustomerSearchController fetch ecprofile realtime body: {}", reqBodyData);

        // call core bank service
        String searchType = reqBodyData.getSearchType();
        String searchValue = reqBodyData.getSearchValue();
        if (!Strings.isNullOrEmpty(searchType) && !Strings.isNullOrEmpty(searchValue)) {
            List<CustomerDetailResponse> data = customerSearchServiceImpl
                    .customersServiceCall(searchType, searchValue, true);
            oneServiceResponse.setData(data);
            oneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
        }

        HttpHeaders responseHeader = TMBUtils.getResponseHeaders();
        logger.payload(TTBPayloadType.OUTBOUND, responseHeader, TMBUtils.convertJavaObjectToString(oneServiceResponse));

        return ResponseEntity.ok().headers(responseHeader).body(oneServiceResponse);
    }

    /**
     * To fetch customer data from biometric verification history service
     *
     * @param correlationId
     * @param reqBodyData
     * @return
     * @throws TMBCommonException
     */
    @PostMapping(value = "/customers/biometric/verification/history", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<BiometricVerificationHistoryModel>> fetchCustomerBiometricVerificationHistory(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @Valid @RequestBody BiometricVerificationHistoryRequest reqBodyData) throws TMBCommonException {
        logger.info("fetchCustomerBiometricVerificationHistory for correlation-ID: {}", correlationId);
        TmbOneServiceResponse<BiometricVerificationHistoryModel> response = new TmbOneServiceResponse<>();
        try {
            reqBodyData.setRquid(correlationId);
            reqBodyData.setBiometricDataSource(CustomerServiceConstant.BIOMETRIC_VERIFICATION_HISTORY_DATA_SOURCE);
            reqBodyData.setAll(true);
            ResponseEntity<List<BiometricVerificationHistoryModel>> clientResponse = biometricVerificationHistoryClient.getBiometricVerificationHistory(reqBodyData);
            if (clientResponse != null && clientResponse.getStatusCode().equals(HttpStatus.OK) && clientResponse.hasBody() && !CollectionUtils.isEmpty(clientResponse.getBody())) {
                List<BiometricVerificationHistoryModel> modelList = clientResponse.getBody();
                Optional<BiometricVerificationHistoryModel> biometricModelOpt = modelList.stream().sorted(Comparator.comparing(BiometricVerificationHistoryModel::getTimestamp).reversed()).findFirst();
                if (biometricModelOpt.isPresent()) {
                    response.setData(biometricModelOpt.get());
                    response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
                }
            }
        } catch (Exception e) {
            logger.error("Error while fetchCustomerBiometricVerificationHistory: {}", e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);
        }
        return ResponseEntity.ok(response);
    }
}