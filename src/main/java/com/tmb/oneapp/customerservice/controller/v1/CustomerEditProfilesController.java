package com.tmb.oneapp.customerservice.controller.v1;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.CustomerKycEddProfileResponse;
import com.tmb.oneapp.customerservice.model.CustomerUpdatePhoneRequest;
import com.tmb.oneapp.customerservice.model.EteCustomerRequest;
import com.tmb.oneapp.customerservice.model.edd.CustomerUpdateEddRequest;
import com.tmb.oneapp.customerservice.service.CustomerEditProfilesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import static com.tmb.oneapp.customerservice.constant.ResponseCode.SUCCESS;

@RestController
public class CustomerEditProfilesController {
    private final CustomerEditProfilesService customerEditProfilesService;

    public CustomerEditProfilesController(CustomerEditProfilesService customerEditProfilesService) {
        this.customerEditProfilesService = customerEditProfilesService;
    }

    @LogAround
    @Operation(summary = "unblock kyc")
    @GetMapping(value = "/profiles/kyc-unblock")
    public ResponseEntity<TmbOneServiceResponse<String>> unBlockKyc(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = CustomerServiceConstant.X_CRMID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.X_CRMID) @RequestHeaderNonNull String crmId
            ) throws TMBCommonException {
        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeader = TMBUtils.getResponseHeaders();

        response.setStatus(new TmbStatus(SUCCESS.getCode(), SUCCESS.getMessage(),
                SUCCESS.getService(), SUCCESS.getDesc()));
        customerEditProfilesService.unBlockKyc(crmId, correlationId);

        return ResponseEntity.ok().headers(responseHeader).body(response);
    }

    @LogAround
    @Operation(summary = "update customer")
    @PostMapping(value = "/profiles/update-customer")
    public ResponseEntity<TmbOneServiceResponse<String>> updateCustomer(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @RequestBody EteCustomerRequest request
    ) throws TMBCommonException {
        TmbOneServiceResponse<String> response = customerEditProfilesService.updateCustomer(request.getCustomer(), correlationId);
        HttpHeaders responseHeader = TMBUtils.getResponseHeaders();
        return ResponseEntity.ok().headers(responseHeader).body(response);
    }

    @LogAround
    @Operation(summary = "get EDD profile")
    @GetMapping(value = "/profiles/kyc-edd")
    public ResponseEntity<TmbOneServiceResponse<CustomerKycEddProfileResponse>> getEddProfile(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = CustomerServiceConstant.X_CRMID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.X_CRMID) @RequestHeaderNonNull String crmId
    ) throws TMBCommonException, JsonProcessingException {
        TmbOneServiceResponse<CustomerKycEddProfileResponse> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeader = TMBUtils.getResponseHeaders();

        response.setStatus(new TmbStatus(SUCCESS.getCode(), SUCCESS.getMessage(),
                SUCCESS.getService(), SUCCESS.getDesc()));
        CustomerKycEddProfileResponse data = customerEditProfilesService.getEddKyc(crmId, correlationId);
        response.setData(data);
        return ResponseEntity.ok().headers(responseHeader).body(response);
    }

    @LogAround
    @Operation(summary = "update EDD profile")
    @PostMapping(value = "/profiles/kyc-edd")
    public ResponseEntity<TmbOneServiceResponse<Void>> updateEddProfile(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = CustomerServiceConstant.X_CRMID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.X_CRMID) @RequestHeaderNonNull String crmId,
            @Parameter(description = "is_create", example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader("is_create") @RequestHeaderNonNull Boolean isCreate,
            @RequestBody CustomerUpdateEddRequest request
    ) throws TMBCommonException {
        TmbOneServiceResponse<Void> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeader = TMBUtils.getResponseHeaders();

        response.setStatus(new TmbStatus(SUCCESS.getCode(), SUCCESS.getMessage(),
                SUCCESS.getService(), SUCCESS.getDesc()));
        customerEditProfilesService.updateCustomerEdd(isCreate, correlationId, crmId, request.getKycEdd());
        return ResponseEntity.ok().headers(responseHeader).body(response);
    }

    @LogAround
    @Operation(summary = "create phone")
    @PutMapping (value = "/profiles/phone")
    public ResponseEntity<TmbOneServiceResponse<Void>> createPhone(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = CustomerServiceConstant.X_CRMID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.X_CRMID) @RequestHeaderNonNull String crmId,
            @RequestBody CustomerUpdatePhoneRequest request
    ) throws TMBCommonException, JsonProcessingException {
        TmbOneServiceResponse<Void> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeader = TMBUtils.getResponseHeaders();

        response.setStatus(new TmbStatus(SUCCESS.getCode(), SUCCESS.getMessage(),
                SUCCESS.getService(), SUCCESS.getDesc()));
        customerEditProfilesService.createPhone(crmId, correlationId, request);
        return ResponseEntity.ok().headers(responseHeader).body(response);
    }

    @LogAround
    @Operation(summary = "delete phone")
    @DeleteMapping (value = "/profiles/phone")
    public ResponseEntity<TmbOneServiceResponse<Void>> deletePhone(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = CustomerServiceConstant.X_CRMID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.X_CRMID) @RequestHeaderNonNull String crmId,
            @RequestBody CustomerUpdatePhoneRequest request
    ) throws TMBCommonException, JsonProcessingException {
        TmbOneServiceResponse<Void> response = new TmbOneServiceResponse<>();
        HttpHeaders responseHeader = TMBUtils.getResponseHeaders();

        response.setStatus(new TmbStatus(SUCCESS.getCode(), SUCCESS.getMessage(),
                SUCCESS.getService(), SUCCESS.getDesc()));
        customerEditProfilesService.deletePhone(crmId, correlationId, request);
        return ResponseEntity.ok().headers(responseHeader).body(response);
    }

}
