package com.tmb.oneapp.customerservice.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.logger.TTBPayloadType;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.CustomerDeviceInformationForReactive;
import com.tmb.oneapp.customerservice.model.CustomerDeviceInformationRequest;
import com.tmb.oneapp.customerservice.model.CustomerProfileEntity;
import com.tmb.oneapp.customerservice.model.UpdateCrmProfileRequest;
import com.tmb.oneapp.customerservice.service.RegistrationService;
import com.tmb.oneapp.customerservice.service.UpdateCustomerProfileStatusService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

import static com.tmb.oneapp.customerservice.utils.CustomerServiceUtils.getTMBCommonException;

@RestController
@Tag(name = "Customer reactivation")
public class ReactivationController {
    private static final TMBLogger<ReactivationController> logger = new TMBLogger<>(ReactivationController.class);
    private final RegistrationService registrationService;
    private final UpdateCustomerProfileStatusService updateCustomerProfileStatusService;

    public ReactivationController(RegistrationService registrationService, UpdateCustomerProfileStatusService updateCustomerProfileStatusService) {
        this.registrationService = registrationService;
        this.updateCustomerProfileStatusService = updateCustomerProfileStatusService;
    }

    @LogAround
    @Operation(summary = "Get customer device information")
    @GetMapping(value = "/customers/device/registration")
    public ResponseEntity<TmbOneServiceResponse<CustomerDeviceInformationForReactive>> getCustomerDeviceInformation(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @Parameter(description = "CRM ID", example = "001100000000000000000012004030", required = true) @RequestHeader(CustomerServiceConstant.HEADERS_X_CRMID) String crmId,
            @RequestHeader HttpHeaders headers) throws TMBCommonException, JsonProcessingException {
        TmbOneServiceResponse<CustomerDeviceInformationForReactive> response = new TmbOneServiceResponse<>();
        try {
            logger.payload(TTBPayloadType.INBOUND, headers, null);

            CustomerDeviceInformationForReactive customerDeviceInformationResponse = registrationService.get(crmId);
            logger.info("Get customer device information: [customerDeviceInformationResponse={}]", customerDeviceInformationResponse);

            response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE, CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMER_EXP_SERVICE));
            response.setData(customerDeviceInformationResponse);

        } catch (TMBCommonException e) {
            logger.error("Error while get customer device information : {}", e);
            throw getTMBCommonException(ResponseCode.DATA_NOT_FOUND_ERROR);

        } catch (Exception e) {
            logger.error("Error while get customer device information : {}", e);
            throw getTMBCommonException(ResponseCode.GENERAL_ERROR);

        }

        HttpHeaders responseHeader = TMBUtils.getResponseHeaders();
        logger.payload(TTBPayloadType.OUTBOUND, responseHeader, TMBUtils.convertJavaObjectToString(response));
        return ResponseEntity.ok().headers(responseHeader).body(response);
    }

    @LogAround
    @Operation(summary = "Update customer device information profile")
    @PostMapping(value = "/customers/device/registration", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<CustomerDeviceInformationForReactive>> updateRegistration(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @RequestBody CustomerDeviceInformationRequest customerDeviceInformationRequest,
            @RequestHeader HttpHeaders headers) throws TMBCommonException, JsonProcessingException {
        TmbOneServiceResponse<CustomerDeviceInformationForReactive> response = new TmbOneServiceResponse<>();
        try {
            logger.payload(TTBPayloadType.INBOUND, headers, TMBUtils.convertJavaObjectToString(customerDeviceInformationRequest));

            logger.info("updateRegistration: [customerDeviceInfoRequest={}]", customerDeviceInformationRequest);
            CustomerDeviceInformationForReactive customerDeviceInformationResponse = registrationService.updateRegistration(customerDeviceInformationRequest);

            response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE, CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMER_EXP_SERVICE));
            response.setData(customerDeviceInformationResponse);
        } catch (Exception e) {
            logger.error("Error while update customer device information : {}", e);
            this.errorResponse();
        }

        HttpHeaders responseHeader = TMBUtils.getResponseHeaders();
        logger.payload(TTBPayloadType.OUTBOUND, responseHeader, TMBUtils.convertJavaObjectToString(response));
        return ResponseEntity.ok().headers(responseHeader).body(response);
    }

    @LogAround
    @Operation(summary = "Update customer device information profile")
    @PostMapping(value = "/customers/crmprofile/update", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<CustomerProfileEntity>> updateCustomerProfileStatus(
            @Parameter(description = "CRMID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = "X-CRMID", required = false) String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @Parameter(description = "Flow name", example = "Set Pin", required = false) @RequestHeader("Flow-Name") String flowName,
            @RequestBody UpdateCrmProfileRequest updateCrmProfileRequest,
            @RequestHeader HttpHeaders headers) throws TMBCommonException, JsonProcessingException {
        TmbOneServiceResponse<CustomerProfileEntity> response = new TmbOneServiceResponse<>();
        logger.payload(TTBPayloadType.INBOUND, headers, TMBUtils.convertJavaObjectToString(updateCrmProfileRequest));

        try {
            String status = updateCrmProfileRequest.getStatus();
            String refId = updateCrmProfileRequest.getRefId();
            CustomerProfileEntity customerProfile = updateCustomerProfileStatusService.update(headers, status, refId);

            response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE, CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMER_SERVICE));
            response.setData(customerProfile);
        } catch (Exception e) {
            logger.error("Error while update customer profile : {}", e);
            this.errorResponse();
        }

        HttpHeaders responseHeader = TMBUtils.getResponseHeaders();
        logger.payload(TTBPayloadType.OUTBOUND, responseHeader, TMBUtils.convertJavaObjectToString(response));
        return ResponseEntity.ok().headers(responseHeader).body(response);
    }

    public void errorResponse() throws TMBCommonException {
        throw new TMBCommonException(
                ResponseCode.FAILED.getCode(),
                ResponseCode.FAILED.getMessage(),
                ResponseCode.FAILED.getService(),
                HttpStatus.OK,
                null);
    }
}