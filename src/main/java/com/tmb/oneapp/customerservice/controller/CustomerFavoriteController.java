package com.tmb.oneapp.customerservice.controller;

import com.google.common.base.Strings;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.logger.TTBLogType;
import com.tmb.common.logger.TTBPayloadType;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.model.customer.favorite.CustomerFavoriteListResponse;
import com.tmb.common.model.customer.favorite.CustomerTransferResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.AddFevReg;
import com.tmb.oneapp.customerservice.model.CrmIdAndAppIdRequest;
import com.tmb.oneapp.customerservice.model.CustFavouriteDeleteDetails;
import com.tmb.oneapp.customerservice.model.CustomerFavoriteBillAndPopupResponse;
import com.tmb.oneapp.customerservice.model.CustomerFavoriteTransferResponse;
import com.tmb.oneapp.customerservice.model.FavoriteValidateRequest;
import com.tmb.oneapp.customerservice.model.FevDetailsReg;
import com.tmb.oneapp.customerservice.model.data.FavDetailsResponse;
import com.tmb.oneapp.customerservice.model.data.FavValidateResponse;
import com.tmb.oneapp.customerservice.model.data.UpdateDetailsRequest;
import com.tmb.oneapp.customerservice.model.favorite.FavoriteSaveResponse;
import com.tmb.oneapp.customerservice.model.favorite.image.UpdateFavoriteImageRequest;
import com.tmb.oneapp.customerservice.model.favorite.image.UpdateFavoriteImageResponse;
import com.tmb.oneapp.customerservice.service.CustomerFavoriteService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;
import com.tmb.oneapp.customerservice.utils.TtbOneUtils;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

@Validated
@RestController
@RequiredArgsConstructor
public class CustomerFavoriteController {
	private static final TMBLogger<CustomerFavoriteController> logger = new TMBLogger<>(CustomerFavoriteController.class);
	private final CustomerFavoriteService customerFavoriteService;

	@LogAround
	@Operation(summary = "Upload/Remove favorite image")
	@PostMapping(value = "/customers/favorite/image")
	public ResponseEntity<TmbOneServiceResponse<UpdateFavoriteImageResponse>> updateFavoriteImage(
					@Parameter(description = "Correlation ID", example = "swagger_32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
					@Parameter(description = "CRM ID", example = "001100000000000000000018595952") @RequestHeader(value = CustomerServiceConstant.HEADERS_X_CRMID, required = false) String crmId,
					@Valid @RequestBody UpdateFavoriteImageRequest request) throws TMBCommonException {
		logger.info("Get customer favorite transfer for correlation-ID: {}", correlationId);
		TmbOneServiceResponse<UpdateFavoriteImageResponse> ttbOneServiceResponse = new TmbOneServiceResponse<>();

		try {
			UpdateFavoriteImageResponse updateFavoriteImageResponse = customerFavoriteService.updateFavoriteImage( request, crmId, correlationId );
			ttbOneServiceResponse.setData( updateFavoriteImageResponse );
			ttbOneServiceResponse.setStatus( TtbOneUtils.toTtbStatus(ResponseCode.SUCCESS) );
			return ResponseEntity.ok(ttbOneServiceResponse);
		} catch (Exception e) {
			logger.error("Error while updateFavoriteImage: {}", e);
			throw new TMBCommonException(
							ResponseCode.FAILED.getCode(),
							ResponseCode.FAILED.getMessage(),
							ResponseCode.FAILED.getService(),
							HttpStatus.OK,
							null);
		}
	}

	@Operation(summary = "Get customer favorite transfer")
    @LogAround
    @PostMapping(value = "/customers/favorite/transfer")
    public ResponseEntity<TmbOneServiceResponse<List<CustomerFavoriteTransferResponse>>> getCustomersFavoriteTransfer(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
			@Parameter(description = "CRM ID", example = "001100000000000000000006534675") @RequestHeader(value = CustomerServiceConstant.HEADERS_X_CRMID, required = false) String headerCrmId,
            @Valid @RequestBody CrmIdAndAppIdRequest request) throws TMBCommonException {
        logger.info("Get customer favorite transfer for correlation-ID: {}", correlationId);

		TmbOneServiceResponse<List<CustomerFavoriteTransferResponse>> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
			boolean isFromMobileApp = StringUtils.isNotBlank(headerCrmId);
			request.setCrmId(isFromMobileApp ? headerCrmId : request.getCrmId());

			List<CustomerFavoriteTransferResponse> response = customerFavoriteService.getCustomerFavoriteTransfer(correlationId, request, isFromMobileApp);
			tmbOneServiceResponse.setData(response);
			tmbOneServiceResponse.setStatus(CustomerServiceUtils.getResponseFromStatus(ResponseCode.SUCCESS));

			return ResponseEntity.ok(tmbOneServiceResponse);
        } catch (Exception e) {
            logger.error("Error while getCustomersFavoriteTransfer: {}", e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);
        }
    }

	@Operation(summary = "Get customer favorite bill")
	@LogAround
	@PostMapping(value = "/customers/favorite/bill")
	public ResponseEntity<TmbOneServiceResponse<List<CustomerFavoriteBillAndPopupResponse>>> getCustomersFavoriteBill(
			@Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
			@Parameter(description = "CRM ID", example = "001100000000000000000006534675") @RequestHeader(value = CustomerServiceConstant.HEADERS_X_CRMID, required = false) String crmIdFromHeaders,
			@Valid @RequestBody CrmIdAndAppIdRequest request) throws TMBCommonException {
		logger.info("Get customer favorite bill for correlation-ID: {}", correlationId);

		TmbOneServiceResponse<List<CustomerFavoriteBillAndPopupResponse>> tmbOneServiceResponse = new TmbOneServiceResponse<>();
		try {
			boolean isFromMobileApp = StringUtils.isNotBlank(crmIdFromHeaders);
			request.setCrmId((isFromMobileApp) ? crmIdFromHeaders : request.getCrmId());

			List<CustomerFavoriteBillAndPopupResponse> response = customerFavoriteService.getCustomerFavoriteBillAndPopup(correlationId, request, "0", isFromMobileApp);
			tmbOneServiceResponse.setData(response);
			tmbOneServiceResponse.setStatus(CustomerServiceUtils.getResponseFromStatus(ResponseCode.SUCCESS));

			return ResponseEntity.ok(tmbOneServiceResponse);
		} catch (Exception e) {
			logger.error("Error while getCustomersFavoriteBill: {}", e);
			throw new TMBCommonException(
					ResponseCode.FAILED.getCode(),
					ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(),
					HttpStatus.OK,
					null);
		}
	}

    @Operation(summary = "Get customer favorite topup")
    @LogAround
    @PostMapping(value = "/customers/favorite/topup")
	public ResponseEntity<TmbOneServiceResponse<List<CustomerFavoriteBillAndPopupResponse>>> getCustomersFavoriteTopup(
			@Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
			@Parameter(description = "CRM ID", example = "001100000000000000000006534675") @RequestHeader(value = CustomerServiceConstant.HEADERS_X_CRMID, required = false) String headerCrmId,
			@Valid @RequestBody CrmIdAndAppIdRequest request) throws TMBCommonException {
		logger.info("Get customer favorite topup for correlation-ID: {}", correlationId);

		TmbOneServiceResponse<List<CustomerFavoriteBillAndPopupResponse>> tmbOneServiceResponse = new TmbOneServiceResponse<>();
		try {
			boolean isFromMobileApp = StringUtils.isNotBlank(headerCrmId);
			request.setCrmId(isFromMobileApp ? headerCrmId : request.getCrmId());

			List<CustomerFavoriteBillAndPopupResponse> response = customerFavoriteService.getCustomerFavoriteBillAndPopup(correlationId, request, "1", isFromMobileApp);

			tmbOneServiceResponse.setData(response);
			tmbOneServiceResponse.setStatus(CustomerServiceUtils.getResponseFromStatus(ResponseCode.SUCCESS));

			return ResponseEntity.ok(tmbOneServiceResponse);
		} catch (Exception e) {
			logger.error("Error while getCustomersFavoriteTopup: {}", e);
			throw new TMBCommonException(
					ResponseCode.FAILED.getCode(),
					ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(),
					HttpStatus.OK,
					null);
		}
	}

    @LogAround
    @Operation(summary = "Get customer favorite List")
    @PostMapping(value = "/customers/favorite/list")
    public ResponseEntity<TmbOneServiceResponse<CustomerFavoriteListResponse>> getCustomersFavoriteList(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @Parameter(description = "X-CRMID", example = "001100000000000000000000200012", required = true) @Valid @RequestHeader("X-CRMID") @RequestHeaderNonNull String crmId) throws TMBCommonException {

        TmbOneServiceResponse<CustomerFavoriteListResponse> favListResponse = new TmbOneServiceResponse<>();
		HttpHeaders getFavListHeaders = new HttpHeaders();
		getFavListHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			long startTime = System.nanoTime();
			if (crmId == null) {

				favListResponse.setStatus(new TmbStatus(ResponseCode.BAD_REQUEST.getCode(),
						ResponseCode.BAD_REQUEST.getMessage(), ResponseCode.BAD_REQUEST.getService()));
				return ResponseEntity.badRequest().headers(getFavListHeaders).body(favListResponse);
			}

			CustomerFavoriteListResponse response = customerFavoriteService.getCustomerFavoriteList(correlationId, crmId);
			if( response != null ){
				favListResponse.setData(response);
				long endTime = System.nanoTime();
				long totalTime = endTime - startTime;
				logger.info(" ==>  Favorite List favListResponse Controller End  : {}", totalTime);

				favListResponse
						.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
								ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));

				if(response.getTransfer()!=null){
					logger.info("Returned favorite ids: {}",response.getTransfer().stream().map(CustomerTransferResponse::getFavoriteId).collect(Collectors.toList()));
				}
				logger.info("Favorite List favListResponse method end Time : {} ", System.currentTimeMillis());
				return ResponseEntity.ok().headers(getFavListHeaders).body(favListResponse);
			} else {
				favListResponse.setStatus(new TmbStatus(ResponseCode.BAD_REQUEST.getCode(),
						ResponseCode.BAD_REQUEST.getMessage(), ResponseCode.BAD_REQUEST.getService()));
				return ResponseEntity.badRequest().headers(getFavListHeaders).body(favListResponse);
			}

		} catch (Exception ef) {
			logger.error("Unable to getCustomersFavoriteList  : {}", ef);
			favListResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService()));
			throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);


		}
    }

    @LogAround
    @Operation(summary = "delete from favorite List")
    @DeleteMapping(value = "/customers/favorite/delete")
    public ResponseEntity<TmbOneServiceResponse<String>> deleteFavourites (
            @Parameter(description = "X-CRMID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = "X-CRMID", required = false) String crmId,
            @RequestBody CustFavouriteDeleteDetails custFavDetails) throws TMBCommonException {

		HttpHeaders responseHeadersFavDel = new HttpHeaders();
		responseHeadersFavDel.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			HttpHeaders reqHeaders = new HttpHeaders();
			reqHeaders.set(CustomerServiceConstant.X_CRMID,crmId);
			logger.payload(TTBPayloadType.INBOUND, reqHeaders, TMBUtils.convertJavaObjectToString(custFavDetails));
			String deleteFromFav = customerFavoriteService.deleteFromFavourites(custFavDetails,crmId);
			TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
			response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE, CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMER_EXP_SERVICE));
			response.setData(deleteFromFav);
			logger.payload(TTBPayloadType.OUTBOUND, responseHeadersFavDel, TMBUtils.convertJavaObjectToString(response));
			return ResponseEntity.ok().body(response);
		}catch (TMBCommonException tce){
			logger.error(TTBLogType.ERROR, "/customers/favorite/delete",  tce);
		throw tce;
		}catch (Exception e) {
			logger.error(TTBLogType.ERROR, "/customers/favorite/delete",  e);
            logger.error("Error while get favID : "+e.getMessage(),e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);
        }

		}

    @LogAround
    @Operation(summary = "Get customer favorite validate")
    @PostMapping(value = "/customers/favorite/validate")
    public ResponseEntity<TmbOneServiceResponse<FavValidateResponse>> getCustomersFavoriteValidate(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @Parameter(description = "X-CRMID", example = "001100000000000000000000200012", required = true) @Valid @RequestHeader("X-CRMID") @RequestHeaderNonNull String crmId,
            @RequestBody FavoriteValidateRequest favoriteValidateRequest) throws TMBCommonException {

        TmbOneServiceResponse<FavValidateResponse> favListResponse = new TmbOneServiceResponse<>();
		HttpHeaders getFavListHeaders = new HttpHeaders();
		getFavListHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {

			long startTime = System.nanoTime();
			logger.info("customer favorite validate in customer service start time {}",startTime);
			if (crmId == null) {

				favListResponse.setStatus(new TmbStatus(ResponseCode.BAD_REQUEST.getCode(),
						ResponseCode.BAD_REQUEST.getMessage(), ResponseCode.BAD_REQUEST.getService()));
				return ResponseEntity.badRequest().headers(getFavListHeaders).body(favListResponse);
			}

			FavValidateResponse response = customerFavoriteService.getCustomerFavoriteValidate(correlationId, crmId, favoriteValidateRequest);
			favListResponse.setData(response);
			long endTime = System.nanoTime();
			long totalTime = endTime - startTime;
			logger.info(" ==>  Favorite List favListResponse Controller End  : {}", totalTime);
			favListResponse.setStatus( TtbOneUtils.toTtbStatus(ResponseCode.SUCCESS) );

			logger.info("getCustomerFavoriteValidate end Time : {} ", System.currentTimeMillis());
			return ResponseEntity.ok().headers(getFavListHeaders).body(favListResponse);


		} catch (CallNotPermittedException ce) {
			logger.error("Unable to getCustomersFavoriteValidate  : {}", ce);
			favListResponse.setStatus(new TmbStatus(ResponseCode.CIRCUIT_BREAKER_ERROR.getCode(), ResponseCode.CIRCUIT_BREAKER_ERROR.getMessage(),
					ResponseCode.CIRCUIT_BREAKER_ERROR.getService()));
			throw CustomerServiceUtils.circuitBreakerExceptionHandling();
		} catch (Exception ef) {
			logger.error("Unable to getCustomersFavoriteValidate  : {}", ef);
			favListResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService()));
			throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);


		}
    }



    @LogAround
    @Operation(summary = "Add customer favorite ")
    @PostMapping(value = "/customers/favorite/add")
    public ResponseEntity<TmbOneServiceResponse<FavoriteSaveResponse>> addFavorite(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @Parameter(description = "X-CRMID", example = "001100000000000000000000200012", required = true) @Valid @RequestHeader("X-CRMID") @RequestHeaderNonNull String crmId,
            @RequestBody AddFevReg addFevReg) throws TMBCommonException {

        TmbOneServiceResponse<FavoriteSaveResponse> addFevResponse = new TmbOneServiceResponse<>();
		HttpHeaders addFavListHeaders = new HttpHeaders();
		addFavListHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			long startTime = System.nanoTime();
			logger.info("Add customer favorite in customer service start time {}", startTime);
			if (Strings.isNullOrEmpty(addFevReg.getCacheKey())) {

				addFevResponse.setStatus(new TmbStatus(ResponseCode.BAD_REQUEST.getCode(),
						ResponseCode.BAD_REQUEST.getMessage(), ResponseCode.BAD_REQUEST.getService()));
				return ResponseEntity.badRequest().headers(addFavListHeaders).body(addFevResponse);
			}

			FavoriteSaveResponse response = customerFavoriteService.addFavorite(correlationId, crmId, addFevReg);
			if (response != null) {
				addFevResponse.setData(response);
				long endTime = System.nanoTime();
				long totalTime = endTime - startTime;
				logger.info(" ==>  addFavorite End  : {}", totalTime);

				addFevResponse
						.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
								ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));

				logger.info("addFavorite method end Time : {} ", System.currentTimeMillis());
				return ResponseEntity.ok().headers(addFavListHeaders).body(addFevResponse);

			} else {
				addFevResponse.setStatus(new TmbStatus(ResponseCode.BAD_REQUEST.getCode(),
						ResponseCode.BAD_REQUEST.getMessage(), ResponseCode.BAD_REQUEST.getService()));
				return ResponseEntity.badRequest().headers(addFavListHeaders).body(addFevResponse);
			}

		} catch (Exception ef) {
			logger.error("Unable to addFavorite  : {}", ef);
			addFevResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService()));
			throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);


		}
    }


    @LogAround
    @Operation(summary = "Customer favorite detail ")
    @PostMapping(value = "/customers/favorite/details")
    public ResponseEntity<TmbOneServiceResponse<FavDetailsResponse>> getFavDetails(
            @Parameter(description = "X-CRMID", example = "001100000000000000000000200012", required = true) @Valid @RequestHeader("X-CRMID") @RequestHeaderNonNull String crmId,
            @RequestBody FevDetailsReg detailsFevReg) throws TMBCommonException {

        TmbOneServiceResponse<FavDetailsResponse> detailsFevResponse = new TmbOneServiceResponse<>();
		HttpHeaders detailsFavListHeaders = new HttpHeaders();
		detailsFavListHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			long startTime = System.nanoTime();
			if (Strings.isNullOrEmpty(detailsFevReg.getFavoriteId())) {

				detailsFevResponse.setStatus(new TmbStatus(ResponseCode.BAD_REQUEST.getCode(),
						ResponseCode.BAD_REQUEST.getMessage(), ResponseCode.BAD_REQUEST.getService()));
				return ResponseEntity.badRequest().headers(detailsFavListHeaders).body(detailsFevResponse);
			}

			FavDetailsResponse response = customerFavoriteService.getFavDetails(crmId, detailsFevReg);
			if (response != null) {
				detailsFevResponse.setData(response);
				long endTime = System.nanoTime();
				long totalTime = endTime - startTime;
				logger.info(" ==>  getFavDetails End  : {}", totalTime);

				detailsFevResponse
						.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
								ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));

				logger.info("getFavDetails method end Time : {} ", System.currentTimeMillis());
				return ResponseEntity.ok().headers(detailsFavListHeaders).body(detailsFevResponse);

			} else {
				detailsFevResponse.setStatus(new TmbStatus(ResponseCode.BAD_REQUEST.getCode(),
						ResponseCode.BAD_REQUEST.getMessage(), ResponseCode.BAD_REQUEST.getService()));
				return ResponseEntity.badRequest().headers(detailsFavListHeaders).body(detailsFevResponse);
			}

		} catch (Exception ef) {
			logger.error("Unable to getFavDetails  : {}", ef);
			detailsFevResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService()));
			throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);


		}
    }

    @LogAround
    @Operation(summary = "Customer update favorite detail ")
    @PostMapping(value = "/customers/favorite/update")
    public ResponseEntity<TmbOneServiceResponse<FavoriteSaveResponse>> updateFavDetails(
    		@Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
    		@Parameter(description = "X-CRMID", example = "001100000000000000000000200012", required = true) @Valid @RequestHeader("X-CRMID") @RequestHeaderNonNull String crmId,
            @RequestBody UpdateDetailsRequest updateFevReg) throws TMBCommonException {

        TmbOneServiceResponse<FavoriteSaveResponse> updateFerResponse = new TmbOneServiceResponse<>();
		HttpHeaders updateFavListHeaders = new HttpHeaders();
		updateFavListHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			long startTime = System.nanoTime();
			if (Strings.isNullOrEmpty(updateFevReg.getFavoriteId())) {

				updateFerResponse.setStatus(new TmbStatus(ResponseCode.BAD_REQUEST.getCode(),
						ResponseCode.BAD_REQUEST.getMessage(), ResponseCode.BAD_REQUEST.getService()));
				return ResponseEntity.badRequest().headers(updateFavListHeaders).body(updateFerResponse);
			}

			FavoriteSaveResponse response = customerFavoriteService.updateFavDetails(correlationId, crmId, updateFevReg );
			if (response != null) {
				updateFerResponse.setData(response);
				long endTime = System.nanoTime();
				long totalTime = endTime - startTime;
				logger.info(" ==>  update End  : {}", totalTime);

				updateFerResponse
						.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
								ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));

				logger.info("update fav method end Time : {} ", System.currentTimeMillis());
				return ResponseEntity.ok().headers(updateFavListHeaders).body(updateFerResponse);

			} else {
				updateFerResponse.setStatus(new TmbStatus(ResponseCode.BAD_REQUEST.getCode(),
						ResponseCode.BAD_REQUEST.getMessage(), ResponseCode.BAD_REQUEST.getService()));
				return ResponseEntity.badRequest().headers(updateFavListHeaders).body(updateFerResponse);
			}

		} catch (Exception ef) {
			logger.error("Unable to update fav  : {}", ef);
			updateFerResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService()));
			throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);


		}
    }

    }
