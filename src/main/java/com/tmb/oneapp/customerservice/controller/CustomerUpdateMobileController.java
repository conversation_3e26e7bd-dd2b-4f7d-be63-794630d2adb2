package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.ChangeMobileRequest;
import com.tmb.oneapp.customerservice.model.UpdatePromptpayMobileResponse;
import com.tmb.oneapp.customerservice.service.CustomerUpdateMobileService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class CustomerUpdateMobileController {
    private static final TMBLogger<CustomerUpdateMobileController> logger = new TMBLogger<>(CustomerUpdateMobileController.class);
    private final CustomerUpdateMobileService customerUpdateMobileService;

    public CustomerUpdateMobileController(CustomerUpdateMobileService customerUpdateMobileService) {
        this.customerUpdateMobileService = customerUpdateMobileService;
    }


    @LogAround
    @Operation(summary = "Update or Add promptpay Mobile number")
    @PostMapping(value = "/customers/promptpay/update-mobile", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<UpdatePromptpayMobileResponse>> updateCustomerPromptpayMobile(
            @Parameter(description = "CRM ID", example ="001100000000000000000000090000", required = true) @RequestHeader(name = CustomerServiceConstant.HEADERS_X_CRMID, required = false) String crmId,
            @Parameter(description = "X-Correlation-ID", example ="b605a35e-106b-4ab2-a53c-30ac8908133b", required = true) @RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @RequestHeader HttpHeaders headers,
            @RequestBody ChangeMobileRequest changeMobileRequest) throws TMBCommonException {

        TmbOneServiceResponse<UpdatePromptpayMobileResponse> response = new TmbOneServiceResponse<>();
        try {
            UpdatePromptpayMobileResponse updateResult = customerUpdateMobileService.updateMobileNumber(crmId, correlationId, changeMobileRequest);
            response.setStatus(CustomerServiceUtils.getResponseFromStatus(ResponseCode.SUCCESS));
            response.setData(updateResult);

        } catch (Exception e) {
            logger.error("Add/Update mobile number failed {}", e);

            if(e.getMessage().contains("5002")) {
                throw CustomerServiceUtils.getTMBCommonException(ResponseCode.ETE_5002_SERVICE_ERROR);
            }

            throw CustomerServiceUtils.getTMBCommonException(ResponseCode.ETE_SERVICE_ERROR);
        }

        HttpHeaders responseHeader = TMBUtils.getResponseHeaders();
        return ResponseEntity.ok().headers(responseHeader).body(response);
    }
}
