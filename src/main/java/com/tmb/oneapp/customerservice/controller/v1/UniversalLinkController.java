package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.universallink.NdidResultStatus;
import com.tmb.oneapp.customerservice.model.universallink.VerifyUniversalLinkRequest;
import com.tmb.oneapp.customerservice.service.UniversalLinkService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_TIMESTAMP;

@RestController
public class UniversalLinkController {
    private final UniversalLinkService universalLinkService;

    public UniversalLinkController(UniversalLinkService universalLinkService) {
        this.universalLinkService = universalLinkService;
    }

    @LogAround
    @PostMapping(value = "/ekyc/universal-link/verify")
    @Operation(summary = "Verify Universal Link")
    public ResponseEntity<TmbOneServiceResponse<NdidResultStatus>> verifyUniversalLink(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @RequestBody VerifyUniversalLinkRequest request
            ) throws TMBCommonException {
        TmbOneServiceResponse<NdidResultStatus> response = new TmbOneServiceResponse<>();

        NdidResultStatus data = universalLinkService.verifyUniversalLink(request, correlationId);
        response.setStatus(new TmbStatus(
                ResponseCode.SUCCESS.getCode(),
                ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(),
                null));
        response.setData(data);
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        return ResponseEntity.ok().headers(responseHeaders).body(response);
    }
}
