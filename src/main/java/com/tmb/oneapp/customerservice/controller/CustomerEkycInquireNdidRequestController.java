package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.EkycInquireNdidRequestResponseFormat;
import com.tmb.oneapp.customerservice.service.CustomerEkycInquireNdidRequestService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * Controller class responsible for get customer EKYC Inquire NDID Request
 */
@RestController
@Tag(name = "Customer EKYC Inquire NDID Request")
public class CustomerEkycInquireNdidRequestController {

    private static final TMBLogger<CustomerEkycInquireNdidRequestController> logger = new TMBLogger<>(CustomerEkycInquireNdidRequestController.class);
    private final CustomerEkycInquireNdidRequestService customerEkycInquireNdidRequestService;

    /**
     * Constructor
     *
     * @param customerEkycInquireNdidRequestService CustomerEkycInquireNdidRequestService
     */
    public CustomerEkycInquireNdidRequestController(CustomerEkycInquireNdidRequestService customerEkycInquireNdidRequestService) {
    	super();
        this.customerEkycInquireNdidRequestService = customerEkycInquireNdidRequestService;
    }

    /**
     * method : To call CustomerEkycInquireNdidRequestService
     *
     * @param requestBody Map of Document Id(Citizen ID)
     *
     * @return TmbOneServiceResponse<List<EkycInquireNdidRequestResponseFormat>>
     */
    @Operation(summary = "Get EkycInquireNdidRequest")
    @PostMapping(value = "/customers/ekyc/inquirendidrequest", produces = MediaType.APPLICATION_JSON_VALUE)
    @LogAround
    public ResponseEntity<TmbOneServiceResponse<List<EkycInquireNdidRequestResponseFormat>>> getEkycInquireNdidRequest(
            @RequestBody Map<String, String> requestBody) {

        TmbOneServiceResponse<List<EkycInquireNdidRequestResponseFormat>> ekycInquireNdidRequestResponse = new TmbOneServiceResponse<>();

        try {
            final List<EkycInquireNdidRequestResponseFormat> ekycInquireNdidRequestResponseList = customerEkycInquireNdidRequestService.getCustomerEkycInquireNdidRequest(requestBody.get("document_id"));

            return CustomerServiceUtils.checkEkycInquireNdidRequest(ekycInquireNdidRequestResponseList);
        } catch (Exception e) {
            logger.error("Unable to getEkycInquireNdidRequest data : {} ", e);
            ekycInquireNdidRequestResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                            ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(ekycInquireNdidRequestResponse);
        }
    }
}
