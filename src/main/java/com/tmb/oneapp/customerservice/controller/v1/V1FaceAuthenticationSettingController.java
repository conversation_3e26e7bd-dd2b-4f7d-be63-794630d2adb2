package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.controller.FaceAuthenticationSettingController;
import com.tmb.oneapp.customerservice.model.commonfr.FaceRecognitionFeature;
import com.tmb.oneapp.customerservice.service.V1FaceAuthenticationSettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

import static com.tmb.oneapp.customerservice.utils.CustomerServiceUtils.getResponseFromStatus;

@Controller
@Tag(name = "Face Authentication Setting")
public class V1FaceAuthenticationSettingController {

    private static final TMBLogger<FaceAuthenticationSettingController> logger = new TMBLogger<>(FaceAuthenticationSettingController.class);

    private final V1FaceAuthenticationSettingService v1FaceAuthenticationSettingService;

    public V1FaceAuthenticationSettingController(V1FaceAuthenticationSettingService v1FaceAuthenticationSettingService) {
        this.v1FaceAuthenticationSettingService = v1FaceAuthenticationSettingService;
    }

    @LogAround
    @Operation(summary = "Get all face authentication feature id and feature name")
    @GetMapping(value = "/fr-feature-list")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, example = "ae40d276-35f4-4512-b010-7bde6a32eb8d", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER)
    })
    public ResponseEntity<TmbOneServiceResponse<List<FaceRecognitionFeature>>> getAllFrFeature(@RequestHeader HttpHeaders headers)
            throws TMBCommonException {
        TmbOneServiceResponse<List<FaceRecognitionFeature>> response = new TmbOneServiceResponse<>();
        String correlationId = headers.getFirst(CustomerServiceConstant.HEADER_CORRELATION_ID);
        response.setStatus(getResponseFromStatus(ResponseCode.SUCCESS));
        response.setData(v1FaceAuthenticationSettingService.get(correlationId));
        HttpHeaders responseHeader = TMBUtils.getResponseHeaders();
        logger.info("get all face authentication feature success response: {} ", response.getData());
        return ResponseEntity.ok().headers(responseHeader).body(response);
    }
}