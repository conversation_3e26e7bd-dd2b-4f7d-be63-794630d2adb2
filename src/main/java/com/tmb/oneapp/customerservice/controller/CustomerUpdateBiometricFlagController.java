package com.tmb.oneapp.customerservice.controller;

import com.google.common.base.Strings;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.logger.TTBPayloadType;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.BiometricFlagUpdate;
import com.tmb.oneapp.customerservice.model.UpdateBiometricsEnableFlagRequest;
import com.tmb.oneapp.customerservice.repository.CustomerDetailRepository;
import com.tmb.oneapp.customerservice.service.CustomerUpdateBiometricService;
import com.tmb.oneapp.customerservice.service.EventService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class CustomerUpdateBiometricFlagController {
    private static final TMBLogger<CustomerUpdateBiometricFlagController> logger = new TMBLogger<>(CustomerUpdateBiometricFlagController.class);
    private final CustomerUpdateBiometricService customerUpdateBiometricService;
    private final EventService eventService;
    private final CustomerDetailRepository customerDetailRepository;

    public CustomerUpdateBiometricFlagController(CustomerUpdateBiometricService customerUpdateBiometricService, EventService eventService, CustomerDetailRepository customerDetailRepository) {
        this.customerUpdateBiometricService = customerUpdateBiometricService;
        this.eventService = eventService;
        this.customerDetailRepository = customerDetailRepository;
    }

    @LogAround
    @Operation(summary = "Update biometric flag")
    @PostMapping(value = "/customers/device/update", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<String>> updateCustomerBiometricFlag(
            @Parameter(description = "CRMID", example = "001100000000000000000012027974", required = false) @RequestHeader(name = CustomerServiceConstant.HEADERS_X_CRMID, required = false) String crmId,
            @Parameter(description = "DeviceID", example = "07ece1375bfbe099706fe9649a2438871e9e919e61b817d7b70218ad9b4a366c", required = true) @RequestHeader(name = CustomerServiceConstant.HEADERS_DEVICE_ID) String deviceId,
            @Parameter(description = "X-Correlation-ID", example = "b605a35e-106b-4ab2-a53c-30ac8908133b", required = true) @RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @Parameter(description = "Flow name", example = "Login - Auto detect Biometric change in device", required = false) @RequestHeader(value = "Flow-Name", required = false) String flowName,
            @RequestHeader final HttpHeaders headers,
            @RequestBody UpdateBiometricsEnableFlagRequest updateBiometricsEnableFlagRequest) throws TMBCommonException {

        TmbOneServiceResponse<String> oneServiceResponse = new TmbOneServiceResponse<>();
        Integer biometricFlag = updateBiometricsEnableFlagRequest.getBiometricFlag();
        logger.info("UpdateCustomerBiometricFlag [biometricFlag={}]", biometricFlag);
        try {

            logger.payload(TTBPayloadType.INBOUND, headers, TMBUtils.convertJavaObjectToString(updateBiometricsEnableFlagRequest));
            logger.info("UpdateCustomerBiometricFlag [x-correlation-id={}]", correlationId);
            if (!Strings.isNullOrEmpty(flowName)) {
                logger.info("Find crmId by deviceId: {}", deviceId);
                crmId = this.getCrmIdFromDeviceId(deviceId);
                headers.set(CustomerServiceConstant.HEADERS_X_CRMID, crmId);
                logger.info("Set crmId: {} to Headers", crmId);
            }

            logger.info("UpdateCustomerBiometricFlag [biometricFlag={}, deviceId={}, crmId={}]",
                    biometricFlag, deviceId, crmId);
            customerUpdateBiometricService.updateBiometricFlag(biometricFlag, deviceId, crmId);
            logger.info("Update biometric flag success for crmId : {}", crmId);
            oneServiceResponse.setStatus(CustomerServiceUtils.getResponseFromStatus(ResponseCode.SUCCESS));

            if (this.isAutoUpdateBiometricFlag(flowName)) {
                logger.info("Is auto update biometric flag [flowName={}]", flowName);
                BiometricFlagUpdate activity = eventService.biometricFlagAutoUpdateActivity(biometricFlag, "Success", "", headers);
                eventService.publish(activity);
            }

            HttpHeaders responseHeader = TMBUtils.getResponseHeaders();
            logger.payload(TTBPayloadType.OUTBOUND, responseHeader, TMBUtils.convertJavaObjectToString(oneServiceResponse));
            return ResponseEntity.ok().headers(responseHeader).body(oneServiceResponse);
        } catch (Exception ex) {
            logger.error("Error while update biometrics enable flag", ex);

            if (this.isAutoUpdateBiometricFlag(flowName)) {
                logger.info("Is auto update biometric flag [flowName={}]", flowName);
                BiometricFlagUpdate activity = eventService.biometricFlagAutoUpdateActivity(biometricFlag, "Failure", ex.getMessage(), headers);
                eventService.publish(activity);
            }

            throw CustomerServiceUtils.getTMBCommonException(ResponseCode.FAILED);
        }
    }

    public String getCrmIdFromDeviceId(String deviceId) throws TMBCommonException {
        try {
            String crmId = customerDetailRepository.findByDevice(deviceId);
            logger.info("CrmId is :{}", crmId);
            if (Strings.isNullOrEmpty(crmId)) {
                logger.info("CrmId not found by deviceId {}", deviceId);
                throw CustomerServiceUtils.getTMBCommonException(ResponseCode.FAILED);
            }
            return crmId;
        } catch (Exception ex) {
            logger.error("Error while find crmId by deviceId : {}", ex);
            throw CustomerServiceUtils.getTMBCommonException(ResponseCode.FAILED);
        }
    }

    public boolean isAutoUpdateBiometricFlag(String flowName) {
        if (flowName == null) {
            return false;
        }
        return flowName.equalsIgnoreCase(CustomerServiceConstant.AUTO_UPDATE_BIOMETRIC_FLAG_FLOW);
    }
}
