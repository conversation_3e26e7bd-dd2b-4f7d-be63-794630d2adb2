package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.logger.TTBPayloadType;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.CustomerCheckFatCaResponse;
import com.tmb.oneapp.customerservice.service.CustomerCommonService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.time.Instant;

@RestController
/**
 * CustomerCommonController handle apis for common feature
 * method to get customer details
 *
 */
@Tag(name = "Controller get customer data for common feature")
public class CustomerCommonController {
    private static final TMBLogger<CustomerCommonController> logger = new TMBLogger<>(CustomerCommonController.class);
    private final CustomerCommonService customerCommonService;

    public CustomerCommonController(CustomerCommonService customerCommonService) {
        this.customerCommonService = customerCommonService;
    }

    @LogAround
    @Operation(summary = "Controller for check customer fatca flag")
    @PostMapping(value = {"/customers/common/checkFatcaFlag"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<CustomerCheckFatCaResponse>> checkFatcaFlag(
            @Parameter(description = CustomerServiceConstant.HEADERS_X_CRMID, example ="001100000000000000000012017238") @RequestHeader("X-CRMID") String crmId,
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @RequestHeader("X-Correlation-ID") String correlationId
    ) throws TMBCommonException, IOException {
        TmbOneServiceResponse<CustomerCheckFatCaResponse> oneServiceResponse = new TmbOneServiceResponse<>();
        logger.info(TTBPayloadType.INBOUND + " checkFatcaFlag: crmId {}", crmId);
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
        oneServiceResponse.setData(customerCommonService.checkCustomerFatcaFlag(crmId, correlationId));
        oneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
        logger.info(TTBPayloadType.OUTBOUND + " checkFatcaFlag: crmId {}", oneServiceResponse.getData().getOpenFatca());

        return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
    }
}
