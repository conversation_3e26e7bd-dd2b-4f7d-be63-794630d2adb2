package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.CustomerInternetServiceResponse;
import com.tmb.oneapp.customerservice.service.InternetBankingService;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * Class responsible to handles to get the InternetBanking Service data for the customer
 */

@RestController
@Tag(name = "To get customer internet profile by CRM ID")
@Validated
public class InternetBankingServiceController {
    private final InternetBankingService internetBankingService;
    private static final TMBLogger<InternetBankingServiceController> logger = new TMBLogger<>(InternetBankingServiceController.class);

    /**
     * @param internetBankingService Constructor to invoke the service
     */
    public InternetBankingServiceController(InternetBankingService internetBankingService) {
        this.internetBankingService = internetBankingService;
    }


    /**
     * @param header pass header values
     * @return customerInternetServiceResponse customer Ib profile details
     */
    @Operation(summary = "Api to  IB customer profile")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = "Correlation ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da")})
    @GetMapping(value = "/customers/ib-profile/get/{crm_id}")
    @LogAround
    public ResponseEntity<TmbOneServiceResponse<CustomerInternetServiceResponse>> getCustInternetBankingService(@RequestHeader HttpHeaders header,
                                                                                                                @Parameter(description = "crm_id", example = "001100000000000000000012027065")
                                                                                                                @PathVariable(CustomerServiceConstant.CRM_ID)
                                                                                                                @NotBlank @Pattern(message = "CRM ID must be a number", regexp = "^[0-9]{30}$") String crmId
    ) {

        TmbOneServiceResponse<CustomerInternetServiceResponse> custIbTmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            CustomerInternetServiceResponse customerInternetServiceResponse = internetBankingService.getInternetBankingService(crmId);

            if (customerInternetServiceResponse != null) {
                custIbTmbOneServiceResponse.setData(customerInternetServiceResponse);
                custIbTmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                        ResponseCode.SUCCESS.getService()));
            } else {
                custIbTmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                        ResponseCode.FAILED.getService()));
            }

            return ResponseEntity.ok().headers(TMBUtils.getResponseHeaders()).body(custIbTmbOneServiceResponse);

        } catch (Exception e) {
            logger.error("Unable to fetch data from database : {}", e);
            custIbTmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService()));

            return ResponseEntity.badRequest().headers(header).body(custIbTmbOneServiceResponse);
        }
    }
}
