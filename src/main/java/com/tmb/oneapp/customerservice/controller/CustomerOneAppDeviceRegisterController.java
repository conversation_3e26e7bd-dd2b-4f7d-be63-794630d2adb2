package com.tmb.oneapp.customerservice.controller;

import java.time.Instant;
import java.util.Map;

import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.MigrationDetails;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.service.RegisterOneAppDeviceDetails;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

/**
 * Controller for saving device Details into OneApp
 *
 */
@RestController
@Tag(name = "Customer Touch Migration Register Api")
public class CustomerOneAppDeviceRegisterController {

	private static final TMBLogger<CustomerOneAppDeviceRegisterController> logger = new TMBLogger<>(
			CustomerOneAppDeviceRegisterController.class);

	private RegisterOneAppDeviceDetails registerOneAppDeviceDetails;

	/**
	 * Constructor
	 * 
	 * @param registerOneAppDeviceDetails
	 */
	@Autowired
	public CustomerOneAppDeviceRegisterController(RegisterOneAppDeviceDetails registerOneAppDeviceDetails) {
		super();
		this.registerOneAppDeviceDetails = registerOneAppDeviceDetails;
	}

	/**
	 * method for saving device Details into OneApp
	 * 
	 * @param migrationDetails
	 * @return
	 */
	@LogAround
	@Operation(summary = "Customer Device Register Api")
	@Parameters({
			@Parameter(name = CustomerServiceConstant.DEVICE_MODEL, description = "Device model", required = false, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "Iphone"),
			@Parameter(name = CustomerServiceConstant.DEVICE_OS_TYPE, description = "Device os type", required = false, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "iOS"),
			@Parameter(name = CustomerServiceConstant.DEVICE_OS, description = "Device os (0,1)", required = false, schema = @Schema(type = "number"), in = ParameterIn.HEADER, example = "1"),
			@Parameter(name = CustomerServiceConstant.DEVICE_OS_VERSION, description = "Device os version", required = false, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "12"),
			@Parameter(name = CustomerServiceConstant.ONEAPP_VERSION_USAGE, description = "Oneapp version", required = false, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "1.0.0"),
			@Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = "Transations ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da"),
			@Parameter(name = CustomerServiceConstant.TOUCH_DEVICEID, description = "Touch DeviceId", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "1A825E93-593A-4E42-B86B-CC7D662FEF80"),
			@Parameter(name = CustomerServiceConstant.ONEAPP_DEVICEID, description = "OneApp DeviceId", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "34cec72b26b7a30ae0a3eaa48d45d82bc2f69a75d299728472d9145d57565885"),
			@Parameter(name = CustomerServiceConstant.AUTH_PUBLIC_KEY, description = "Auth Public key", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "MIIBITANBgkqhkiG9w0BAQEFAAOCAQ4AMIIBCQKCAQBvuDajzvbljT9GxHeYyTV/FN6GqC46qXsjtfgPPWKW3xa1/W6BMaVBqLpERD96APkh/HAs3Y/hKYNoyQGiVRp3H4WTGhCVfQOVbdRg7cgQdhQp2KRMUf4k1gm0/DxBX5q64qIrtZ9F8UI++ljr+0Uxsxpey1v/WxCjbYfy7Pfx/HHp8pw6HJ5OBVS1G+nbBpblyqPKD6Dbn4O7IPJXyQbucK09O7W0BOlxQzNEJ79BOQi8x7rAUz48EOygN7ZuuSXAZFWy6Lm8EDbHEuwaeaQa6gFbB4wav5LXk7XbN1UKBUOiQBT1VgM0fVZ9DS1UsFBA+IYbrcswMJZIrVN/yFJjAgMBAAE=") })
	@PostMapping(value = "/customers/device/register")
	public ResponseEntity<TmbOneServiceResponse<String>> saveOneDeviceDetails(
			@Parameter(hidden = true) @RequestHeader Map<String, String> headers,
			@RequestBody MigrationDetails migrationDetails) {
		TmbOneServiceResponse<String> oneServiceResponse = new TmbOneServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		logger.info("saveOneDeviceDetails - headers: {}", headers);
		logger.info("saveOneDeviceDetails - migrationDetails: {}", migrationDetails);
		boolean successFlag = registerOneAppDeviceDetails.saveOneAppDeviceDetails(headers, migrationDetails);
		logger.info("saveOneDeviceDetails successFlag : {}", successFlag);
		if (successFlag) {
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
					ResponseCode.SUCCESS.getService()));
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} else {
			oneServiceResponse.setStatus(
					new TmbStatus(ResponseCode.GENERAL_ERROR.getCode(), ResponseCode.GENERAL_ERROR.getMessage(),
							ResponseCode.GENERAL_ERROR.getService(), ResponseCode.GENERAL_ERROR.getDesc()));
			return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
		}
	}
	
}
