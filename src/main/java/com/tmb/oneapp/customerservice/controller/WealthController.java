package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.PWAProfileResponse;
import com.tmb.oneapp.customerservice.service.WealthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * Controller for obtaining wealth customer data
 *
 */
@RestController
public class WealthController {

    private static final TMBLogger<WealthController> logger =
            new TMBLogger<>(WealthController.class);

    private final WealthService wealthService;

    @Autowired
    public WealthController(WealthService wealthService) {
        this.wealthService = wealthService;
    }

    /**
     * Get user's personal wealth advisor data
     *
     * @return return response
     */
    @Operation(summary = "Gets personal wealth advisor data of customer")
    @LogAround
    @GetMapping(value = "/customers/getPWAProfile", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<PWAProfileResponse>> getPWAProfileByCrmId(
            @Parameter(description = "CRMID", example = "001100000000000000000001184383", required = true) @Valid @RequestHeader("X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId) {
        TmbOneServiceResponse<PWAProfileResponse> response = new TmbOneServiceResponse<>();
        try {
            PWAProfileResponse responseData = wealthService.getPWAProfileByCrmId(crmId, correlationId);

            if(responseData.getFullNameTh() != null) {
                response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                        ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
                response.setData(responseData);
            } else if(responseData.getFullNameTh() == null || responseData.getFullNameTh().isEmpty()){
                response.setStatus(new TmbStatus(ResponseCode.DATA_NOT_FOUND_ERROR.getCode(), ResponseCode.DATA_NOT_FOUND_ERROR.getMessage(),
                        ResponseCode.DATA_NOT_FOUND_ERROR.getService(), ResponseCode.DATA_NOT_FOUND_ERROR.getDesc()));
                response.setData(null);
            }
            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(response);
        } catch (Exception e) {
            logger.error("Unexpected error when calling GET /customers/getPWAProfile : {} ", e);
        }

        response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));
        response.setData(null);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .headers(TMBUtils.getResponseHeaders())
                .body(response);
    }
}
