package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.EkycIdpConsentRequestFormat;
import com.tmb.oneapp.customerservice.model.EkycIdpConsentResponseFormat;
import com.tmb.oneapp.customerservice.service.CustomerEkycIdpConsentService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * Controller class responsible for PUT Ekyc IDP Consent
 */
@RestController
@Tag(name = "PUT Ekyc IDP Consent")
public class CustomerEkycIdpConsentController {

    private static final TMBLogger<CustomerEkycIdpConsentController> logger = new TMBLogger<>(CustomerEkycIdpConsentController.class);
    private final CustomerEkycIdpConsentService customerEkycIdpConsentService;

    /**
     * Constructor
     *
     * @param customerEkycIdpConsentService CustomerEkycIdpConsentService
     */
    public CustomerEkycIdpConsentController(CustomerEkycIdpConsentService customerEkycIdpConsentService) {
        super();
        this.customerEkycIdpConsentService = customerEkycIdpConsentService;
    }

    /**
     * method : To call CustomerEkycIdpConsentService
     *
     * @param requestBody EkycIdpConsentRequestFormat
     * @return EkycIdpConsentResponseFormat
     */
    @Operation(summary = "Put Ekyc IDP Consent")
    @PutMapping(value = "/customers/ekyc/idpconsent", produces = MediaType.APPLICATION_JSON_VALUE)
    @LogAround
    public ResponseEntity<TmbOneServiceResponse<EkycIdpConsentResponseFormat>> putCustomerEkycIdpConsent(
            @Valid @RequestBody EkycIdpConsentRequestFormat requestBody) {

        TmbOneServiceResponse<EkycIdpConsentResponseFormat> customerEkycIdpConsentResponse = new TmbOneServiceResponse<>();

        try {
            final EkycIdpConsentResponseFormat ekycIdpConsentResponseFormat = customerEkycIdpConsentService.updateCustomerEkycIdpConsent(requestBody);

            customerEkycIdpConsentResponse.setData(ekycIdpConsentResponseFormat);
            customerEkycIdpConsentResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));

            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(customerEkycIdpConsentResponse);
        } catch (Exception e) {
            logger.error("Unable to putCustomerEkycIdpConsent data : {} ", e);
            customerEkycIdpConsentResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(customerEkycIdpConsentResponse);
        }
    }
}
