package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.model.CustomerUsageVersion;
import com.tmb.oneapp.customerservice.service.CustomerVersionUsageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;


@RestController
public class V1CustomerVersionUsageController {
    private final CustomerVersionUsageService customerVersionUsageService;

    @Autowired
    public V1CustomerVersionUsageController(CustomerVersionUsageService customerVersionUsageService) {
        super();
        this.customerVersionUsageService = customerVersionUsageService;
    }

    @LogAround
    @Operation(summary = "Get Customer's usage version by CrmId")
    @GetMapping("/versions/{crmId}")
    public ResponseEntity<TmbServiceResponse<CustomerUsageVersion>> getCustomersUsageVersionByCrmId(
            @Parameter(name = "crmId", example = "001100000000000000000001184383", required = true) @PathVariable("crmId") String crmId) throws TMBCommonException {
        return ResponseEntity.ok()
                .headers(TMBUtils.getResponseHeaders())
                .body(customerVersionUsageService.getUsageVersion(crmId));
    }
}