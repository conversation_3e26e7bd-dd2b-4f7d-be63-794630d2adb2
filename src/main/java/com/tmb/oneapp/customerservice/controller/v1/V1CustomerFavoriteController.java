package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.V1CustomerFavoriteTopUpResponse;
import com.tmb.oneapp.customerservice.model.V1CustomerFavoriteTransferResponse;
import com.tmb.oneapp.customerservice.model.favorite.V1FavoriteBillPay;
import com.tmb.oneapp.customerservice.service.V1CustomerFavoriteService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Validated
@RestController
@RequiredArgsConstructor
public class V1CustomerFavoriteController {
    private static final TMBLogger<V1CustomerFavoriteController> logger = new TMBLogger<>(V1CustomerFavoriteController.class);
    private final V1CustomerFavoriteService customerFavoriteService;


    @Operation(summary = "Get customer favorite transfer")
    @LogAround
    @GetMapping(value = "/favorites/transfer")
    public ResponseEntity<TmbOneServiceResponse<List<V1CustomerFavoriteTransferResponse>>> getCustomersFavoriteTransfer(
            @Parameter(description = "Correlation ID", example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @Parameter(description = "CRM ID", example ="001100000000000000000006534675") @RequestHeader(value = CustomerServiceConstant.HEADERS_X_CRMID, required = false) String headerCrmId,
            @Parameter(description = "appId", required = true) @RequestParam("appId") String appId) throws TMBCommonException {
        logger.info("Get V1 customer favorite transfer for correlation-ID: {}", correlationId);

        TmbOneServiceResponse<List<V1CustomerFavoriteTransferResponse>> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        try {
            List<V1CustomerFavoriteTransferResponse> response = customerFavoriteService.getCustomerFavoriteTransfer(correlationId, headerCrmId, appId);
            tmbOneServiceResponse.setData(response);
            tmbOneServiceResponse.setStatus(CustomerServiceUtils.getResponseFromStatus(ResponseCode.SUCCESS));
            return ResponseEntity.ok(tmbOneServiceResponse);
        } catch (Exception e) {
            logger.error("Error while getV1CustomersFavoriteTransfer: {}", e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);
        }
    }

    @Operation(summary = "Get customer favorite topup")
    @LogAround
    @GetMapping(value = "/favorites/topup")
    public ResponseEntity<TmbOneServiceResponse<List<V1CustomerFavoriteTopUpResponse>>> getCustomerFavoriteTopUp(
            @Parameter(description = "Correlation ID", example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @Parameter(description = "CRM ID", example ="001100000000000000000006534675")
            @RequestHeader(value = CustomerServiceConstant.HEADERS_X_CRMID, required = false) String headerCrmId,
            @Parameter(description = "crmId") @RequestParam(value = "crmId") String crmId)
            throws TMBCommonException {
        logger.info("Get customer favorite topup for correlation-ID: {}", correlationId);
        TmbOneServiceResponse<List<V1CustomerFavoriteTopUpResponse>> tmbOneServiceResponse = new TmbOneServiceResponse<>();

        try {
            boolean isFromMobileApp = StringUtils.isNotBlank(headerCrmId);
            crmId = isFromMobileApp ? headerCrmId : crmId;

            List<V1CustomerFavoriteTopUpResponse> responses = customerFavoriteService
                    .getCustomerFavoriteTopUp(correlationId, crmId, isFromMobileApp);
            tmbOneServiceResponse.setData(responses);
            tmbOneServiceResponse.setStatus(CustomerServiceUtils.getResponseFromStatus(ResponseCode.SUCCESS));

            return ResponseEntity.ok(tmbOneServiceResponse);
        } catch (Exception e) {
            logger.error("Error while getCustomersFavoriteTopup: {}", e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.BAD_REQUEST,
                    null);
        }
    }

    @Operation(summary = "Get customer favorite billpay")
    @LogAround
    @GetMapping(value = "/favorites/billpay")
    public ResponseEntity<TmbOneServiceResponse<List<V1FavoriteBillPay>>> getCustomersFavoriteBillPay(
            @Parameter(description = "Correlation ID", example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @Parameter(description = "CRM ID", example ="001100000000000000000006534675", required = true) @RequestHeader(value = CustomerServiceConstant.HEADERS_X_CRMID) String headerCrmId) throws TMBCommonException {
        List<V1FavoriteBillPay> response = customerFavoriteService.getFavoriteBillPay(headerCrmId, correlationId);
        TmbOneServiceResponse<List<V1FavoriteBillPay>> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setData(response);
        tmbOneServiceResponse.setStatus(CustomerServiceUtils.getResponseFromStatus(ResponseCode.SUCCESS));
        return ResponseEntity.ok(tmbOneServiceResponse);
    }

}