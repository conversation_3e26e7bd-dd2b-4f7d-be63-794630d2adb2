package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.EkycBiometricConsent;
import com.tmb.oneapp.customerservice.model.EkycNDIDConditionRequest;
import com.tmb.oneapp.customerservice.model.EkycNDIDConditionResponse;
import com.tmb.oneapp.customerservice.model.PostEkycBiometricConsentRequest;
import com.tmb.oneapp.customerservice.model.PostGetDipchipByCidRequest;
import com.tmb.oneapp.customerservice.model.PostGetDipchipData;
import com.tmb.oneapp.customerservice.model.PutEkycNDIDConditionRequest;
import com.tmb.oneapp.customerservice.model.EkycNdidCondResp;
import com.tmb.oneapp.customerservice.service.EkycNDIDService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.CORRELATION_ID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADERS_X_CRMID;

/**
 * Controller for obtaining EKYC NDID data
 */
@RestController
@Tag(name = "Customer EKYC NDID Api")
public class EkycNDIDController {
    private static final TMBLogger<EkycNDIDController> logger = new TMBLogger<>(EkycNDIDController.class);
    private final EkycNDIDService ekycNDIDService;

    @Autowired
    public EkycNDIDController(EkycNDIDService ekycNDIDService) {
        this.ekycNDIDService = ekycNDIDService;
    }

    /**
     * Get ndid acceptance status
     *
     * @param ekycNDIDConditionRequest citizen id
     * @return return response
     */
    @Operation(summary = "Get NDID conditions")
    @LogAround
    @PostMapping(value = "/customers/ekyc/ndidCondition")
    public ResponseEntity<TmbOneServiceResponse<EkycNDIDConditionResponse>> getEkycNdidCondition(
            @Parameter(description = CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @RequestHeader(CORRELATION_ID) String correlationId,
            @RequestBody EkycNDIDConditionRequest ekycNDIDConditionRequest) {

        TmbOneServiceResponse<EkycNDIDConditionResponse> response = new TmbOneServiceResponse<>();

        try {
            EkycNDIDConditionResponse ncbPaymentConfirmResponse =
                    ekycNDIDService.getEkycNdidCondition(correlationId, ekycNDIDConditionRequest.getCitizenId());

            response.setData(ncbPaymentConfirmResponse);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(),
                    ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));

            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(response);

        } catch (Exception e) {
            logger.error("Error calling GET /customers/ekyc/ndidCondition : {}", e);
            response.setStatus(new TmbStatus(ResponseCode.GENERAL_ERROR.getCode(),
                    ResponseCode.GENERAL_ERROR.getMessage(), ResponseCode.GENERAL_ERROR.getService()));
            return ResponseEntity.badRequest().headers(TMBUtils.getResponseHeaders()).body(response);
        }

    }

    /**
     * Revoke ndid acceptance status
     *
     * @param putEkycNDIDConditionRequest citizen id, event
     * @return return response
     */
    @Operation(summary = "PUT NDID conditions")
    @LogAround
    @PutMapping(value = "/customers/ekyc/ndidCondition")
    public ResponseEntity<TmbOneServiceResponse<EkycNdidCondResp>> putEkycNdidCondition(
            @Parameter(description = CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @RequestHeader(CORRELATION_ID) String correlationId,
            @RequestBody PutEkycNDIDConditionRequest putEkycNDIDConditionRequest) {

        TmbOneServiceResponse<EkycNdidCondResp> response = new TmbOneServiceResponse<>();

        try {
            EkycNdidCondResp ekycNdidConditionResponse =
                    ekycNDIDService.putEkycNdidCondition(correlationId,
                            putEkycNDIDConditionRequest);

            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(),
                    ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
            response.setData(ekycNdidConditionResponse);

            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(response);

        } catch (Exception e) {
            logger.error("Error calling PUT /customers/ekyc/ndidCondition : {}", e);
            response.setStatus(new TmbStatus(ResponseCode.GENERAL_ERROR.getCode(),
                    ResponseCode.GENERAL_ERROR.getMessage(), ResponseCode.GENERAL_ERROR.getService()));
            return ResponseEntity.badRequest().headers(TMBUtils.getResponseHeaders()).body(response);
        }

    }

    /**
     * Revoke ndid acceptance status
     *
     * @param putEkycNDIDConditionRequest citizen id, event
     * @return return response
     */
    @Operation(summary = "PUT NDID conditions")
    @LogAround
    @PutMapping(value = "/customers/v2/ekyc/ndidCondition")
    public ResponseEntity<TmbOneServiceResponse<EkycNdidCondResp>> putV2EkycNdidCondition(
            @Parameter(description = CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @RequestHeader(CORRELATION_ID) String correlationId,
            @RequestBody PutEkycNDIDConditionRequest putEkycNDIDConditionRequest) {

        TmbOneServiceResponse<EkycNdidCondResp> response = new TmbOneServiceResponse<>();

        try {
            EkycNdidCondResp ekycNdidConditionResponse =
                    ekycNDIDService.putEkycNdidConditionV2(correlationId,
                            putEkycNDIDConditionRequest);

            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(),
                    ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
            response.setData(ekycNdidConditionResponse);

            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(response);

        } catch (Exception e) {
            logger.error("Error calling PUT /customers/ekyc/ndidCondition : {}", e);
            response.setStatus(new TmbStatus(ResponseCode.GENERAL_ERROR.getCode(),
                    ResponseCode.GENERAL_ERROR.getMessage(), ResponseCode.GENERAL_ERROR.getService()));
            return ResponseEntity.badRequest().headers(TMBUtils.getResponseHeaders()).body(response);
        }

    }

    /**
     * Get biometric consent status
     *
     * @param crmId customer Id
     * @return return response
     */
    @Operation(summary = "Get Biometric Consent")
    @LogAround
    @GetMapping(value = "/customers/ekyc/biometricConsent")
    public ResponseEntity<TmbOneServiceResponse<EkycBiometricConsent>> getEkycBiometricConsent(
            @Parameter(description = HEADERS_X_CRMID, example = "001100000000000000000012037178", required = true)
            @RequestHeader(HEADERS_X_CRMID) String crmId) {
        TmbOneServiceResponse<EkycBiometricConsent> response = new TmbOneServiceResponse<>();

        try {
            EkycBiometricConsent ncbPaymentConfirmResponse =
                    ekycNDIDService.getEkycBiometricConsent(crmId);

            if (ncbPaymentConfirmResponse == null) {
                response.setStatus(new TmbStatus(ResponseCode.DATA_NOT_FOUND_ERROR.getCode(),
                        ResponseCode.DATA_NOT_FOUND_ERROR.getMessage(), ResponseCode.DATA_NOT_FOUND_ERROR.getService()));
            } else {
                response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(),
                        ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
            }

            response.setData(ncbPaymentConfirmResponse);

            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(response);

        } catch (Exception e) {
            logger.error("Error calling GET /customers/ekyc/biometricConsent : {}", e);
            response.setStatus(new TmbStatus(ResponseCode.GENERAL_ERROR.getCode(),
                    ResponseCode.GENERAL_ERROR.getMessage(), ResponseCode.GENERAL_ERROR.getService()));
            return ResponseEntity.badRequest().headers(TMBUtils.getResponseHeaders()).body(response);
        }

    }

    /**
     * Update/Insert biometric consent status
     *
     * @param crmId   customer Id
     * @param request accept/revoke status, version
     * @return return response
     */
    @Operation(summary = "Update Biometric Consent")
    @LogAround
    @PostMapping(value = "/customers/ekyc/biometricConsent")
    public ResponseEntity<TmbOneServiceResponse<Void>> postEkycBiometricConsent(
            @Parameter(description = HEADERS_X_CRMID, example = "001100000000000000000000000100", required = true)
            @RequestHeader(HEADERS_X_CRMID) String crmId,
            @RequestBody PostEkycBiometricConsentRequest request) {
        TmbOneServiceResponse<Void> response = new TmbOneServiceResponse<>();

        try {
            boolean postEkycBiometricConsentResponse =
                    ekycNDIDService.postEkycBiometricConsent(crmId, request);

            if (!postEkycBiometricConsentResponse) {
                response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(),
                        ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService()));

                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .headers(TMBUtils.getResponseHeaders())
                        .body(response);

            }

            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(),
                    ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));

            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(response);

        } catch (Exception e) {
            logger.error("Error calling GET /customers/ekyc/biometricConsent : {}", e);
            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService()));
            return ResponseEntity.badRequest().headers(TMBUtils.getResponseHeaders()).body(response);
        }

    }

    /**
     * Get Dip chip data by citizen id
     *
     * @param correlationId   correlation Id
     * @param request accept/revoke status, version
     * @return return response
     */
    @Operation(summary = "Update Biometric Consent")
    @LogAround
    @PostMapping(value = "/customers/get-dipchip-by-citizen-id")
    public ResponseEntity<TmbOneServiceResponse<PostGetDipchipData>> postGetDipchipByCitizenId(
            @Parameter(description = CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @RequestHeader(CORRELATION_ID) String correlationId,
            @RequestBody PostGetDipchipByCidRequest request) {
        TmbOneServiceResponse<PostGetDipchipData> response = new TmbOneServiceResponse<>();

        try {
            PostGetDipchipData data =
                    ekycNDIDService.postGetDipchipByCitizenId(correlationId, request);

            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(),
                    ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
            response.setData(data);

            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(response);

        } catch (Exception e) {
            logger.error("Error calling GET /customers/get-dipchip-by-citizen-id : {}", e);
            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService()));
            return ResponseEntity.badRequest().headers(TMBUtils.getResponseHeaders()).body(response);
        }

    }
}
