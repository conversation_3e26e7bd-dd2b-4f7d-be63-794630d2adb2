package com.tmb.oneapp.customerservice.controller;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADERS_DEVICE_ID;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.tmb.oneapp.customerservice.model.CustGeneralProfileResponse;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.servlet.http.HttpServletRequest;

import com.tmb.oneapp.customerservice.service.CustomerKycForeignerService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.GetMapping;
import com.google.common.base.Strings;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.LogMessageBuilder;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.logger.TTBEventStatus;
import com.tmb.common.model.CustomerProfileStatus;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.CustomerDevice;
import com.tmb.oneapp.customerservice.model.CustomerDeviceInformation;
import com.tmb.oneapp.customerservice.model.CustomerKYCResponse;
import com.tmb.oneapp.customerservice.model.profile.Phone;
import com.tmb.oneapp.customerservice.repository.CustomerDeviceInformationRepository;
import com.tmb.oneapp.customerservice.service.CustomerKycService;
import com.tmb.oneapp.customerservice.service.CustomerPhoneDetailServiceImpl;
import com.tmb.oneapp.customerservice.service.CustomerProfileServiceImpl;
import com.tmb.oneapp.customerservice.service.DeviceNameStatusServiceImpl;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;

import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

@RestController
/**
 * CustomerProfileController request mapping will handle apis call and then
 * navigate to respective method to get customer details
 *
 */
public class CustomerProfileController {
	private static final TMBLogger<CustomerProfileController> logger = new TMBLogger<>(CustomerProfileController.class);
	public static final String ONEAPP_CUSTOMER_KYC = "ONEAPP_CUSTOMER_KYC";
	private final CustomerProfileServiceImpl customerProfileServiceImpl;
	private final DeviceNameStatusServiceImpl deviceNameStatusServiceImpl;
	private final CustomerDeviceInformationRepository customerDeviceInformationRepository;
	private final CustomerKycService customerKycService;
	private final CustomerPhoneDetailServiceImpl customerPhoneDetailservice;
	private final CustomerKycForeignerService customerKycForeignerService;

	@Autowired
	public CustomerProfileController(CustomerProfileServiceImpl customerProfileServiceImpl,
			DeviceNameStatusServiceImpl deviceNameStatusServiceImpl,
			CustomerDeviceInformationRepository customerDeviceInformationRepository,
			CustomerKycService customerKycService, CustomerPhoneDetailServiceImpl customerPhoneDetailservice,
			CustomerKycForeignerService customerKycForeignerService){
		super();
		this.customerProfileServiceImpl = customerProfileServiceImpl;
		this.deviceNameStatusServiceImpl = deviceNameStatusServiceImpl;
		this.customerDeviceInformationRepository = customerDeviceInformationRepository;
		this.customerKycService = customerKycService;
		this.customerPhoneDetailservice = customerPhoneDetailservice;
		this.customerKycForeignerService = customerKycForeignerService;
	}

	/**
	 * Description:- Inquiry crmID and customer details
	 *
	 * @param headers
	 * @return return customer Details
	 */
	@LogAround
	@GetMapping(value = "/customers", produces = MediaType.APPLICATION_JSON_VALUE)
	@Operation(summary = "Get customer details")
	@Parameters({
			@Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true, in = ParameterIn.HEADER),
			@Parameter(name = CustomerServiceConstant.HEADERS_X_CRMID, example ="001100000000000000000018593707", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER) })
	public ResponseEntity<TmbOneServiceResponse<CustGeneralProfileResponse>> getCustomerDetails(
			@Parameter(hidden = true) @RequestHeader Map<String, String> headers,
			@RequestParam(value = "ec_flag", required = false) String ecFlag) {
		String crmId = headers.get(CustomerServiceConstant.HEADERS_X_CRMID);
		logger.info("customers-service getCustomerDetails method start Time : {} ", System.currentTimeMillis());
		TmbOneServiceResponse<CustGeneralProfileResponse> oneServiceResponse = new TmbOneServiceResponse<>();
		logger.info("CustomerProfileController Request Parameter CustomerProfileController crmId : {}", crmId);
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			// call core bank service
			CustGeneralProfileResponse data = customerProfileServiceImpl
					.customersServiceCall(CustomerServiceConstant.RM_ID, crmId, ecFlag);
			oneServiceResponse.setData(data);
			oneServiceResponse
					.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
							ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
			logger.info("customers-service getCustomerDetails method end Time : {} ", System.currentTimeMillis());
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (Exception e) {
			logger.error("Unable to fetch data from core banking service : {}", e);
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

			return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
		}

	}

	/**
	 * Description:- Inquiry customer details by path crmID
	 *
	 * @param crmPatId
	 * @return return customer Details
	 */
	@LogAround
	@GetMapping(value = "/customers/profile-inquiry/{crm}", produces = MediaType.APPLICATION_JSON_VALUE)
	@Operation(summary = "Get customer details by path CRM")
	@Parameters({
			@Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true, in = ParameterIn.HEADER),
			@Parameter(name = CustomerServiceConstant.HEADERS_X_CRMID, example ="001100000000000000000018593707", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER) })
	public ResponseEntity<TmbOneServiceResponse<CustGeneralProfileResponse>> getCustomerDetailsByCrmPath(
			@PathVariable(name = "crm", required = true) String crmPatId) {
		logger.info("customers-service getCustomerDetailsByCrmPath method start Time : {} ",
				System.currentTimeMillis());
		TmbOneServiceResponse<CustGeneralProfileResponse> oneServiceResponse = new TmbOneServiceResponse<>();
		logger.info("getCustomerDetailsByCrmPath Request Parameter CustomerProfileController crmId : {}", crmPatId);
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			// call core bank service
			CustGeneralProfileResponse data = customerProfileServiceImpl
					.customersServiceCall(CustomerServiceConstant.RM_ID, crmPatId, null);
			oneServiceResponse.setData(data);
			oneServiceResponse
					.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
							ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
			logger.info("customers-service getCustomerDetailsByCrmPath method end Time : {} ",
					System.currentTimeMillis());
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (Exception e) {
			logger.error("Unable to fetch data from core banking service : {}", e);
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

			return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
		}

	}

	/**
	 * Description:- Inquiry deviceInfo and customer status
	 *
	 * @param deviceId
	 * @return return customer status data if found
	 */
	@LogAround
	@GetMapping(value = "/customers/{deviceId}")
	public ResponseEntity<TmbOneServiceResponse<CustomerProfileStatus>> checkDeviceNameStatus(
			@PathVariable("deviceId") String deviceId) {
		TmbOneServiceResponse<CustomerProfileStatus> oneServiceResponse = new TmbOneServiceResponse<>();
		logger.info("checkDeviceNameStatus method device id: {}", deviceId);

		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			if (!Strings.isNullOrEmpty(deviceId)) {
				logger.info("inside checkDeviceNameStatus method");
				CustomerProfileStatus deviceNameStatus = deviceNameStatusServiceImpl
						.getCustomerDeviceNameStatus(deviceId , false);
				oneServiceResponse.setData(deviceNameStatus);
				oneServiceResponse
						.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
								ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
				return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);

			} else {
				logger.info("checkDeviceNameStatus method deviceId is empty");
				oneServiceResponse.setStatus(new TmbStatus(ResponseCode.CUSTOMER_NOT_FOUND.getCode(),
						ResponseCode.CUSTOMER_NOT_FOUND.getMessage(), ResponseCode.CUSTOMER_NOT_FOUND.getService(),
						ResponseCode.CUSTOMER_NOT_FOUND.getDesc()));
				return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
			}

		} catch (Exception e) {
			logger.error("Unable to fetch data from oneapp db : {}", e);
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService()));

			return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
		}

	}

	/**
	 * Description:- Inquiry deviceInfo and customer status
	 *
	 * @return return customer status data if found
	 */
	@LogAround
	@GetMapping(value = "/customers/device")
	@Parameters({
			@Parameter(name = HEADERS_DEVICE_ID, example ="34cec72b26b7a30ae0a3eaa48d45d82bc2f69728472d9145d57565885", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER), })
	public ResponseEntity<TmbOneServiceResponse<CustomerProfileStatus>> getCustomersDevice(
			@Parameter(hidden = true) @RequestHeader Map<String, String> requestHeaders) {
		String deviceId = requestHeaders.get(HEADERS_DEVICE_ID);
		TmbOneServiceResponse<CustomerProfileStatus> oneServiceResponse = new TmbOneServiceResponse<>();
		logger.info("getCustomersDevice method device id: {}", deviceId);

		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			if (!Strings.isNullOrEmpty(deviceId)) {
				logger.info("inside getCustomersDevice method");
				CustomerProfileStatus deviceNameStatus = deviceNameStatusServiceImpl
						.getCustomerDeviceNameStatus(deviceId);
				oneServiceResponse.setData(deviceNameStatus);
				oneServiceResponse
						.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
								ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
				return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);

			} else {
				logger.info("getCustomersDevice method deviceId is empty");
				oneServiceResponse.setStatus(new TmbStatus(ResponseCode.CUSTOMER_NOT_FOUND.getCode(),
						ResponseCode.CUSTOMER_NOT_FOUND.getMessage(), ResponseCode.CUSTOMER_NOT_FOUND.getService(),
						ResponseCode.CUSTOMER_NOT_FOUND.getDesc()));
				return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
			}

		} catch (Exception e) {
			logger.error("Unable to fetch data from oneapp db : {}", e);
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService()));

			return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
		}
	}

	@LogAround
	@Operation(summary = "Get Customer's deviceId by CrmId")
	@GetMapping("/customers/device/{crmId}")
	public ResponseEntity<TmbOneServiceResponse<CustomerDevice>> getCustomersDeviceByCrmId(
			@Parameter(name = "crmId", example ="001100000000000000000001184383", required = true) @PathVariable("crmId") String crmId) {

		TmbOneServiceResponse<CustomerDevice> response = new TmbOneServiceResponse<>();

		Optional<CustomerDeviceInformation> queryResult = customerDeviceInformationRepository.findByCrmId(crmId);
		queryResult.ifPresentOrElse(result -> {
			String deviceId = result.getDeviceId();
			logger.info("Customer deviceId found for crm: {}, deviceId: {}", crmId, deviceId);
			response.setData(CustomerDevice.builder().deviceId(deviceId).crmId(crmId).build());
			response.setStatus(CustomerServiceUtils.getResponseFromStatus(ResponseCode.SUCCESS));
		}, () -> {
			logger.info("Customer deviceId not found for crm: {}", crmId);
			response.setStatus(CustomerServiceUtils.getResponseFromStatus(ResponseCode.DATA_NOT_FOUND_ERROR));
		});

		return ResponseEntity.ok(response);
	}

	/**
	 *
	 * @param headers
	 * @return
	 * @throws TMBCommonException
	 */
	@Operation(summary = "Get customer kyc detail")
	@Parameters({
			@Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true, in = ParameterIn.HEADER),
			@Parameter(name = CustomerServiceConstant.HEADERS_X_CRMID, example ="001100000000000000000018593707", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER) })
	@GetMapping(value = { "/customers/kyc", "/customers/kyc/cache" })
	public ResponseEntity<TmbOneServiceResponse<CustomerKYCResponse>> getCustomerKYC(HttpServletRequest request,
			@Parameter(hidden = true) @RequestHeader Map<String, String> headers) throws TMBCommonException {

		LogMessageBuilder logMessageBuilder = LogMessageBuilder.instance(logger);
		logMessageBuilder.start();
		logMessageBuilder.inboundLog(request, null).operationLog(request.getRequestURI())
				.setEventName(ONEAPP_CUSTOMER_KYC);

		String crmId = headers.get(CustomerServiceConstant.HEADERS_X_CRMID);
		TmbOneServiceResponse<CustomerKYCResponse> oneServiceResponse = new TmbOneServiceResponse<>();
		CustomerKYCResponse data = null;
		logger.info("get kyc customer : {}", crmId);
		ResponseEntity<TmbOneServiceResponse<CustomerKYCResponse>> responseEntity;
		try {
			Boolean isCache = false;
			if (request.getRequestURI().contains("kyc/cache")) {
				data = customerKycService.getKYCDataCache(crmId);
				isCache = true;
			}

			if (data == null) {
				data = customerKycService.getKYCData(crmId, false);
				isCache = false;

				boolean isForeigner = StringUtils.equals("AI", data.getIdType())
						|| StringUtils.equals("OT", data.getIdType());
				if (isForeigner) {
					data = customerKycForeignerService.getKYCData(crmId, false);
				}

			}

			oneServiceResponse.setData(data);
			if (Boolean.FALSE.equals(isCache) && data != null) {
				customerKycService.saveKYCDataCache(data);
			}
			oneServiceResponse
					.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
							ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));

			responseEntity = ResponseEntity.ok().body(oneServiceResponse);

			logMessageBuilder.outboundLog(responseEntity.getHeaders(), oneServiceResponse)
					.setStatus(TTBEventStatus.SUCCESS).setHttpStatus(responseEntity.getStatusCodeValue());
		} catch (Exception e) {
			logMessageBuilder.setStatus(TTBEventStatus.FAIL).setHttpStatus(500);
			throw e;
		} finally {
			logMessageBuilder.finish();
		}

		return responseEntity;
	}

	@Operation(summary = "Get customer kyc detail realtime")
	@Parameters({
			@Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true, in = ParameterIn.HEADER),
			@Parameter(name = CustomerServiceConstant.HEADERS_X_CRMID, example ="001100000000000000000018593707", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER) })
	@GetMapping(value = { "/customers/kyc/realtime" })
	public ResponseEntity<TmbOneServiceResponse<CustomerKYCResponse>> getCustomerKYCRealtime(HttpServletRequest request,
																					 @Parameter(hidden = true) @RequestHeader Map<String, String> headers) throws TMBCommonException {

		LogMessageBuilder logMessageBuilder = LogMessageBuilder.instance(logger);
		logMessageBuilder.start();
		logMessageBuilder.inboundLog(request, null).operationLog(request.getRequestURI())
				.setEventName(ONEAPP_CUSTOMER_KYC);

		String crmId = headers.get(CustomerServiceConstant.HEADERS_X_CRMID);
		TmbOneServiceResponse<CustomerKYCResponse> oneServiceResponse = new TmbOneServiceResponse<>();
		CustomerKYCResponse data = null;
		logger.info("get kyc customer : {}", crmId);
		ResponseEntity<TmbOneServiceResponse<CustomerKYCResponse>> responseEntity;
		try {
				data = customerKycService.getKYCData(crmId, true);

				boolean isForeigner = StringUtils.equals("AI", data.getIdType())
						|| StringUtils.equals("OT", data.getIdType());
				if (isForeigner) {
					data = customerKycForeignerService.getKYCData(crmId, true);
				}

			oneServiceResponse.setData(data);
			oneServiceResponse
					.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
							ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));

			responseEntity = ResponseEntity.ok().body(oneServiceResponse);

			logMessageBuilder.outboundLog(responseEntity.getHeaders(), oneServiceResponse)
					.setStatus(TTBEventStatus.SUCCESS).setHttpStatus(responseEntity.getStatusCodeValue());
		} catch (Exception e) {
			logMessageBuilder.setStatus(TTBEventStatus.FAIL).setHttpStatus(500);
			throw e;
		} finally {
			logMessageBuilder.finish();
		}

		return responseEntity;
	}

	@Operation(summary = "Fetch Customer Phone Details")
	@LogAround
	@Parameters({
			@Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true, in = ParameterIn.HEADER),
			@Parameter(name = CustomerServiceConstant.HEADERS_X_CRMID, example ="001100000000000000000018593707", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER) })
	@GetMapping(value = "/customers/details/phone")
	public ResponseEntity<TmbServiceResponse<List<Phone>>> getPhonebyCustomerDetail(@RequestHeader HttpHeaders headers)
			throws TMBCommonException {
		logger.info("CustomerProfileController  getPhonebyCustomerDetail method start Time : {} ",
				System.currentTimeMillis());
		TmbServiceResponse<List<Phone>> oneServiceResponse = new TmbServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			oneServiceResponse.setData(customerPhoneDetailservice.getPhonebyCustomerProfile(headers));
			oneServiceResponse.setStatus(returnSuccessStatus());
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (TMBCommonException tmbcommon) {
			logger.error("getPhonebyCustomerDetail : TMBCommonException : {}", tmbcommon);
			throw tmbcommon;
		} catch (Exception e) {
			logger.error("getPhonebyCustomerDetail : Exception : {}", e);
			throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
		}
	}

	@Operation(summary = "clear customer kyc cache")
	@Parameters({
			@Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID,
					example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true, in = ParameterIn.HEADER),
			@Parameter(name = CustomerServiceConstant.HEADERS_X_CRMID,
					example = "001100000000000000000018593707",
					required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER) })
	@GetMapping("/customers/kyc/cache/clear" )
	public ResponseEntity<TmbOneServiceResponse<Void>> clearCustomerKycCache(HttpServletRequest request,
																			 @Parameter(hidden = true) @RequestHeader
																			 Map<String, String> headers) {
		LogMessageBuilder logMessageBuilder = LogMessageBuilder.instance(logger);
		TmbOneServiceResponse<Void> oneServiceResponse = new TmbOneServiceResponse<>();
		try {
			logMessageBuilder.start();
			logMessageBuilder.inboundLog(request, null).operationLog(request.getRequestURI())
					.setEventName(ONEAPP_CUSTOMER_KYC);
			String crmId = headers.get(CustomerServiceConstant.HEADERS_X_CRMID);
			logger.info("clear kyc customer cache - crmId: {}", crmId);
			customerKycService.deleteKYCDataCache(crmId);
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
							ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
			ResponseEntity<TmbOneServiceResponse<Void>> responseEntity = ResponseEntity.ok().body(oneServiceResponse);
			logMessageBuilder.outboundLog(responseEntity.getHeaders(), oneServiceResponse)
					.setStatus(TTBEventStatus.SUCCESS).setHttpStatus(responseEntity.getStatusCodeValue());
			return responseEntity;
		} catch (Exception e) {
			logMessageBuilder.setStatus(TTBEventStatus.FAIL).setHttpStatus(500);
			throw e;
		} finally {
			logMessageBuilder.finish();
		}
	}

	@LogAround
	private Status returnSuccessStatus() {
		return new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
				ResponseCode.SUCCESS.getService(), CustomerServiceUtils.returnSuccessDescription());
	}

}