package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.InitializeFaceRecognitionFeatureRequest;
import com.tmb.oneapp.customerservice.model.PersonalizedFaceRecognitionResponse;
import com.tmb.oneapp.customerservice.model.UpdateFaceAuthenticationSettingRequest;
import com.tmb.oneapp.customerservice.service.FaceAuthenticationSettingService;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import jakarta.validation.Valid;
import java.util.List;

import static com.tmb.oneapp.customerservice.utils.CustomerServiceUtils.getResponseFromStatus;

@Controller
@Tag(name = "Face Authentication Setting")
public class FaceAuthenticationSettingController {
    private static final TMBLogger<FaceAuthenticationSettingController> logger = new TMBLogger<>(FaceAuthenticationSettingController.class);

    private final FaceAuthenticationSettingService faceAuthenticationSettingService;

    public FaceAuthenticationSettingController(FaceAuthenticationSettingService faceAuthenticationSettingService) {
        this.faceAuthenticationSettingService = faceAuthenticationSettingService;
    }

    @LogAround
    @Operation(summary = "Get all feature face authentication")
    @GetMapping(value = "/customers/face-authentication/all-feature")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="ae40d276-35f4-4512-b010-7bde6a32eb8d", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = CustomerServiceConstant.X_HEADER_CRMID, example ="001100000000000000000001184383", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER)
    })
    public ResponseEntity<TmbOneServiceResponse<List<PersonalizedFaceRecognitionResponse>>> getAllFeatureFaceAuthentication(@RequestHeader HttpHeaders headers)
            throws TMBCommonException {

        TmbOneServiceResponse<List<PersonalizedFaceRecognitionResponse>> response = new TmbOneServiceResponse<>();
        try {
            List<PersonalizedFaceRecognitionResponse> faceAuthenticationFeature = faceAuthenticationSettingService.get(headers);
            logger.info("fetch feature face authentication success response: {} ", faceAuthenticationFeature);

            response.setStatus(getResponseFromStatus(ResponseCode.SUCCESS));
            response.setData(faceAuthenticationFeature);

            HttpHeaders responseHeader = TMBUtils.getResponseHeaders();

            return ResponseEntity.ok().headers(responseHeader).body(response);
        } catch (Exception e) {
            logger.error("Error while get personalized face recognition ", e);
            throw getTMBCommonException();
        }
    }

    @LogAround
    @Operation(summary = "Update feature face authentication")
    @PostMapping(value = "/customers/face-authentication/all-feature")
    public ResponseEntity<TmbOneServiceResponse<String>> updateFeatureFaceAuthentication(
            @Parameter(description = "CRMID", example ="001100000000000000000001184383", required = true) @RequestHeader(name = "X-CRMID", required = false) String crmId,
            @Parameter(description = "Correlation ID", example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @RequestBody UpdateFaceAuthenticationSettingRequest updateFaceAuthenticationSettingRequest)
               throws TMBCommonException {
        TmbOneServiceResponse <String> response = new TmbOneServiceResponse<>();
        try {
            faceAuthenticationSettingService.update(crmId, updateFaceAuthenticationSettingRequest);
            logger.info("fetch feature face authentication success {} response: {} ", correlationId);

            response.setStatus(getResponseFromStatus(ResponseCode.SUCCESS));
            response.setData(CustomerServiceConstant.SUCCESS_MESSAGE);

            HttpHeaders responseHeader = TMBUtils.getResponseHeaders();

            return ResponseEntity.ok().headers(responseHeader).body(response);
        } catch (Exception e) {
            logger.error("Error while update personalized face recognition ", e);
            throw getTMBCommonException();
        }
    }

    @LogAround
    @Operation(summary = "Insert all feature to personalize face recognition")
    @PostMapping(value = "/customers/face-authentication/initialize-feature")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="ae40d276-35f4-4512-b010-7bde6a32eb8d", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = CustomerServiceConstant.X_HEADER_CRMID, example ="001100000000000000000001184383", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER)
    })
    public ResponseEntity<TmbOneServiceResponse<List<PersonalizedFaceRecognitionResponse>>> initializeFeatureFaceAuthentication(
            @RequestHeader HttpHeaders headers, @RequestBody InitializeFaceRecognitionFeatureRequest request)
            throws TMBCommonException {

        TmbOneServiceResponse<List<PersonalizedFaceRecognitionResponse>> response = new TmbOneServiceResponse<>();
        try {
            List<PersonalizedFaceRecognitionResponse> faceAuthenticationFeature = faceAuthenticationSettingService.insertFeature(headers, request);
            logger.info(" Insert data personalize face recognition feature success");

            response.setStatus(getResponseFromStatus(ResponseCode.SUCCESS));
            response.setData(faceAuthenticationFeature);

            HttpHeaders responseHeader = TMBUtils.getResponseHeaders();

            return ResponseEntity.ok().headers(responseHeader).body(response);
        } catch (Exception e) {
            logger.error("Error while get personalized face recognition ", e);
            throw getTMBCommonException();
        }
    }

    private TMBCommonException getTMBCommonException(){
        return new TMBCommonException(ResponseCode.GENERAL_ERROR.getCode(),
                ResponseCode.GENERAL_ERROR.getMessage(),
                ResponseCode.GENERAL_ERROR.getService(),
                HttpStatus.INTERNAL_SERVER_ERROR,
                null);
    }
}