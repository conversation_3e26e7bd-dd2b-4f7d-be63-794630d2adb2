package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.CalculateRiskRequest;
import com.tmb.oneapp.customerservice.model.ekycriskcalculate.RiskCalculateResponseBody;
import com.tmb.oneapp.customerservice.service.V1KYCRiskCalculateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Tag(name = "Customer EKYC Api V1")
public class V1KYCRiskCalculateController {

    private final V1KYCRiskCalculateService v1KYCRiskCalculateService;

    public V1KYCRiskCalculateController(V1KYCRiskCalculateService v1KYCRiskCalculateService) {
        this.v1KYCRiskCalculateService = v1KYCRiskCalculateService;
    }
    @Operation(summary = "V1 Risk Calculator")
    @LogAround
    @PostMapping(value = "/kyc/csgw/risk-calculate")
    public ResponseEntity<TmbServiceResponse<RiskCalculateResponseBody>> getCustomerCalculateRisk(
            @Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader(value = CustomerServiceConstant.CORRELATION_ID,required = false) String correlationId,
            @RequestBody CalculateRiskRequest request) throws TMBCommonException {
        return ResponseEntity.ok()
                .headers(TMBUtils.getResponseHeaders())
                .body(v1KYCRiskCalculateService.getCustomerCalculateRisk(correlationId, request));

    }
}
