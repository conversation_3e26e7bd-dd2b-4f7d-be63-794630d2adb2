package com.tmb.oneapp.customerservice.controller.crs;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Description;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.customerservice.constant.CommonReportingStandardConstants;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.service.crs.CommonReportingStandardService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class CommonReportingStandardController {
    private static final TMBLogger<CommonReportingStandardController> logger =
            new TMBLogger<>(CommonReportingStandardController.class);
    @Autowired
    private CommonReportingStandardService commonReportingStandardService;

    @LogAround
    @Parameters({
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID,
                    description = CustomerServiceConstant.HEADER_CORRELATION_ID,
                    example = "c28f91e4-881e-4387-a597-4a39c2822b3c",
                    in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADERS_X_CRMID,
                    description = CustomerServiceConstant.HEADERS_X_CRMID,
                    example = "001100000000000000000012003333", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CommonReportingStandardConstants.HEADER_ANSWER_FLAG,
                    description = CommonReportingStandardConstants.HEADER_ANSWER_FLAG,
                    example = "Y",
                    in = ParameterIn.HEADER, required = true)
    })
    @PostMapping(value = "/customers/common-reporting-standard/create", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<String>> create(@RequestHeader HttpHeaders headers)
            throws TMBCommonException {
        TmbServiceResponse<String> response = new TmbServiceResponse<>();
        String correlationId = headers.getFirst(CustomerServiceConstant.HEADER_CORRELATION_ID);
        String crmId = headers.getFirst(CustomerServiceConstant.HEADERS_X_CRMID);
        String answerFlag = headers.getFirst(CommonReportingStandardConstants.HEADER_ANSWER_FLAG);
        logger.info("Start CommonReportingStandardService create with Correlation: {} CrmId: {} AnswerFlag: {}",
                correlationId, crmId, answerFlag);
        commonReportingStandardService.create(correlationId, crmId, answerFlag);
        response.setStatus(getStatusSuccess());
        return ResponseEntity.ok(response);
    }

    private Status getStatusSuccess() {
        Description description = new Description(ResponseCode.SUCCESS.getDesc(), ResponseCode.SUCCESS.getDesc());
        return new Status(ResponseCode.SUCCESS.getCode(),
                ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(),
                description);
    }
}
