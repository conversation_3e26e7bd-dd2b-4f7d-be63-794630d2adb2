package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.pdpa.PDPAFormResponse;
import com.tmb.oneapp.customerservice.model.pdpa.PDPAGetConsentCustomer;
import com.tmb.oneapp.customerservice.model.pdpa.PDPAGetConsentResponse;
import com.tmb.oneapp.customerservice.model.pdpa.PDPAUpdateConsentServiceRequest;
import com.tmb.oneapp.customerservice.service.PDPAService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

import static com.tmb.oneapp.customerservice.utils.CustomerServiceUtils.getResponseFromStatus;
import static com.tmb.oneapp.customerservice.utils.CustomerServiceUtils.getTMBCommonException;

@RestController
@Tag(name = "PDPA consent")
public class PDPAController {
    private static final TMBLogger<PDPAController> logger = new TMBLogger<>(PDPAController.class);

    private PDPAService pdpaService;

    public PDPAController(PDPAService pdpaService) {
        this.pdpaService = pdpaService;
    }

    @LogAround
    @Operation(summary = "Get PDPA-Market consent")
    @GetMapping(value = "/customers/pdpa-market/consent")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.X_CRMID, description = "X-crmId", example = "001100000000000000000012003333", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = "X-Correlation-ID", example = "c28f91e4-881e-4387-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true)})
    public ResponseEntity<TmbOneServiceResponse<PDPAGetConsentCustomer>> getPdpaConsent(@RequestHeader HttpHeaders headers) throws TMBCommonException {

        TmbOneServiceResponse<PDPAGetConsentCustomer> response = new TmbOneServiceResponse<>();
        try {
            String crmId = headers.getFirst(CustomerServiceConstant.X_CRMID);

            PDPAGetConsentResponse pdpaGetConsentResponse = pdpaService.getPdpaConsent(crmId);
            logger.info("Get PDPA consent success by crmId : {} ", crmId);

            if (pdpaGetConsentResponse.getStatus().getCode().equals(CustomerServiceConstant.ETE_DATA_NOT_FOUND_ERROR_CODE)) {
                response.setStatus(new TmbStatus(CustomerServiceConstant.ETE_DATA_NOT_FOUND_ERROR_CODE,
                        CustomerServiceConstant.DATA_NOT_FOUND,
                        CustomerServiceConstant.CUSTOMER_SERVICE_NAME,
                        null));
            } else {
                response.setStatus(getResponseFromStatus(ResponseCode.SUCCESS));
            }
            response.setData(pdpaGetConsentResponse.getCustomer());

        } catch (Exception e) {
            logger.error("Error while get PDPA consent : {} ", e);
            throw getTMBCommonException(ResponseCode.GENERAL_ERROR);
        }

        HttpHeaders responseHeader = TMBUtils.getResponseHeaders();

        return ResponseEntity.ok().headers(responseHeader).body(response);
    }

    @LogAround
    @Operation(summary = "Get PDPA-Market form")
    @GetMapping(value = "/customers/pdpa-market/getForm")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.X_CRMID, description = "X-crmId", example = "001100000000000000000012003333", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_TYPE_OF_CONSENT, description = "Type-of-consent", example = "pdpa", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = "X-Correlation-ID", example = "c28f91e4-881e-4387-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true)})
    public ResponseEntity<TmbOneServiceResponse<PDPAFormResponse>> getPdpaForm(@RequestHeader HttpHeaders headers) throws TMBCommonException {

        TmbOneServiceResponse<PDPAFormResponse> response = new TmbOneServiceResponse<>();
        try {
            String crmId = headers.getFirst(CustomerServiceConstant.X_CRMID);

            PDPAFormResponse pdpaGetConsentResponse = pdpaService.getPdpaForm(headers);
            logger.info("Get PDPA form success by crmId : {} ", crmId);

            response.setStatus(getResponseFromStatus(ResponseCode.SUCCESS));
            response.setData(pdpaGetConsentResponse);

        } catch (Exception e) {
            logger.error("Error while get PDPA form : {} ", e);
            throw getTMBCommonException(ResponseCode.GENERAL_ERROR);
        }

        HttpHeaders responseHeader = TMBUtils.getResponseHeaders();

        return ResponseEntity.ok().headers(responseHeader).body(response);
    }

    @LogAround
    @Operation(summary = "Update PDPA-Market consent")
    @PostMapping(value = "/customers/pdpa-market/consent-update")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.X_CRMID, description = "X-crmId", example = "001100000000000000000012003333", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = "X-Correlation-ID", example = "c28f91e4-881e-4387-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true)})
    public ResponseEntity<TmbOneServiceResponse<PDPAGetConsentResponse>> updatePdpaConsent(@RequestHeader HttpHeaders headers,
                                                                                           @Valid @RequestBody PDPAUpdateConsentServiceRequest request) throws TMBCommonException {

        TmbOneServiceResponse<PDPAGetConsentResponse> response = new TmbOneServiceResponse<>();
        try {
            String crmId = headers.getFirst(CustomerServiceConstant.X_CRMID);

            PDPAGetConsentResponse pdpaGetConsentResponse = pdpaService.updatePdpaConsent(headers, request);
            logger.info("Update PDPA form success by crmId : {} ", crmId);

            response.setStatus(getResponseFromStatus(ResponseCode.SUCCESS));
            response.setData(pdpaGetConsentResponse);

        } catch (Exception e) {
            logger.error("Error while update PDPA form : {} ", e);
            throw getTMBCommonException(ResponseCode.GENERAL_ERROR);
        }

        HttpHeaders responseHeader = TMBUtils.getResponseHeaders();

        return ResponseEntity.ok().headers(responseHeader).body(response);
    }
}
