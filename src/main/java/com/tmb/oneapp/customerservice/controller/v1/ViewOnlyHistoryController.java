package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Description;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.ViewOnlyHistoryDTO;
import com.tmb.oneapp.customerservice.mongodb.customer.mapper.ViewOnlyHistoryMapper;
import com.tmb.oneapp.customerservice.service.ViewOnlyHistoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
public class ViewOnlyHistoryController {
    private static final TMBLogger<ViewOnlyHistoryController> logger = new TMBLogger<>(ViewOnlyHistoryController.class);
    private final ViewOnlyHistoryService viewOnlyHistoryService;

    @Operation(summary = "get view only history information")
    @GetMapping(value = "/view-only-history/{account_no}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Parameters({
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = CustomerServiceConstant.HEADER_CORRELATION_ID, example = "c28f91w5-881e-4386-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true)
    })
    public ResponseEntity<TmbServiceResponse<List<ViewOnlyHistoryDTO>>> getViewOnlyHistory(
            @RequestHeader HttpHeaders headers,
            @RequestHeader(name = CustomerServiceConstant.X_HEADER_CRMID) String crmId,
            @PathVariable(value = "account_no") String accountNo
    ) throws TMBCommonException {
        logger.info("##### START Get View Only History [CRM ID: {}, Account Number :{}] ", crmId, TMBUtils.maskAccountId(accountNo));
        TmbServiceResponse<List<ViewOnlyHistoryDTO>> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        response.setData(ViewOnlyHistoryMapper.INSTANCE.toViewOnlyHistoryDTOS(viewOnlyHistoryService.getViewOnlyHistory(crmId, accountNo)));
        logger.info("##### END Get View Only History");
        return ResponseEntity.ok(response);
    }

    private Status getStatusSuccess() {
        Description description = new Description(ResponseCode.SUCCESS.getDesc(), ResponseCode.SUCCESS.getDesc());
        return new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), description);
    }
}
