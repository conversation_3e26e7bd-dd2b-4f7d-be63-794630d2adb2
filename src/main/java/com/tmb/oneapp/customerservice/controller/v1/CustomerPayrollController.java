package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.service.CustomerPayrollService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import static com.tmb.oneapp.customerservice.utils.CustomerServiceUtils.getTMBCommonException;

@RestController
public class CustomerPayrollController {
    private static final TMBLogger<CustomerPayrollController> logger = new TMBLogger<>(CustomerPayrollController.class);
    private final CustomerPayrollService customerPayrollService;

    public CustomerPayrollController(CustomerPayrollService customerPayrollService) {
        this.customerPayrollService = customerPayrollService;
    }

    @LogAround
    @Operation(summary = "Get Customer Payroll Data")
    @GetMapping(value = {"/customer-payroll/{wowRefId}"})
    public ResponseEntity<TmbOneServiceResponse<Integer>> getCustomerPayroll(
            @Parameter(description = "wow-ref-id", example ="P00000002519488", required = true) @PathVariable("wowRefId") String wowRefId,
            @Parameter(description = "X-Correlation-ID", example ="b605a35e-106b-4ab2-a53c-30ac8908133b", required = true) @RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId) throws TMBCommonException {

        TmbOneServiceResponse<Integer> response = new TmbOneServiceResponse<>();
        try {
            response.setData(customerPayrollService.checkCustomerPayroll(wowRefId));
            response.setStatus(new TmbStatus(
                    ResponseCode.SUCCESS.getCode(),
                    ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(),
                    null));
            HttpHeaders responseHeader = TMBUtils.getResponseHeaders();
            return ResponseEntity.ok().headers(responseHeader).body(response);
        } catch (Exception e) {
            throw getTMBCommonException(ResponseCode.GENERAL_ERROR);
        }
    }
}
