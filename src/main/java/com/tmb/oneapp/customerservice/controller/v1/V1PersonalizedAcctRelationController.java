package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Description;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.personalizedacctrelation.PersonalizedAcctRelationMapper;
import com.tmb.oneapp.customerservice.model.personalizedacctrelation.model.AccountSettingResponseModel;
import com.tmb.oneapp.customerservice.model.personalizedacctrelation.model.PersonalizedAcctRelationRequest;
import com.tmb.oneapp.customerservice.model.personalizedacctrelation.model.PersonalizedAcctRelationResponse;
import com.tmb.oneapp.customerservice.service.PersonalizedAcctRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import jakarta.validation.constraints.NotNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/personalized-acct-relation")
public class V1PersonalizedAcctRelationController {
    private static final TMBLogger<V1PersonalizedAcctRelationController> logger = new TMBLogger<>(V1PersonalizedAcctRelationController.class);
    private final PersonalizedAcctRelationService personalizedAcctRelationService;

    public V1PersonalizedAcctRelationController(PersonalizedAcctRelationService personalizedAcctRelationService) {
        this.personalizedAcctRelationService = personalizedAcctRelationService;
    }

    @LogAround
    @Operation(summary = "get personalized account relation")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.X_CRMID, description = CustomerServiceConstant.X_CRMID, example ="c28f91e4-881r-4387-a697-4a39c2821b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="c28d92e5-881e-4386-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_APP_VERSION, description = CustomerServiceConstant.HEADER_APP_VERSION, example ="8.0.0", in = ParameterIn.HEADER, required = true)
    })
    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<List<PersonalizedAcctRelationResponse>>> getPersonalizedAcctRelation(
            @RequestHeader HttpHeaders headers,
            @RequestHeader(CustomerServiceConstant.X_CRMID) @NotNull String crmId
    ) {
        logger.info("##### START Get Personalized Acct Relation [CRM ID: {}] ", crmId);
        TmbServiceResponse<List<PersonalizedAcctRelationResponse>> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        response.setData(PersonalizedAcctRelationMapper.INSTANCE.toDTOList(personalizedAcctRelationService.getPersonalizedAcctRelationByCrmId(crmId)));
        logger.info("##### END /personalized-acct-relation");

        return ResponseEntity.ok(response);
    }
    @LogAround
    @Operation(summary = "create personalized account relation")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.X_CRMID, description = CustomerServiceConstant.X_CRMID, example ="c28f91e4-881r-4387-a697-4a39c2821g3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="c28d92c5-881e-4386-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_APP_VERSION, description = CustomerServiceConstant.HEADER_APP_VERSION, example ="8.0.0", in = ParameterIn.HEADER, required = true)
    })
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<String>> createPersonalizedAcctRelation(
            @RequestHeader HttpHeaders headers,
            @RequestHeader(CustomerServiceConstant.X_CRMID) @NotNull String crmId,
            @RequestBody PersonalizedAcctRelationRequest personalizedAcctRelationRequest
            ) {
        logger.info("##### START Create Personalized Acct Relation [CRM ID: {}, PERSONALIZED ACCT RELATION REQUEST: {}] ", crmId, personalizedAcctRelationRequest);
        personalizedAcctRelationService.createPersonalizedAcctRelation(headers, personalizedAcctRelationRequest);
        TmbServiceResponse<String> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        response.setData(ResponseCode.SUCCESS.getMessage());
        logger.info("##### END /personalized-acct-relation  create");

        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "update personalized account relation")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.X_CRMID, description = CustomerServiceConstant.X_CRMID, example ="c28f91e4-881r-4387-a697-4a39c2821h3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="c28d92c5-881s-4386-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_APP_VERSION, description = CustomerServiceConstant.HEADER_APP_VERSION, example ="8.0.0", in = ParameterIn.HEADER, required = true)
    })
    @PutMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<String>> updatePersonalizedAcctRelation(
            @RequestHeader HttpHeaders headers,
            @RequestHeader(CustomerServiceConstant.X_CRMID) @NotNull String crmId,
            @RequestBody PersonalizedAcctRelationRequest personalizedAcctRelationRequest
    ) {
        logger.info("##### START Update Personalized Acct Relation [CRM ID: {}, PERSONALIZED ACCT RELATION REQUEST: {}] ", crmId, personalizedAcctRelationRequest);
        personalizedAcctRelationService.updatePersonalizedAcctRelation(headers, personalizedAcctRelationRequest);
        TmbServiceResponse<String> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        response.setData(ResponseCode.SUCCESS.getMessage());
        logger.info("##### END /personalized-acct-relation  update");

        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "update all personalized account relation")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.X_CRMID, description = CustomerServiceConstant.X_CRMID, example ="c28f91e4-881r-4387-a697-4a39c2821h3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="c28d92c5-881s-4386-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_APP_VERSION, description = CustomerServiceConstant.HEADER_APP_VERSION, example ="8.0.0", in = ParameterIn.HEADER, required = true)
    })
    @PutMapping(value = "/all", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<String>> updateAllPersonalizedAcctRelation(
            @RequestHeader HttpHeaders headers,
            @RequestHeader(CustomerServiceConstant.X_CRMID) @NotNull String crmId,
            @RequestBody List<PersonalizedAcctRelationRequest> personalizedAcctRelationRequestList
    ) {
        logger.info("##### START Update Personalized Acct Relation [CRM ID: {}, PERSONALIZED ACCT RELATION REQUEST: {}] ", crmId, personalizedAcctRelationRequestList);
        personalizedAcctRelationService.updateAllPersonalizedAcctRelation(crmId, personalizedAcctRelationRequestList);
        TmbServiceResponse<String> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        response.setData(ResponseCode.SUCCESS.getMessage());
        logger.info("##### END /personalized-acct-relation  update");

        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "delete personalized account relation")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.X_CRMID, description = CustomerServiceConstant.X_CRMID, example ="c28e91e4-881r-4387-a697-4a39c2821h3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="c28e92c5-881s-4386-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_APP_VERSION, description = CustomerServiceConstant.HEADER_APP_VERSION, example ="8.0.0", in = ParameterIn.HEADER, required = true)
    })
    @DeleteMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<String>> deletePersonalizedAcctRelation(
            @RequestHeader HttpHeaders headers,
            @RequestHeader(CustomerServiceConstant.X_CRMID) @NotNull String crmId,
            @RequestBody PersonalizedAcctRelationRequest personalizedAcctRelationRequest
    ) {
        logger.info("##### START Delete Personalized Acct Relation [CRM ID: {}, PERSONALIZED ACCT RELATION REQUEST: {}] ", crmId, personalizedAcctRelationRequest);
        personalizedAcctRelationService.deletePersonalizedAcctRelation(crmId, personalizedAcctRelationRequest);
        TmbServiceResponse<String> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        response.setData(ResponseCode.SUCCESS.getMessage());
        logger.info("##### END /personalized-acct-relation  delete");

        return ResponseEntity.ok(response);
    }
    @LogAround
    @Operation(summary = "get list of account setting")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.X_CRMID, description = CustomerServiceConstant.X_CRMID, example ="c28f91e4-881r-4387-a697-4a39c2811b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="c28d92e5-882e-4386-a597-4a39c2822b3c", in = ParameterIn.HEADER, required = true),
            @Parameter(name = CustomerServiceConstant.HEADER_APP_VERSION, description = CustomerServiceConstant.HEADER_APP_VERSION, example ="8.0.0", in = ParameterIn.HEADER, required = true)
    })
    @GetMapping(value = "/account-setting", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<List<AccountSettingResponseModel>>> getListAccountSetting(
            @RequestHeader HttpHeaders headers,
            @RequestHeader(CustomerServiceConstant.X_CRMID) @NotNull String crmId
    ) {
        logger.info("##### START Get List of Account Setting [CRM ID: {}] ", crmId);
        TmbServiceResponse<List<AccountSettingResponseModel>> response = new TmbServiceResponse<>();
        response.setStatus(getStatusSuccess());
        response.setData(personalizedAcctRelationService.getListAccountSetting(crmId));
        logger.info("##### END /personalized-acct-relation/account-setting");

        return ResponseEntity.ok(response);
    }

    private Status getStatusSuccess() {
        Description description = new Description(ResponseCode.SUCCESS.getDesc(), ResponseCode.SUCCESS.getDesc());
        return new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), description);
    }
}
