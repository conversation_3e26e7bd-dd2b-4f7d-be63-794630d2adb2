package com.tmb.oneapp.customerservice.controller;

import java.time.Instant;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.base.Strings;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.service.CustomerDetailsServiceImpl;

@RestController
/**
 * CustomersDetailController request mapping will handle apis call and then
 * navigate to respective method to get customer status
 *
 */
public class CustomersDetailController {
	private static final TMBLogger<CustomersDetailController> logger = new TMBLogger<>(CustomersDetailController.class);

	private final CustomerDetailsServiceImpl customerDetailsServiceImpl;

	/**
	 * doing dependency injection for CustomerDetailsServiceImpl through
	 * CustomersDetailController Constructor
	 * 
	 * @param customerDetailsServiceImpl
	 */
	@Autowired
	public CustomersDetailController(CustomerDetailsServiceImpl customerDetailsServiceImpl) {
		super();
		this.customerDetailsServiceImpl = customerDetailsServiceImpl;
	}

	/**
	 * Description:- Inquiry deviceInfo and customer status
	 * 
	 * @param deviceId
	 * @return return crmid if found
	 */
	@GetMapping(value = "/customer/{deviceId}")
	public ResponseEntity<TmbOneServiceResponse<String>> getCustomerProfile(@PathVariable("deviceId") String deviceId) {
		logger.info("customers-service getCustomerProfile method start Time : {} ", System.currentTimeMillis());
		TmbOneServiceResponse<String> oneServiceResponse = new TmbOneServiceResponse<>();
		logger.info("[CustomersServiceController][getCustomerProfile] [deviceId]==== : {}", deviceId);

		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			if (!Strings.isNullOrEmpty(deviceId)) {
				String crmId = customerDetailsServiceImpl.getCustomerStatus(deviceId);

				if (!Strings.isNullOrEmpty(crmId)) {
					oneServiceResponse.setData(crmId);
					oneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(),
							ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
					logger.info("customers-service getCustomerProfile method end Time : {} ",
							System.currentTimeMillis());
					return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
				} else {
					oneServiceResponse.setData(CustomerServiceConstant.CUSTOMER_DATA_NOT_FOUND);
					oneServiceResponse.setStatus(new TmbStatus(ResponseCode.DATA_NOT_FOUND.getCode(),
							ResponseCode.DATA_NOT_FOUND.getMessage(), ResponseCode.DATA_NOT_FOUND.getService()));

					return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
				}

			} else {
				oneServiceResponse.setData(CustomerServiceConstant.DEVICEID_DATA_NOT_FOUND);
				oneServiceResponse.setStatus(new TmbStatus(ResponseCode.DATA_NOT_FOUND.getCode(),
						ResponseCode.DATA_NOT_FOUND.getMessage(), ResponseCode.DATA_NOT_FOUND.getService()));
				return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
			}

		} catch (Exception e) {
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService()));

			return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
		}
	}
}
