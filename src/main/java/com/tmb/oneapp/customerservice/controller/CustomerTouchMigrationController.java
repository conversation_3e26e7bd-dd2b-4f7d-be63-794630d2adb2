package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.MigrationDetails;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.CustomerProfileStatusResponse;
import com.tmb.oneapp.customerservice.service.CustomerStatusImpl;
import com.tmb.oneapp.customerservice.service.UboServiceImpl;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;

/**
 * CustomerTouchMigrationController request mapping will handle apis call and
 * then navigate to respective method to get migration details
 */
@RestController
@Tag(name = "Customer Touch Migration Api")
public class CustomerTouchMigrationController {
    private final CustomerStatusImpl customerStatusImpl;
    private final UboServiceImpl uboServiceImpl;
    private static final TMBLogger<CustomerTouchMigrationController> logger = new TMBLogger<>(
            CustomerTouchMigrationController.class);

    /**
     * Constructor
     *
     * @param customerStatusImpl
     * @param uboServiceImpl
     */
    @Autowired
    public CustomerTouchMigrationController(CustomerStatusImpl customerStatusImpl, UboServiceImpl uboServiceImpl) {
        super();
        this.customerStatusImpl = customerStatusImpl;
        this.uboServiceImpl = uboServiceImpl;
    }

    /**
     * Description:- Inquiry deviceInfo and touch customer data
     *
     * @param deviceId
     * @return return migration data if found
     */
    @LogAround
    @Operation(summary = "Fetch Touch Device Data")
    @GetMapping(value = "/customers/device/migrate/{Device_ID}")
    public ResponseEntity<TmbOneServiceResponse<MigrationDetails>> fetchTouchDeviceData(
            @Parameter(description = "DeviceID", example = "1A825E93-593A-4E42-B86B-CC7D662FEF80", required = true) @PathVariable("Device_ID") String deviceId,
            @Parameter(description = "X-Correlation-ID", example = "b605a35e-106b-4ab2-a53c-30ac8908133b", required = true) @RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId) {
        TmbOneServiceResponse<MigrationDetails> oneServiceResponse = new TmbOneServiceResponse<>();
        logger.info("fetchTouchDeviceData method device id: {}", deviceId);
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

        oneServiceResponse.setStatus(new TmbStatus(ResponseCode.CUSTOMER_NOT_FOUND.getCode(),
                ResponseCode.CUSTOMER_NOT_FOUND.getMessage(), ResponseCode.CUSTOMER_NOT_FOUND.getService(),
                ResponseCode.CUSTOMER_NOT_FOUND.getDesc()));
        return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
    }

    /**
     * Description:- Inquiry customer status
     *
     * @param crmId
     * @return return migration data if found
     */
    @LogAround
    @Operation(summary = "Check Customer Status")
    @GetMapping(value = {"/customers/profile", "/internal/digital/profile"})
    public ResponseEntity<TmbOneServiceResponse<CustomerProfileStatusResponse>> checkCustomerStatus(
            @Parameter(description = "CRMID", example = "001100000000000000000001184383", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CUSTOMER_CRM_ID) String crmId,
            @Parameter(description = "X-Correlation-ID", example = "b605a35e-106b-4ab2-a53c-30ac8908133b", required = true) @RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId) {
        TmbOneServiceResponse<CustomerProfileStatusResponse> oneServiceResponse = new TmbOneServiceResponse<>();
        logger.info("checkCustomerStatus method crm id: {}", crmId);
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

        try {
            CustomerProfileStatusResponse status = customerStatusImpl.getCustomerStatus(crmId);
            oneServiceResponse.setData(status);
            oneServiceResponse
                    .setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                            ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
            return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
        } catch (Exception e) {
            logger.error("Unable to fetch data from oneapp db : {}", e);
            oneServiceResponse.setStatus(new TmbStatus(ResponseCode.DATABASE_CONNECTION_ERROR.getCode(),
                    ResponseCode.DATABASE_CONNECTION_ERROR.getMessage(),
                    ResponseCode.DATABASE_CONNECTION_ERROR.getService(),
                    ResponseCode.DATABASE_CONNECTION_ERROR.getDesc()));

            return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);

        }

    }

    /**
     * Description:- Inquiry UBO ete service data
     *
     * @param crmId
     * @return return migration data if found
     */

    @LogAround
    @Operation(summary = "Fetch UBO Details")
    @GetMapping(value = "/customers/age/{CRM_ID}")
    public ResponseEntity<TmbOneServiceResponse<MigrationDetails>> uboServiceDetails(
            @Parameter(description = "CRMID", example = "001100000000000000000001184383", required = true) @PathVariable("CRM_ID") String crmId,
            @Parameter(description = "X-Correlation-ID", example = "b605a35e-106b-4ab2-a53c-30ac8908133b", required = true) @RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId)
            throws Exception {
        TmbOneServiceResponse<MigrationDetails> oneServiceResponse = new TmbOneServiceResponse<>();
        logger.info("UboServiceDetails method crm id: {}", crmId);
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

        try {
            MigrationDetails uboResData = uboServiceImpl.uboServiceCall(CustomerServiceConstant.RM_ID, crmId);
            oneServiceResponse.setData(uboResData);
            oneServiceResponse
                    .setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                            ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
            return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
        } catch (Exception ex) {
            logger.error("Unable to fetch UBO data : {}", ex);
            oneServiceResponse.setStatus(new TmbStatus(ResponseCode.GENERAL_ERROR.getCode(),
                    ResponseCode.GENERAL_ERROR.getMessage(), ResponseCode.GENERAL_ERROR.getService()));

            return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
        }

    }

}
