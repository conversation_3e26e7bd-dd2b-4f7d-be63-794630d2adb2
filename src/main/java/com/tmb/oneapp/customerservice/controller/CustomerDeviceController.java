package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.CustomerDeviceInformation;
import com.tmb.oneapp.customerservice.model.CustomerPublicKeyData;
import com.tmb.oneapp.customerservice.repository.CustomerDeviceInformationRepository;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Pattern;
import java.util.Optional;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADERS_X_CRMID;

/**
 * Controller class responsible for fetching device public key
 */
@RestController
public class CustomerDeviceController {

    private static final TMBLogger<CustomerDeviceController> logger = new TMBLogger<>(CustomerDeviceController.class);
    private final CustomerDeviceInformationRepository customerDeviceInformationRepository;

    /**
     * Constructor
     *
     * @param customerDeviceInformationRepository
     */
    public CustomerDeviceController(CustomerDeviceInformationRepository customerDeviceInformationRepository) {
        this.customerDeviceInformationRepository = customerDeviceInformationRepository;
    }

    /**
     * Method to fetch public key based on device ID
     *
     * @param deviceId
     * @return
     */
    @GetMapping(value = "/customer/{deviceId}/publickey")
    public ResponseEntity<TmbOneServiceResponse<CustomerPublicKeyData>> getCustomerPublicKey(@Parameter(description = "X-Correlation-ID", example = "c28f91e4-881e-4387-a597-4a39c2822b3c", required = true)
                                                                                             @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) final String correlationId,
                                                                                             @Parameter(description = "deviceId", example = "test-device-id", required = true)
                                                                                             @PathVariable(CustomerServiceConstant.DEVICE_ID) String deviceId) {
        logger.info("Get public key by device id {} ", deviceId);
        Optional<CustomerDeviceInformation> customerDeviceInformation = customerDeviceInformationRepository.findById(deviceId);
        TmbOneServiceResponse<CustomerPublicKeyData> response = new TmbOneServiceResponse<>();
        if (customerDeviceInformation.isPresent()) {
            CustomerPublicKeyData customerPublicKeyData = new CustomerPublicKeyData();
            customerPublicKeyData.setPublicKey(customerDeviceInformation.get().getInstallationPublicKey());
            response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE, CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMER_EXP_SERVICE));
            response.setData(customerPublicKeyData);
            logger.info("Get public key by device id {} success", deviceId);
            return ResponseEntity.ok().body(response);
        } else {
            response.setStatus(new TmbStatus(CustomerServiceConstant.FAILED_CODE, CustomerServiceConstant.FAILED_MESSAGE, CustomerServiceConstant.CUSTOMER_EXP_SERVICE));
            logger.info("Get public key by device id {} not found", deviceId);
            return ResponseEntity.badRequest().body(response);
        }
    }


    @GetMapping("/customer/public-key/{crmId}")
    public ResponseEntity<TmbOneServiceResponse<String>> getCustomerDeviceDetails(@Parameter(description = "X-Correlation-ID", example = "c28f91e4-881e-4387-a597-4a39c2822b3c", required = true)
                                                                                             @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) final String correlationId,
                                                                                             @Parameter(name = HEADERS_X_CRMID, example = "001100000000000000000000000100", required = true)
                                                                                             @Valid @PathVariable("crmId") @Pattern(message = "CRM ID must be a number", regexp = "^[0-9]{30}$") String crmId) {
        logger.info("Get public key Info by crm {} ", crmId);
        Optional<CustomerDeviceInformation> customerDeviceInformation = customerDeviceInformationRepository.findByCrmId(crmId);
        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        if (customerDeviceInformation.isPresent()) {
            response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE, CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMER_EXP_SERVICE));
            response.setData(customerDeviceInformation.get().getInstallationPublicKey());
            logger.info("Get device key by crm id {} success", crmId);
            return ResponseEntity.ok().body(response);
        } else {
            response.setStatus(new TmbStatus(CustomerServiceConstant.FAILED_CODE, CustomerServiceConstant.FAILED_MESSAGE, CustomerServiceConstant.CUSTOMER_EXP_SERVICE));
            logger.info("Get public key by crm id {} not found", crmId);
            return ResponseEntity.badRequest().body(response);
        }
    }
}
