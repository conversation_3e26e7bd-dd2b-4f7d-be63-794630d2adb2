package com.tmb.oneapp.customerservice.controller;

import java.time.Instant;

import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.data.DebitCardAutoSlip;
import com.tmb.oneapp.customerservice.service.DebitCardDetailsService;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

@RestController
@Tag(name = "DebitCard Details")
public class DebitCardDetailsController {
	
	private static final TMBLogger<DebitCardDetailsController> logger = new TMBLogger<>(DebitCardDetailsController.class);
	private final DebitCardDetailsService debitCardDetailsService;
	
	@Autowired
	public DebitCardDetailsController(DebitCardDetailsService debitCardDetailsService) {
		this.debitCardDetailsService = debitCardDetailsService;
	}

	/**
	 *  This method is using to get Status of Autosave slip
	 * @param crmId
	 * @return
	 */
	@Operation(summary = " DebitCard getAutoSaveSlip Controller ")
	@LogAround
	@GetMapping(value = "/settings/get-autosave-slip", consumes=MediaType.APPLICATION_JSON_VALUE, produces=MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<TmbOneServiceResponse<DebitCardAutoSlip>> getAutoSaveSlip(
			@Parameter(description = "X-CRMID", example = "001100000000000000000000200012", required = true) @Valid @RequestHeader("X-CRMID") @RequestHeaderNonNull String crmId) {
		logger.info(" Debit Card getAutoSaveSlip method start Time : {} ", System.currentTimeMillis());
		TmbOneServiceResponse<DebitCardAutoSlip> getautoRegresponse = new TmbOneServiceResponse<>();
		HttpHeaders getautosavHeaders = new HttpHeaders();
		getautosavHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			long setstartaecTimeMob = System.nanoTime();
			if (crmId == null) {

				getautoRegresponse.setStatus(new TmbStatus(ResponseCode.BAD_REQUEST.getCode(),
						ResponseCode.BAD_REQUEST.getMessage(), ResponseCode.BAD_REQUEST.getService()));
				return ResponseEntity.badRequest().headers(getautosavHeaders).body(getautoRegresponse);
			}

			DebitCardAutoSlip message = debitCardDetailsService.getAutoSaveSlip(crmId);
			if (message != null) {
				getautoRegresponse.setData(message);
				long setendaeTimeMob = System.nanoTime();
				long settimeaecapsedMoendb = setendaeTimeMob - setstartaecTimeMob;
				logger.info(" ==>  Debit Card getAutoSaveSlip Contoller End  : {}", settimeaecapsedMoendb);

				getautoRegresponse
						.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
								ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));

				logger.info(" DebitCard getAutoSaveSlip method end Time : {} ", System.currentTimeMillis());
				return ResponseEntity.ok().headers(getautosavHeaders).body(getautoRegresponse);

			} else {
				getautoRegresponse.setStatus(new TmbStatus(ResponseCode.BAD_REQUEST.getCode(),
						ResponseCode.BAD_REQUEST.getMessage(), ResponseCode.BAD_REQUEST.getService()));
				return ResponseEntity.badRequest().headers(getautosavHeaders).body(getautoRegresponse);
			}

		} catch (Exception ef) {
			logger.error("Unable to fetchdata from getAutoSaveSlip  : {}", ef);
			getautoRegresponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService()));

			return ResponseEntity.badRequest().headers(getautosavHeaders).body(getautoRegresponse);

		}

	}
	
	
	/**
	 * This method is using to set Status of Autosave slip
	 * 
	 * @param crmId
	 * @param debitCardAutoSlip
	 * @return
	 */
	@Operation(summary = " DebitCard setAutoSaveSlip Controller ")
	@LogAround
	@PostMapping(value = "/settings/set-autosave-slip", consumes=MediaType.APPLICATION_JSON_VALUE, produces=MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<TmbOneServiceResponse<String>> setAutoSaveSlip(
			@Parameter(description = "X-CRMID", example = "001100000000000000000000200015", required = true) @Valid @RequestHeader("X-CRMID") @RequestHeaderNonNull String crmId,
			@RequestBody DebitCardAutoSlip debitCardAutoSlip) {
		logger.info(" Debit Card setAutoSaveSlip method start Time : {} ", System.currentTimeMillis());
		TmbOneServiceResponse<String> setautosavRegresponse = new TmbOneServiceResponse<>();
		HttpHeaders setautosavHeaders = new HttpHeaders();
		setautosavHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			long setstartaecTimeMob = System.nanoTime();
			if (crmId == null) {

				setautosavRegresponse.setStatus(new TmbStatus(ResponseCode.BAD_REQUEST.getCode(),
						ResponseCode.BAD_REQUEST.getMessage(), ResponseCode.BAD_REQUEST.getService()));
				return ResponseEntity.badRequest().headers(setautosavHeaders).body(setautosavRegresponse);
			}

			String message = debitCardDetailsService.setAutoSaveSlip( crmId, debitCardAutoSlip);
			if (message != null) {
				setautosavRegresponse.setData(message);
				long setendaeTimeMob = System.nanoTime();
				long settimeaecapsedMoendb = setendaeTimeMob - setstartaecTimeMob;
				logger.info(" ==>  Debit Card setAutoSaveSlip Contoller End  : {}", settimeaecapsedMoendb);

				setautosavRegresponse
						.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
								ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));

				logger.info(" DebitCard setAutoSaveSlip method end Time : {} ", System.currentTimeMillis());
				return ResponseEntity.ok().headers(setautosavHeaders).body(setautosavRegresponse);

			} else {
				setautosavRegresponse.setStatus(new TmbStatus(ResponseCode.BAD_REQUEST.getCode(),
						ResponseCode.BAD_REQUEST.getMessage(), ResponseCode.BAD_REQUEST.getService()));
				return ResponseEntity.badRequest().headers(setautosavHeaders).body(setautosavRegresponse);
			}

		} catch (Exception ef) {
			logger.error("Unable to fetchdata from setAutoSaveSlip  : {}", ef);
			setautosavRegresponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService()));

			return ResponseEntity.badRequest().headers(setautosavHeaders).body(setautosavRegresponse);

		}

	}

}
