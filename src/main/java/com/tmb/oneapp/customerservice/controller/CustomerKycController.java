package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.model.CustomerBlockFlagAndForce;
import com.tmb.oneapp.customerservice.service.CustomerKycService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Pattern;
import java.util.Optional;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.CORRELATION_ID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADERS_X_CRMID;

@RestController
public class CustomerKycController {
    private final CustomerKycService customerKycService;

    public CustomerKycController(CustomerKycService customerKycService) {
        this.customerKycService = customerKycService;
    }

    /**
     * API to get customer block flag from CBS
     * @param crmId
     * @return
     */
    @Parameters({
            @Parameter(name = CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true, in = ParameterIn.HEADER),
            @Parameter(name = HEADERS_X_CRMID, example = "001100000000000000000000000100", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER)
    })
    @GetMapping("/customers/kyc/profile/block-flag")
    @Operation(summary = "API to get customer block flag")
    @LogAround
    public ResponseEntity<TmbServiceResponse<String>> getCustomerBlockFlag(@Valid @RequestHeader(HEADERS_X_CRMID) @Pattern(message = "CRM ID must be a number", regexp = "^\\d{30}$")
                                                                                  String crmId) {
        TmbServiceResponse<String> response = new TmbServiceResponse<>();
        Optional<String> optional = customerKycService.getCustomerBlockFlag(crmId);
        if (optional.isPresent()) {
            response.setStatus(CustomerServiceUtils.returnSuccess());
            response.setData(optional.get());
        } else {
            response.setStatus(CustomerServiceUtils.getResponseFail(null));
        }
        return ResponseEntity.ok().headers(TMBUtils.getResponseHeaders()).body(response);
    }

    @Parameters({
            @Parameter(name = CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true, in = ParameterIn.HEADER),
            @Parameter(name = HEADERS_X_CRMID, example = "001100000000000000000000000100", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER)
    })
    @GetMapping(value = {"/customers/v2/kyc/profile/block-flag", "/customers/v2/kyc/profile/block-flag/cache"})
    @Operation(summary = "API to get customer block flag and logout force")
    @LogAround
    public ResponseEntity<TmbServiceResponse<CustomerBlockFlagAndForce>> getCustomerBlockFlagAndForce(HttpServletRequest request, @Valid @RequestHeader(HEADERS_X_CRMID) @Pattern(message = "CRM ID must be a number", regexp = "^\\d{30}$")
                                                                           String crmId) {
        TmbServiceResponse<CustomerBlockFlagAndForce> response = new TmbServiceResponse<>();

        try {
            Boolean isCache = request.getRequestURI().contains("/cache") ? true : false;
             CustomerBlockFlagAndForce customerBlockFlagAndForce = customerKycService.getCustomerBlockFlagAndForce(crmId, isCache);
            if (customerBlockFlagAndForce != null) {
                response.setData(customerBlockFlagAndForce);
                response.setStatus(CustomerServiceUtils.returnSuccess());
            } else {
                response.setStatus(CustomerServiceUtils.getResponseFail(null));
            }

            return ResponseEntity.ok().headers(TMBUtils.getResponseHeaders()).body(response);

        } catch (Exception e) {
           response.setStatus(CustomerServiceUtils.getResponseFail(null));

            return ResponseEntity.badRequest().headers(TMBUtils.getResponseHeaders()).body(response);
        }
    }
}
