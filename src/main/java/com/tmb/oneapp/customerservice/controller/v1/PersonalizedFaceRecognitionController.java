package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Description;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.GetPersonalizedFaceRecognitionResponse;
import com.tmb.oneapp.customerservice.model.PersonalizeFaceRecognitionRequest;
import com.tmb.oneapp.customerservice.service.PersonalizedFaceRecognitionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

@RestController
public class PersonalizedFaceRecognitionController {
    private static final TMBLogger<PersonalizedFaceRecognitionController> logger = new TMBLogger<>(PersonalizedFaceRecognitionController.class);
    @Autowired
    private PersonalizedFaceRecognitionService personalizedFaceRecognitionService;

    @LogAround
    @Operation(summary = "Get Customer Face Authentication Status")
    @GetMapping("/personalized-face-recognition")
    public ResponseEntity<TmbServiceResponse<GetPersonalizedFaceRecognitionResponse>> getPersonalizedFaceRecognition(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = CustomerServiceConstant.X_CRMID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.X_CRMID) @RequestHeaderNonNull String crmId,
            @Parameter(description = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, example ="th", required = true) @RequestHeader(CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE) @RequestHeaderNonNull String acceptLanguage,
            @Parameter(description = CustomerServiceConstant.HEADER_APP_VERSION, example ="4.0.0", required = true) @RequestHeader(CustomerServiceConstant.HEADER_APP_VERSION) @RequestHeaderNonNull String appVersion,
            @RequestParam @NotNull Integer featureId
    ) throws TMBCommonException {
        TmbServiceResponse<GetPersonalizedFaceRecognitionResponse> getPersonalizedFaceRecognitionResponse = new TmbServiceResponse<>();
        getPersonalizedFaceRecognitionResponse.setStatus(getStatusSuccess());
        getPersonalizedFaceRecognitionResponse.setData(personalizedFaceRecognitionService.getPersonalizedFaceRecognition(crmId, featureId));
        return ResponseEntity.ok(getPersonalizedFaceRecognitionResponse);
    }

    @LogAround
    @Operation(summary = "Save Customer Face Authentication Data")
    @PostMapping("/personalized-face-recognition")
    public ResponseEntity<TmbServiceResponse<Void>> postPersonalizedFaceRecognition(
            @Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(description = CustomerServiceConstant.X_CRMID, example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.X_CRMID) @RequestHeaderNonNull String crmId,
            @Parameter(description = CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE, example ="th", required = true) @RequestHeader(CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE) @RequestHeaderNonNull String acceptLanguage,
            @Parameter(description = CustomerServiceConstant.HEADER_APP_VERSION, example ="4.0.0", required = true) @RequestHeader(CustomerServiceConstant.HEADER_APP_VERSION) @RequestHeaderNonNull String appVersion,
            @RequestBody @Valid PersonalizeFaceRecognitionRequest request
    ) throws TMBCommonException {
        TmbServiceResponse<Void> postPersonalizedFaceRecognitionResponse = new TmbServiceResponse<>();
        personalizedFaceRecognitionService.postPersonalizedFaceRecognition(crmId, request);
        postPersonalizedFaceRecognitionResponse.setStatus(getStatusSuccess());
        return ResponseEntity.ok(postPersonalizedFaceRecognitionResponse);
    }

    private Status getStatusSuccess() {
        Description description = new Description(ResponseCode.SUCCESS.getDesc(), ResponseCode.SUCCESS.getDesc());
        return new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), description);
    }
}
