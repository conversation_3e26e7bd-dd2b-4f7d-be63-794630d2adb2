package com.tmb.oneapp.customerservice.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.logger.TTBPayloadType;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.StatementNDIDASRequest;
import com.tmb.oneapp.customerservice.service.DStatementService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.*;

/**
 * Controller for EKYC DStatement operations
 */
@RestController
@Tag(name = "EKYC DStatement Api")
public class DStatementController {
	private static final TMBLogger<DStatementController> logger = new TMBLogger<>(DStatementController.class);
	private final DStatementService dstatementService;

	@Autowired
	public DStatementController(DStatementService dstatementService) {
		this.dstatementService = dstatementService;
	}

	/**
	 * Send statement request data to RP
	 *
	 * @param correlationId          correlationId
	 * @param statementNDIDASRequest requestBody
	 * @return return response
	 */
	@Operation(summary = "Send statement request data to RP")
	@LogAround
	@PostMapping(value = "/customers/statement-ndid-as")
	public ResponseEntity<TmbOneServiceResponse<Void>> postStatementNDIDAs(
			@Parameter(description = CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
			@RequestHeader(CORRELATION_ID) String correlationId,
			@Parameter(name = HEADERS_X_CRMID, example = "001100000000000000000000000100", required = true)
			@RequestHeader(HEADERS_X_CRMID) String crmId,
			@RequestBody StatementNDIDASRequest statementNDIDASRequest) throws JsonProcessingException {

		TmbOneServiceResponse<Void> response = new TmbOneServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CORRELATION_ID, correlationId);
		responseHeaders.set(HEADERS_X_CRMID, crmId);
		logger.payload(TTBPayloadType.INBOUND, responseHeaders, TMBUtils.convertJavaObjectToString(statementNDIDASRequest));

		try {
			dstatementService.postStatementNDIDAs(correlationId, crmId, statementNDIDASRequest);

			response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
					ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));

			logger.payload(TTBPayloadType.OUTBOUND, responseHeaders, TMBUtils.convertJavaObjectToString(response));
			return ResponseEntity.status(HttpStatus.OK).headers(TMBUtils.getResponseHeaders()).body(response);

		} catch (Exception e) {
			logger.error("Error calling GET /customers/statement-ndid-as : {}", e);
			response.setStatus(new TmbStatus(ResponseCode.GENERAL_ERROR.getCode(),
					ResponseCode.GENERAL_ERROR.getMessage(), ResponseCode.GENERAL_ERROR.getService()));
			return ResponseEntity.badRequest().headers(TMBUtils.getResponseHeaders()).body(response);
		}

	}

	@Operation(summary = "Update Customer IAL Value")
	@LogAround
	@PostMapping(value = "/customers/upliftCustomer")
	public ResponseEntity<TmbServiceResponse<Void>> updateCustomerIALValue(
			@Parameter(description = CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
			@RequestHeader(CORRELATION_ID) String correlationId,
			@Parameter(name = HEADERS_X_CRMID, example = "001100000000000000000000000100", required = true)
			@RequestHeader(HEADERS_X_CRMID) String crmId,
			@Parameter(name = HEADERS_X_EKYC_FLAG, example = "N", required = true)
			@RequestHeader(value = HEADERS_X_EKYC_FLAG ,required = false) String ekycFlag)
			throws TMBCommonException
	{
		TmbServiceResponse<Void> response = new TmbServiceResponse<>();
		try {
			dstatementService.updateCustomerIALValue(crmId, ekycFlag);
			response.setStatus(CustomerServiceUtils.returnSuccessStatus());
			return ResponseEntity.status(HttpStatus.OK).headers(TMBUtils.getResponseHeaders()).body(response);
		} catch (Exception e) {
			logger.error("Error calling GET /customers/statement-ndid-as : {}", e);
			throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
		}

	}

	@Operation(summary = "Update customer IAL value without updating eKYC flag")
	@LogAround
	@PostMapping(value = "/customers/uplift-customer-ial")
	public ResponseEntity<TmbServiceResponse<Void>> updateCustomerIALValueWithoutEKYC(
			@Parameter(description = CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
			@RequestHeader(CORRELATION_ID) String correlationId,
			@Parameter(name = HEADERS_X_CRMID, example = "001100000000000000000000000100", required = true)
			@RequestHeader(HEADERS_X_CRMID) String crmId)
			throws TMBCommonException {
		TmbServiceResponse<Void> response = new TmbServiceResponse<>();
		try {
			dstatementService.updateCustomerIALValueWithoutEKYC(crmId);
			response.setStatus(CustomerServiceUtils.returnSuccessStatus());
			return ResponseEntity.status(HttpStatus.OK).headers(TMBUtils.getResponseHeaders()).body(response);
		} catch (Exception e) {
			logger.error("Error calling POST /customers/uplift-customer-ial : {}", e);
			throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
		}
	}

}
