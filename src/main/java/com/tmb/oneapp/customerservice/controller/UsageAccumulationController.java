package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.AccumulateUsageRequest;
import com.tmb.oneapp.customerservice.service.UsageAccumulationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class UsageAccumulationController {
    private static final TMBLogger<UsageAccumulationController> logger = new TMBLogger<>(UsageAccumulationController.class);

    private final UsageAccumulationService usageAccumulationService;

    @Operation(summary = "Patch Accumulate data by crmID")
    @PatchMapping("/customers/accumulate")
    public ResponseEntity<TmbServiceResponse<String>> patchCrmAppLevelAttr(
            @Parameter(description = "X-CRMID", example = "001100000000000000000001185383", required = true) @Valid @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar37-b4f628fbc8da", required = true) @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @RequestBody @Valid @NotNull AccumulateUsageRequest accumulateUsageRequest
    ) throws TMBCommonException {
        logger.info("=== START Patching accumulate ===");
        logger.debug("Accumulate Usage Request : [{}]", accumulateUsageRequest);

        usageAccumulationService.updateAccumulatedUsage(accumulateUsageRequest, crmId, correlationId);

        TmbServiceResponse<String> response = new TmbServiceResponse<>();
        response.setStatus(new Status(
                ResponseCode.SUCCESS.getCode(),
                ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService(),
                null
        ));
        response.setData(ResponseCode.SUCCESS.getMessage());

        logger.info("=== END Patching accumulate ===");
        return ResponseEntity.ok(response);
    }
}
