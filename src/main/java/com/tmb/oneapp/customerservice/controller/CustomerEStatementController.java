package com.tmb.oneapp.customerservice.controller;

import java.util.Map;

import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.base.Strings;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.StatementFlag;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.model.creditcard.UpdateEStatmentResp;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.service.CustomerEStatementServiceImpl;

import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

@RestController
/**
 * CustomerEStatementController request mapping will handle apis call and then
 * navigate to respective method to get customer e-statement
 *
 */
public class CustomerEStatementController {

	private static final TMBLogger<CustomerEStatementController> logger = new TMBLogger<>(
			CustomerEStatementController.class);
	private final CustomerEStatementServiceImpl customerEStatementServiceImpl;

	/**
	 * Constructor
	 *
	 * @param customerEStatementServiceImpl
	 */
	@Autowired
	public CustomerEStatementController(CustomerEStatementServiceImpl customerEStatementServiceImpl) {
		super();
		this.customerEStatementServiceImpl = customerEStatementServiceImpl;
	}

	/**
	 * @return return customer e-statement
	 */
	@Operation(summary = "Get E-Statement information")
	@GetMapping(value = { "/customers/profile/get-e-statement" }, produces = MediaType.APPLICATION_JSON_VALUE)
	@Parameters({
			@Parameter(name = CustomerServiceConstant.CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true, in = ParameterIn.HEADER),
			@Parameter(name = CustomerServiceConstant.HEADERS_X_CRMID, example = "001100000000000000000011004078", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER) })
	public TmbOneServiceResponse<UpdateEStatmentResp> getCustomerEStatement(
			@RequestHeader(CustomerServiceConstant.CORRELATION_ID) String correlationId,
			@RequestHeader(CustomerServiceConstant.HEADERS_X_CRMID) String crmId) {
		TmbOneServiceResponse<UpdateEStatmentResp> oneServiceResponse = new TmbOneServiceResponse<>();
		
		try {
			if (!Strings.isNullOrEmpty(crmId) && !Strings.isNullOrEmpty(correlationId)) {
				UpdateEStatmentResp data = customerEStatementServiceImpl.getCustomerEStatement(crmId);
				logger.info("ApplyEStatementResponse while getting e-statement: {}", data.toString());
				oneServiceResponse.setData(data);
				oneServiceResponse
						.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
								ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
			}

		} catch (Exception e) {
			logger.error("Unable to fetch data from core banking service : {}", e);
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService()));
		}
		return oneServiceResponse;
	}

	@Operation(summary = "Update E-Statement information")
	@PostMapping(value = "/customers/profile/update-e-statement", produces = MediaType.APPLICATION_JSON_VALUE)
	@Parameters({
			@Parameter(name = CustomerServiceConstant.CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true, in = ParameterIn.HEADER),
			@Parameter(name = CustomerServiceConstant.HEADERS_X_CRMID, example = "001100000000000000000011004078", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER) })
	public TmbOneServiceResponse<UpdateEStatmentResp> updateEmailEstatment(
			@Parameter(hidden = true) @RequestHeader Map<String, String> requestHeaders,@RequestBody StatementFlag statementFlag) {
		String crmId = requestHeaders.get(CustomerServiceConstant.HEADERS_X_CRMID);
		TmbOneServiceResponse<UpdateEStatmentResp> oneServiceResponse = new TmbOneServiceResponse<>();
		try {
			UpdateEStatmentResp response = customerEStatementServiceImpl.updateEStatment(crmId,statementFlag);
			logger.info("ApplyEStatementResponse while update e-statement: {}", response.toString());
			oneServiceResponse.setData(response);
			oneServiceResponse
					.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
							ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
		} catch (Exception e) {
			logger.error(e.toString(), e);
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService()));
		}
		return oneServiceResponse;
	}

}
