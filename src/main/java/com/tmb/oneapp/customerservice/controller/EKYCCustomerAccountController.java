package com.tmb.oneapp.customerservice.controller;

import java.time.Instant;

import jakarta.validation.Valid;

import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.CreateAccountRequest;
import com.tmb.oneapp.customerservice.model.CreateCustomerDBRequest;
import com.tmb.oneapp.customerservice.model.CustomerAccountNoResponse;
import com.tmb.oneapp.customerservice.model.CustomerRMIdCreateRequest;
import com.tmb.oneapp.customerservice.model.CustomerRMIdCreateResponse;
import com.tmb.oneapp.customerservice.service.CreateCustomerAccountNoService;
import com.tmb.oneapp.customerservice.service.CreateCustomerRMIDService;
import com.tmb.oneapp.customerservice.service.CreateCustomerServiceDB;
import com.tmb.oneapp.customerservice.service.CreateECASPinService;
import com.tmb.oneapp.customerservice.service.CustomerCreateAccountService;
import com.tmb.oneapp.customerservice.service.UpdateConsentEKYCService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

/**
 * Controller Class responsible for EKYC Create Customer
 *
 */
@RestController
public class EKYCCustomerAccountController {
	private static final TMBLogger<EKYCCustomerAccountController> logger = new TMBLogger<>(
			EKYCCustomerAccountController.class);
	private final CreateCustomerAccountNoService createCustomerAccountNoService;
	private final CreateCustomerRMIDService createCustomerRMIdService;
	private final CustomerCreateAccountService customerCreateAccountService;
	private final CreateECASPinService createECASPinService;
	private final UpdateConsentEKYCService updateConsentEKYCService;
	private final CreateCustomerServiceDB createCustomerServiceDB;

	/**
	 * Constructor
	 */
	@Autowired
	public EKYCCustomerAccountController(CreateCustomerAccountNoService createCustomerAccountNoService,
			CreateCustomerRMIDService createCustomerRMIdService,
			CustomerCreateAccountService customerCreateAccountService, CreateECASPinService createECASPinService,
			UpdateConsentEKYCService updateConsentEKYCService, CreateCustomerServiceDB createCustomerServiceDB) {
		this.createCustomerAccountNoService = createCustomerAccountNoService;
		this.createCustomerRMIdService = createCustomerRMIdService;
		this.customerCreateAccountService = customerCreateAccountService;
		this.createECASPinService = createECASPinService;
		this.updateConsentEKYCService = updateConsentEKYCService;
		this.createCustomerServiceDB = createCustomerServiceDB;
	}

	/**
	 * Method responsible for generating CRM ID
	 */
	@Operation(summary = "Generate Customer CRM ID")
	@LogAround
	@PostMapping(value = "/customers/ekyc/create/crmid")
	public ResponseEntity<TmbServiceResponse<CustomerRMIdCreateResponse>> generateCustomerCRMId(
			@Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
			@Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
			@RequestBody CustomerRMIdCreateRequest request,
			@RequestHeader(name = "X-EKYC-FLAG", defaultValue = "Y") String ekycFlag
	) throws TMBCommonException, TMBCommonExceptionWithResponse {
		logger.info("EKYCCustomerAccountController  generateCustomerCRMId method start Time : {} ", System.currentTimeMillis());
		TmbServiceResponse<CustomerRMIdCreateResponse> oneServiceResponse = new TmbServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			oneServiceResponse.setData(createCustomerRMIdService.createCustomerRMIdETE(request, correlationId, ekycFlag));
			oneServiceResponse.setStatus(CustomerServiceUtils.returnSuccessStatus());
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (TMBCommonExceptionWithResponse e) {
			logger.error("generateCustomerCRMId : TMBCommonExceptionWithResponse : {}", e);
			throw e;
		} catch (TMBCommonException e) {
			logger.error("generateCustomerCRMId : TMBCommonException : {}", e);
			throw e;
		} catch (Exception ex) {
			logger.error("generateCustomerCRMId : Exception : {}", ex);
			throw new TMBCommonException(ResponseCode.FAILED.getCode(),
					"Can not create CRM: " + ex.getMessage(),
					ResponseCode.FAILED.getService(),
					HttpStatus.BAD_REQUEST,
					null);
		}
	}

	/**
	 * Method responsible for generating account Number
	 */
	@Operation(summary = "Generate Account Number")
	@LogAround
	@PostMapping(value = "/customers/ekyc/generateAccountNumber")
	public ResponseEntity<TmbServiceResponse<CustomerAccountNoResponse>> getAccountNumber(
			@Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId)
			throws TMBCommonException {
		logger.info("EKYCCustomerAccountController  getAccountNumber method start Time : {} ",
				System.currentTimeMillis());
		TmbServiceResponse<CustomerAccountNoResponse> oneServiceResponse = new TmbServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			oneServiceResponse.setData(createCustomerAccountNoService.callCustomerAccountGenerateETEAPI(correlationId));
			oneServiceResponse.setStatus(CustomerServiceUtils.returnSuccessStatus());
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (TMBCommonException tmbcommonException) {
			logger.error("getAccountNumber : TMBCommonException : {}", tmbcommonException);
			throw tmbcommonException;
		} catch (Exception e) {
			logger.error("getAccountNumber : Exception : {}", e);
			throw new TMBCommonException(ResponseCode.FAILED.getCode(),
					"Can not generate account number: " + e.getMessage(),
					ResponseCode.FAILED.getService(),
					HttpStatus.BAD_REQUEST,
					null);
		}
	}

	/**
	 * Method responsible for linking customer id with Account Number
	 */
	@Operation(summary = "Link Account Number With Customer ID")
	@LogAround
	@PostMapping(value = "/customers/ekyc/customer/linkaccount")
	public ResponseEntity<TmbServiceResponse<String>> linkCustomerWithAccount(
			@RequestBody CreateAccountRequest request) throws TMBCommonException {
		logger.info("EKYCCustomerAccountController  linkCustomerWithAccount method start Time : {} ",
				System.currentTimeMillis());
		TmbServiceResponse<String> oneServiceResponse = new TmbServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			customerCreateAccountService.callCreateAccountETE(request);
			oneServiceResponse.setStatus(CustomerServiceUtils.returnSuccessStatus());
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (TMBCommonException e) {
			logger.error("linkCustomerWithAccount : TMBCommonException : {}", e);
			throw e;
		} catch (Exception err) {
			logger.error("linkCustomerWithAccount : Exception : {}", err);
			throw new TMBCommonException(ResponseCode.FAILED.getCode(),
					"Can not link account: " + err.getMessage(),
					ResponseCode.FAILED.getService(),
					HttpStatus.BAD_REQUEST,
					null);
		}
	}

	/**
	 * Method responsible for creating Pin for CRM ID
	 */
	@Operation(summary = "Generate PIN for CRM ID")
	@LogAround
	@PostMapping(value = "/customers/create/pin")
	public ResponseEntity<TmbServiceResponse<String>> createPinCRMID(
			@Parameter(description = "X-CRM-ID", example ="001100000000000000000018595311", required = true) @Valid @RequestHeader("X-CRM-ID") @RequestHeaderNonNull String crmId,
			@Parameter(description = "X-PIN", example ="12345", required = true) @Valid @RequestHeader("X-Pin") @RequestHeaderNonNull String pin)
			throws TMBCommonException {
		logger.info("EKYCCustomerAccountController  createPinCRMID method start Time : {} ",
				System.currentTimeMillis());
		TmbServiceResponse<String> oneServiceResponse = new TmbServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			createECASPinService.createECASPinETE(crmId, pin);
			oneServiceResponse.setStatus(CustomerServiceUtils.returnSuccessStatus());
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (TMBCommonException err) {
			logger.error("createPinCRMID : TMBCommonException : {}", err);
			throw err;
		} catch (Exception er) {
			logger.error("createPinCRMID : Exception : {}", er);
			throw new TMBCommonException(ResponseCode.FAILED.getCode(),
					"Can not create PIN: " + er.getMessage(),
					ResponseCode.FAILED.getService(),
					HttpStatus.BAD_REQUEST,
					null);
		}
	}

	/**
	 * Update Consent Result for Bank Request ID
	 */
	@Operation(summary = "Update Request ID Consent Result")
	@LogAround
	@PostMapping(value = "/customers/update/consent")
	public ResponseEntity<TmbServiceResponse<String>> updateConsentEKYC(
			@Parameter(description = "X-Request-ID", example ="bd12247f-e1c8-4011-9707-9e9e2cd37609", required = true) @Valid @RequestHeader("X-Request-ID") @RequestHeaderNonNull String requestId)
			throws TMBCommonException {
		logger.info("EKYCCustomerAccountController  updateConsentEKYC method start Time : {} ",
				System.currentTimeMillis());
		TmbServiceResponse<String> oneServiceResponse = new TmbServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			updateConsentEKYCService.callUpdateConstentEKYC(requestId);
			oneServiceResponse.setStatus(CustomerServiceUtils.returnSuccessStatus());
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (TMBCommonException ex) {
			logger.error("updateConsentEKYC : TMBCommonException : {}", ex);
			throw ex;
		} catch (Exception ex) {
			logger.error("updateConsentEKYC : Exception : {}", ex);
			throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
		}
	}

	/**
	 * Update Consent Result for Bank Request ID
	 */
	@Operation(summary = "Create Customer in DB")
	@LogAround
	@PostMapping(value = "/customers/insert/db")
	public ResponseEntity<TmbServiceResponse<String>> createCustomerDB(
			@Parameter(description = "X-Correlation-ID", example ="32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
			@RequestBody CreateCustomerDBRequest request) throws TMBCommonException {
		logger.info("EKYCCustomerAccountController  createCustomerDB method start Time : {} ",
				System.currentTimeMillis());
		TmbServiceResponse<String> oneServiceResponse = new TmbServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			createCustomerServiceDB.insertDataToDB(request, correlationId);
			oneServiceResponse.setStatus(CustomerServiceUtils.returnSuccessStatus());
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (TMBCommonException ex) {
			throw ex;
		} catch (Exception ex) {
			logger.error("createCustomerDB : Exception : {}", ex);
			throw new TMBCommonException(ResponseCode.FAILED.getCode(),
					"Can not insert customer data to database: " + ex.getMessage(),
					ResponseCode.FAILED.getService(),
					HttpStatus.BAD_REQUEST,
					null);
		}
	}

}
