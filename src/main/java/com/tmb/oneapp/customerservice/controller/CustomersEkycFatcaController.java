package com.tmb.oneapp.customerservice.controller;

import java.time.Instant;

import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.CustomerGetFatcaResponse;
import com.tmb.oneapp.customerservice.model.EKYCCreateFatcaCustomerResponse;
import com.tmb.oneapp.customerservice.model.EKYCCreateFatcaRequest;
import com.tmb.oneapp.customerservice.model.EKYCGetFatcaFlagRequest;
import com.tmb.oneapp.customerservice.service.CustomerEKycFatcaService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

/**
 * Controller responsible for Customer EKYC
 *
 */
@RestController
@Tag(name = "Customer EKYC Fatca Api")
public class CustomersEkycFatcaController {
	private static final TMBLogger<CustomersEkycFatcaController> logger = new TMBLogger<>(CustomersEkycFatcaController.class);
	private final CustomerEKycFatcaService eKycService;

	/**
	 * Constructor
	 */
	@Autowired
	public CustomersEkycFatcaController(CustomerEKycFatcaService eKycService) {
		this.eKycService = eKycService;
	}

	@Operation(summary = "Create Fatca answer from ete service")
	@LogAround
	@PostMapping(value = "/customers/ekyc/fatca/creation")
	public ResponseEntity<TmbServiceResponse<EKYCCreateFatcaCustomerResponse>> createFatcaAnswer(
			@Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
			@RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
			@RequestBody EKYCCreateFatcaRequest request) throws TMBCommonException {
		logger.info("Validate Fatca answer request : {} ", request);
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			TmbServiceResponse<EKYCCreateFatcaCustomerResponse> oneServiceResponse = new TmbServiceResponse<>();
			oneServiceResponse.setStatus(new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
					ResponseCode.SUCCESS.getService(), CustomerServiceUtils.returnSuccessDescription()));
			oneServiceResponse.setData(eKycService.createFatcaAnswer(request));
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (TMBCommonException e) {
			logger.error("Create Fatca answer : TMBCommonException : {}", e);
			throw e;
		} catch (Exception e) {
			logger.error("Create Fatca answer : Exception : {}", e);
			throw new TMBCommonException(ResponseCode.FAILED.getCode(),
					"Can not create FATCA answer: " + e.getMessage(),
					ResponseCode.FAILED.getService(),
					HttpStatus.BAD_REQUEST, null);
		}

	}
	
	@Operation(summary = "Get Fatca from ete service")
	@LogAround
	@GetMapping(value = "/customers/ekyc/fatca/get/{crmid}")
	public ResponseEntity<TmbServiceResponse<CustomerGetFatcaResponse>> getCustomerFatca(
			@Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
			@RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
			@Valid @PathVariable("crmid") String crmid) throws TMBCommonException {
		logger.info("Get Customer Fatca By CRM-ID : {} ", crmid);
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			TmbServiceResponse<CustomerGetFatcaResponse> oneServiceResponse = new TmbServiceResponse<>();
			oneServiceResponse.setStatus(new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
					ResponseCode.SUCCESS.getService(), CustomerServiceUtils.returnSuccessDescription()));
			oneServiceResponse.setData(eKycService.getCustomerFatca(crmid));
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (TMBCommonException e) {
			logger.error("Get Customer Fatca : TMBCommonException : {}", e);
			throw e;
		} catch (Exception e) {
			logger.error("Get Customer Fatca : Exception : {}", e);
			throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
		}

	}
	
	@Operation(summary = "Validate Fatca answer from ete service")
	@LogAround
	@PostMapping(value = "/customers/ekyc/fatca/validation")
	public ResponseEntity<TmbServiceResponse<String>> validateFatcaAnswer(
			@Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
			@RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
			@RequestBody EKYCGetFatcaFlagRequest request) throws TMBCommonException {
		logger.info("Validate Fatca answer request : {} ", request);
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {
			eKycService.validateFatcaAnswer(request);
			TmbServiceResponse<String> oneServiceResponse = new TmbServiceResponse<>();
			oneServiceResponse.setStatus(new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
					ResponseCode.SUCCESS.getService(), CustomerServiceUtils.returnSuccessDescription()));
			oneServiceResponse.setData(CustomerServiceConstant.CUSTOMER_STATUS_RESPONSE_SUSSESS);
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (TMBCommonException e) {
			logger.error("Validate Fatca answer : TMBCommonException : {}", e);
			throw e;
		} catch (Exception e) {
			logger.error("Validate Fatca answer : Exception : {}", e);
			throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
		}

	}

}
