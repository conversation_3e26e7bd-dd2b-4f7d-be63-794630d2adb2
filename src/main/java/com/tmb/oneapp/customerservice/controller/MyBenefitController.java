package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.BZBRedeemHistoryDetailResponse;
import com.tmb.oneapp.customerservice.model.CreditCardCampaignConfigRequest;
import com.tmb.oneapp.customerservice.model.RedeemHistoryRequest;
import com.tmb.oneapp.customerservice.mongodb.customer.model.CreditCardCampaignResult;
import com.tmb.oneapp.customerservice.mongodb.customer.model.CreditcardProductAllow;
import com.tmb.oneapp.customerservice.service.MyBenefitBatchService;
import com.tmb.oneapp.customerservice.service.MyBenefitService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@Tag(name = "My Benefit controller for My Benefit service")
public class MyBenefitController extends MyBenefitBaseController {

    private static final TMBLogger<MyBenefitController> logger = new TMBLogger<>(MyBenefitController.class);
    private final MyBenefitService myBenefitService;
    private final MyBenefitBatchService myBenefitBatchService;

    @LogAround
    @PostMapping("/customers/my-benefit/batch/cc-campaign/load")
    public ResponseEntity<TmbOneServiceResponse<String>> batchLoadCreditCardCampaign(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId
    ) throws TMBCommonException {
        try {
            String response = myBenefitBatchService.batchLoadCreditCardCampaign(correlationId);
            TmbOneServiceResponse<String> buildResponse = new TmbOneServiceResponse<>();

            buildResponse.setStatus(getResponseSuccess());
            buildResponse.setData(response);

            return ResponseEntity.ok().body(buildResponse);
        } catch (Exception e) {
            logger.error("Failed to load credit card campaign: {}", e);
            throw genericTmbException(ResponseCode.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @LogAround
    @PostMapping("/customers/my-benefit/batch/cc-campaign-tracking/load")
    public ResponseEntity<TmbOneServiceResponse<String>> batchLoadCreditCardCampaignResult(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId
    ) throws TMBCommonException {
        try {
            logger.info("Request cc-campaign-tracking load correlationId: {}", correlationId);
            String response = myBenefitBatchService.batchBackupExpiredAndLoadCreditCardCampaignResult();

            TmbOneServiceResponse<String> buildResponse = new TmbOneServiceResponse<>();
            buildResponse.setStatus(getResponseSuccess());
            buildResponse.setData(response);
            return ResponseEntity.ok().body(buildResponse);
        } catch (Exception e) {
            logger.error("Failed to load credit card campaign result: {}", e);
            throw genericTmbException(ResponseCode.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @LogAround
    @PostMapping("/customers/my-benefit/credit-card/campaign/config")
    public ResponseEntity<TmbOneServiceResponse<String>> getCreditCardCampaignConfiguration(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @RequestBody CreditCardCampaignConfigRequest request
    ) throws TMBCommonException {
        String campaignType = request.getCampaignType();
        String campaignId = request.getCampaignId();

        if (campaignType == null || campaignId == null) {
            throw genericTmbException(ResponseCode.INVALID_CAMPAIGN_REQUEST_BODY, HttpStatus.BAD_REQUEST);
        }

        try {
            logger.info("Request cc-campaign-tracking load correlationId: {}, campaignType: {}, campaignId: {}",
                    correlationId, campaignType, campaignId);
            String response = myBenefitService.getCreditCardCampaignConfiguration(campaignType, campaignId);

            TmbOneServiceResponse<String> buildResponse = new TmbOneServiceResponse<>();
            buildResponse.setStatus(getResponseSuccess());
            buildResponse.setData(response);

            return ResponseEntity.ok().body(buildResponse);
        } catch (Exception e) {
            logger.error("Failed to get credit card campaign: {}", e);
            throw genericTmbException(ResponseCode.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @LogAround
    @GetMapping("/customers/my-benefit/credit-card/campaign/result")
    public ResponseEntity<TmbOneServiceResponse<CreditCardCampaignResult>> getCreditCardCampaignResult(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @Parameter(description = "Campaign Code", example = "200000", required = true) @RequestParam(value = "campaignCode") String campaignCode,
            @Parameter(description = "Account Id", example = "0000000050082450342012296", required = true) @RequestParam(value = "accountId") String accountId
    ) throws TMBCommonException {
        if (campaignCode == null || accountId == null) {
            throw genericTmbException(ResponseCode.INVALID_CAMPAIGN_REQUEST_BODY, HttpStatus.BAD_REQUEST);
        }

        logger.info("Request cc-campaign-tracking load correlationId: {}, campaignCode: {}, accountId: {}",
                correlationId, campaignCode, accountId);
        CreditCardCampaignResult response = myBenefitService.getCreditCardCampaignResult(campaignCode, accountId);

        TmbOneServiceResponse<CreditCardCampaignResult> buildResponse = new TmbOneServiceResponse<>();
        buildResponse.setStatus(getResponseSuccess());
        buildResponse.setData(response);

        return ResponseEntity.ok().body(buildResponse);
    }

    @LogAround
    @PostMapping("/customers/my-benefit/redeem/cashback/history")
    public ResponseEntity<TmbOneServiceResponse<BZBRedeemHistoryDetailResponse>> createRedeemHistory(
            @Parameter(description = "CRMID", example = "001100000000000000000018592773", required = true) @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @RequestBody RedeemHistoryRequest request
    ) throws TMBCommonException {
        try {
            logger.info("Request createRedeemHistory crmId: {}, correlationId: {}", crmId, correlationId);
            BZBRedeemHistoryDetailResponse response = myBenefitService.createRedeemHistory(crmId, correlationId, request);

            TmbOneServiceResponse<BZBRedeemHistoryDetailResponse> buildResponse = new TmbOneServiceResponse<>();
            buildResponse.setStatus(getResponseSuccess());
            buildResponse.setData(response);
            return ResponseEntity.ok().body(buildResponse);
        } catch (Exception e) {
            logger.info("Failed to create redeem history at BZB: {}", e);
            throw genericTmbException(ResponseCode.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @LogAround
    @GetMapping("/customers/my-benefit/creditcard-product-allow")
    public ResponseEntity<TmbOneServiceResponse<CreditcardProductAllow>> getCreditcardProductAllow(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") String correlationId
    ) throws TMBCommonException {
        try {
            CreditcardProductAllow response = myBenefitService.getCreditcardProductAllow();
            return ResponseEntity.ok().body(buildResponseSuccess(response));
        } catch (Exception e) {
            logger.error("Failed to get creditcard product allow", e);
            throw genericTmbException(ResponseCode.FAILED, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
