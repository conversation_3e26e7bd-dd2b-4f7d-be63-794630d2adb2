package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.GetCustFirstTimeResponse;
import com.tmb.oneapp.customerservice.service.CustomerFirstTimeUsageService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADERS_DEVICE_ID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADERS_X_CRMID;

@RestController
@Tag(name = "Customer First Time Usage API")
public class CustomerFirstTimeUsageController {
    private static final TMBLogger<CustomerFirstTimeUsageController> logger = new TMBLogger<>(
            CustomerFirstTimeUsageController.class);

    private final CustomerFirstTimeUsageService customerFirstTimeUsageService;

    /**
     * Constructor
     *
     * @param customerFirstTimeUsageService service
     */
    @Autowired
    public CustomerFirstTimeUsageController(CustomerFirstTimeUsageService customerFirstTimeUsageService) {
        this.customerFirstTimeUsageService = customerFirstTimeUsageService;
    }

    /**
     * Description:- Check if it is the first time customer is using a service/feature
     *
     * @param crmId         unique identifier for customer
     * @param deviceId      unique identifier for device
     * @param serviceTypeId identifier for service/feature used by customer
     * @return return null if first time use, else return first time use information
     */
    @Operation(summary = "Check user first time usage")
    @LogAround
    @GetMapping(value = "/customers/firstTimeUsage", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<GetCustFirstTimeResponse>> getCustomersFirstTimeUsage(
            @Parameter(description = HEADERS_X_CRMID, example = "001100000000000000000000086006")
            @RequestHeader(value = HEADERS_X_CRMID) String crmId,
            @Parameter(description = HEADERS_DEVICE_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da")
            @RequestHeader(value = HEADERS_DEVICE_ID, required = false) String deviceId,
            @Parameter(description = "Service Type Id", example = "EPB")
            @RequestParam(value = "serviceTypeId", required = false) String serviceTypeId) {
        logger.info("get /customers/firstTimeUsage start time : {}", System.currentTimeMillis());
        TmbOneServiceResponse<GetCustFirstTimeResponse> customerFirstUsageResponse = new TmbOneServiceResponse<>();

        try {

            final GetCustFirstTimeResponse getCustFirstTimeResponse = customerFirstTimeUsageService
                    .getFirstTimeUsage(crmId, deviceId, serviceTypeId);

            if (getCustFirstTimeResponse == null || getCustFirstTimeResponse.getUsages().isEmpty()) {
                customerFirstUsageResponse.setStatus(new TmbStatus(ResponseCode.DATA_NOT_FOUND_ERROR.getCode(),
                        ResponseCode.DATA_NOT_FOUND_ERROR.getMessage(), ResponseCode.DATA_NOT_FOUND_ERROR.getService()));
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .headers(TMBUtils.getResponseHeaders())
                        .body(customerFirstUsageResponse);
            }

            customerFirstUsageResponse.setData(getCustFirstTimeResponse);
            customerFirstUsageResponse
                    .setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                            ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(customerFirstUsageResponse);
        } catch (Exception e) {
            logger.error("Unable to update customer first time usage data : {} ", e);
            customerFirstUsageResponse
                    .setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                            ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(customerFirstUsageResponse);
        }
    }

    /**
     * Description:- Insert customer data into DB after first time use
     *
     * @param crmId         unique identifier for customer
     * @param deviceId      unique identifier for device
     * @param serviceTypeId identifier for service/feature used by customer
     */
    @Operation(summary = "Insert user first time usage")
    @LogAround
    @PostMapping(value = "/customers/firstTimeUsage", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<Void>> postCustomersFirstTimeUsage(
            @Parameter(description = HEADERS_X_CRMID, example = "001100000000000000000000086006", required = true)
            @RequestHeader(HEADERS_X_CRMID) String crmId,
            @Parameter(description = HEADERS_DEVICE_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader(HEADERS_DEVICE_ID) String deviceId,
            @Parameter(description = "Service Type Id", example = "EPB", required = true)
            @RequestParam(value = "serviceTypeId") String serviceTypeId) {
        TmbOneServiceResponse<Void> customerFirstUsageResponse = new TmbOneServiceResponse<>();

        try {
            String errorCode = customerFirstTimeUsageService.postFirstTimeUsage(crmId, deviceId, serviceTypeId);

            if (!CustomerServiceConstant.SUCCESS_CODE.equals(errorCode)) {
                customerFirstUsageResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(),
                        ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService()));
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .headers(TMBUtils.getResponseHeaders())
                        .body(customerFirstUsageResponse);
            }

            customerFirstUsageResponse
                    .setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                            ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));

            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(customerFirstUsageResponse);

        } catch (Exception e) {
            logger.error("Unable to fetch customer first time usage data : {} ", e);
            customerFirstUsageResponse
                    .setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                            ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(customerFirstUsageResponse);
        }
    }

    /**
     * Description:- Update customer data into DB after complete NCB request process
     *
     * @param crmId         unique identifier for customer
     * @param deviceId      unique identifier for device
     * @param serviceTypeId identifier for service/feature used by customer
     */
    @Operation(summary = "Update user first time usage")
    @LogAround
    @PutMapping(value = "/customers/firstTimeUsage", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<Void>> putCustomersFirstTimeUsage(
            @Parameter(description = HEADERS_X_CRMID, example = "001100000000000000000000086006", required = true)
            @RequestHeader(HEADERS_X_CRMID) String crmId,
            @Parameter(description = HEADERS_DEVICE_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true)
            @RequestHeader(HEADERS_DEVICE_ID) String deviceId,
            @Parameter(description = "Service Type Id", example = "NCBR", required = true)
            @RequestParam(value = "serviceTypeId") String serviceTypeId) {
        TmbOneServiceResponse<Void> customerFirstUsageResponse = new TmbOneServiceResponse<>();

        try {
            String errorCode = customerFirstTimeUsageService.updateFirstTimeUsage(crmId, deviceId, serviceTypeId);

            if (!CustomerServiceConstant.SUCCESS_CODE.equals(errorCode)) {
                customerFirstUsageResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(),
                        ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService()));
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .headers(TMBUtils.getResponseHeaders())
                        .body(customerFirstUsageResponse);
            }

            customerFirstUsageResponse
                    .setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                            ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));

            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(customerFirstUsageResponse);

        } catch (Exception e) {
            logger.error("Unable to fetch customer first time usage data : {} ", e);
            customerFirstUsageResponse
                    .setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                            ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(customerFirstUsageResponse);
        }
    }
}
