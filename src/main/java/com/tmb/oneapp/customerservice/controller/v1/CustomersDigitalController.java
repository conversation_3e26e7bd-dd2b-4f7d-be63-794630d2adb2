package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.StandardResponseCode;
import com.tmb.oneapp.customerservice.model.CustomerDigitalStatusRequest;
import com.tmb.oneapp.customerservice.model.CustomerDigitalStatusResponse;
import com.tmb.oneapp.customerservice.service.CustomersDigitalService;
import jakarta.validation.Valid;
import org.hibernate.exception.JDBCConnectionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class CustomersDigitalController {

    private static final TMBLogger<CustomersDigitalController> logger = new TMBLogger<>(CustomersDigitalController.class);


    @Autowired
    private CustomersDigitalService customersDigitalService;

    @PostMapping(value = "/customers/digital/status")
    public ResponseEntity<TmbOneServiceResponse<List<CustomerDigitalStatusResponse>>> getDigitalStatus(
            @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) final String correlationId,
            @Valid @RequestBody CustomerDigitalStatusRequest request
    ) {
        TmbOneServiceResponse<List<CustomerDigitalStatusResponse>> oneServiceResponse = new TmbOneServiceResponse<>();
        try {
            List<CustomerDigitalStatusResponse> digitalStatus = customersDigitalService.getDigitalStatus(request);
            oneServiceResponse.setData(digitalStatus);
            oneServiceResponse.setStatus(new TmbStatus(StandardResponseCode.SUCCESS.getCode(), StandardResponseCode.SUCCESS.getMessage(),
                    StandardResponseCode.SUCCESS.getService(), StandardResponseCode.SUCCESS.getMessage()));
            return ResponseEntity.ok().body(oneServiceResponse);
        } catch (JDBCConnectionException e) {
            logger.error("correlation : {} , error in getDigitalStatus DB : {}", correlationId, e.getMessage());
            oneServiceResponse.setStatus(new TmbStatus(StandardResponseCode.ERROR_DATABASE_100003.getCode(), StandardResponseCode.ERROR_DATABASE_100003.getMessage(),
                    StandardResponseCode.ERROR_DATABASE_100003.getService(), StandardResponseCode.ERROR_DATABASE_100003.getMessage()));
            return ResponseEntity.ok().body(oneServiceResponse);
        } catch (Exception e) {
            logger.error("correlation : {} , error in getDigitalStatus Generic : {}", correlationId, e.getMessage());
            oneServiceResponse.setStatus(new TmbStatus(StandardResponseCode.FAILED.getCode(), StandardResponseCode.FAILED.getMessage(),
                    StandardResponseCode.FAILED.getService(), StandardResponseCode.FAILED.getMessage()));
            return ResponseEntity.ok().body(oneServiceResponse);
        }
    }
}
