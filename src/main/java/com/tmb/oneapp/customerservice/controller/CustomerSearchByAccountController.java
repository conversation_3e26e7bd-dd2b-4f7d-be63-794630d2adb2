package com.tmb.oneapp.customerservice.controller;

import com.google.common.base.Strings;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.CustomerProfileResponseData;
import com.tmb.oneapp.customerservice.model.CustomerSearchByAccountRequest;
import com.tmb.oneapp.customerservice.service.CustomerAccountServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.List;

/**
 * CustomerSearchByMblController request mapping will handle apis call and then
 * navigate to respective method to get customer details
 *
 */
@RestController
public class CustomerSearchByAccountController {

    private static final TMBLogger<CustomerSearchByAccountController> logger = new TMBLogger<>(
            CustomerSearchByAccountController.class);
    private final CustomerAccountServiceImpl customerAccountServiceImpl;

    /**
     * Constructor
     *
     * @param customerAccountServiceImpl
     */
    @Autowired
    public CustomerSearchByAccountController(CustomerAccountServiceImpl customerAccountServiceImpl) {
        super();
        this.customerAccountServiceImpl = customerAccountServiceImpl;
    }


    /**
     * @param accountRequest
     * @return
     */

    @PostMapping(value = "/customers/account/search", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<List<CustomerProfileResponseData>>> fetchCustomerSearch(@RequestBody CustomerSearchByAccountRequest accountRequest) throws TMBCommonException {
        HttpHeaders responseHeaders = new HttpHeaders();
        TmbOneServiceResponse<List<CustomerProfileResponseData>> oneServiceResponse = new TmbOneServiceResponse<>();

        String searchAccountType = accountRequest.getSearchType();
        String searchAccountValue = accountRequest.getSearchValue();
        List<CustomerProfileResponseData> customer = null;
        if (!Strings.isNullOrEmpty(searchAccountType) && !Strings.isNullOrEmpty(searchAccountValue)) {

            logger.info("CustomerSearchByAccountController Search Type : {}, Search Value : {}", searchAccountType, searchAccountValue);
            responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

            if (searchAccountValue.length() == CustomerServiceConstant.CARD_ID_LENGTH) {
                customer = customerAccountServiceImpl
                        .customersCardIdServiceCall(searchAccountValue);
                oneServiceResponse.setData(customer);
            } else if (searchAccountValue.length() >= CustomerServiceConstant.ACCOUNT_NO_MIN_LENGTH && searchAccountValue.length() < CustomerServiceConstant.CARD_ID_LENGTH) {
                customer = customerAccountServiceImpl
                        .customersAccountServiceCall(searchAccountValue);
            }

        }
        if (customer != null) {
            oneServiceResponse.setData(customer);
            oneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
            return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
        } else {
            return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);
        }


    }


}
