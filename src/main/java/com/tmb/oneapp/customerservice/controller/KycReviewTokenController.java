package com.tmb.oneapp.customerservice.controller;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.profile.KycRequest;
import com.tmb.oneapp.customerservice.model.profile.KycReviewTokenResponse;
import com.tmb.oneapp.customerservice.service.KycReviewTokenService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Pattern;
import java.util.Optional;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.CORRELATION_ID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADERS_X_CRMID;

@RestController
public class KycReviewTokenController {

    private final KycReviewTokenService kycReviewTokenService;

    public KycReviewTokenController(KycReviewTokenService kycReviewTokenService) {
        this.kycReviewTokenService = kycReviewTokenService;
    }

    /**
     * API to Get KYC web access token
     * @param crmId
     * @param request
     * @return
     */
    @Operation(summary = "API to Get KYC web access token")
    @LogAround
    @Parameters({
            @Parameter(name = CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true, in = ParameterIn.HEADER),
            @Parameter(name = HEADERS_X_CRMID, example = "001100000000000000000000000100", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER)
    })
    @PostMapping("/customers/kyc/profile/token")
    public ResponseEntity<TmbServiceResponse<KycReviewTokenResponse>> getKeyAccessToken(@Valid @RequestHeader(HEADERS_X_CRMID) @Pattern(message = "CRM ID must be a number", regexp = "^[0-9]{30}$") String crmId,
                                                                                        @Valid @RequestHeader("X-Correlation-ID") String correlationId,
                                                                                        @Valid @RequestBody KycRequest request) throws TMBCommonException{
        TmbServiceResponse<KycReviewTokenResponse> response = new TmbServiceResponse<>();
        try{
            Optional<KycReviewTokenResponse> optional = kycReviewTokenService.getKycReviewToken(correlationId,crmId, request);
            if (optional.isPresent()) {
                response.setStatus(CustomerServiceUtils.returnSuccess());
                response.setData(optional.get());
            } else {
                response.setStatus(CustomerServiceUtils.getResponseFail(null));
            }
        }catch (TMBCommonException e){
            response.setStatus(new Status("8100", "Cache Not Found",
                    CustomerServiceConstant.CUSTOMERS_SERVICE, null));
        }
        return ResponseEntity.ok().headers(TMBUtils.getResponseHeaders()).body(response);
    }
}
