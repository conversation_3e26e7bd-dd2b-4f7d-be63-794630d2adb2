package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.PostFinancialCertSubmitRequest;
import com.tmb.oneapp.customerservice.service.FinancialCertService;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.Map;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.CORRELATION_ID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADERS_X_CRMID;

@RestController
@Tag(name = "Financial Certificate Controller")
public class FinancialCertController {
    private static final TMBLogger<FinancialCertController> logger = new TMBLogger<>(
            FinancialCertController.class);

    private final FinancialCertService service;

    /**
     * Constructor
     *
     * @param service FinancialCertRequestService
     */
    @Autowired
    public FinancialCertController(FinancialCertService service) {
        this.service = service;
    }

    /**
     * Description:- Insert financial certificate request into db
     *
     * @param headers                  crmId, correlationId
     * @param postFinancialCertRequest financial cert info
     */
    @Operation(summary = "Insert financial certificate request")
    @LogAround
    @PostMapping(value = "/customers/financialcert/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    @Parameters({
            @Parameter(name = CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true, in = ParameterIn.HEADER),
            @Parameter(name = HEADERS_X_CRMID, example = "001100000000000000000000000100", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER)
    })
    public ResponseEntity<TmbOneServiceResponse<Void>> postFinancialCertSubmit(
            @Parameter(hidden = true) @RequestHeader Map<String, String> headers,
            @RequestBody @Valid PostFinancialCertSubmitRequest postFinancialCertRequest) {
        TmbOneServiceResponse<Void> response = new TmbOneServiceResponse<>();

        try {
            int responseCode = service.postFinancialCertRequest(headers, postFinancialCertRequest);

            if(responseCode >= 1) {
                response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                        ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));

                return ResponseEntity.status(HttpStatus.OK)
                        .headers(TMBUtils.getResponseHeaders())
                        .body(response);
            }

            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), "Unable to insert financial cert request",
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(response);

        } catch (Exception e) {
            logger.error("Unable to insert financial cert request: {} ", e);
            response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), "Unable to insert financial cert request",
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(response);
        }
    }
}
