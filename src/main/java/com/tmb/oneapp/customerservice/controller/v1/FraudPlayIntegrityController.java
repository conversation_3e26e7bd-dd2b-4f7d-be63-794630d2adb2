package com.tmb.oneapp.customerservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.integrity.ReduceDailyLimitConfig;
import com.tmb.oneapp.customerservice.service.FraudPlayIntegrityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;

@RestController
public class FraudPlayIntegrityController {
    private static final TMBLogger<FraudPlayIntegrityController> logger = new TMBLogger<>(FraudPlayIntegrityController.class);

    private final FraudPlayIntegrityService fraudPlayIntegrityService;

    public FraudPlayIntegrityController(FraudPlayIntegrityService fraudPlayIntegrityService) {
        this.fraudPlayIntegrityService = fraudPlayIntegrityService;
    }

    @LogAround
    @Operation(summary = "update daily limit")
    @PostMapping(value = "/reduce-daily-limit")
    public ResponseEntity<TmbOneServiceResponse<String>> updateDailyLimit(
            @Parameter(description = "Correlation ID", example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)@RequestHeader(CustomerServiceConstant.HEADERS_CORRELATION_ID) String correlationId,
            @Parameter(description = "CRM ID", example ="001100000000000000000012004030", required = true) @RequestHeader(CustomerServiceConstant.HEADERS_FRUAD_X_CRM_ID) String crmId,
            @Parameter(description = "Device Model", example ="sumsung note", required = true) @RequestHeader(CustomerServiceConstant.HEADER_DEVICE_MODEL) String deviceModel,
            @RequestBody ReduceDailyLimitConfig reduceDailyLimitConfig) throws TMBCommonException {

        TmbOneServiceResponse<String> response =new TmbOneServiceResponse<>();
        HttpHeaders responseHeader = TMBUtils.getResponseHeaders();
        responseHeader.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

        try {
            if (fraudPlayIntegrityService.updateCustomerTempLimit(crmId, correlationId, reduceDailyLimitConfig, deviceModel)) {
                logger.info("0.[success] update daily limit crm_id : {}",crmId);
                response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                        ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
            } else {
                logger.info("0.[fail] update daily limit crm_id : {}",crmId);
                response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                        ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));
            }
            return ResponseEntity.ok().headers(responseHeader).body(response);
        } catch (Exception e) {
            logger.error("0.[exception]fraud update daily limit customer has  : Exception : {}", e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.OK, null);
        }
    }
}
