package com.tmb.oneapp.customerservice.controller;

import java.text.ParseException;
import java.time.Instant;

import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.validation.Valid;

import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.oneapp.customerservice.model.ekycriskcalculate.EkycRiskCalculateResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.CalculateRiskRequest;
import com.tmb.oneapp.customerservice.service.CustomerEKYCCalRiskService;
import com.tmb.oneapp.customerservice.utils.CustomerServiceUtils;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

/**
 * Controller responsible for Customer EKYC
 *
 */
@RestController
@Tag(name = "Customer EKYC Api")
public class CustomerEKYCCalRiskController {
	private static final TMBLogger<CustomerEKYCCalRiskController> logger =
			new TMBLogger<>(CustomerEKYCCalRiskController.class);
	private final CustomerEKYCCalRiskService customerEKYCCalRiskService;

	/**
	 * Constructor
	 */
	@Autowired
	public CustomerEKYCCalRiskController(CustomerEKYCCalRiskService customerEKYCCalRiskService) {
		this.customerEKYCCalRiskService = customerEKYCCalRiskService;
	}

	/**
	 * Controller for calling Calculation Risk API
	 */
	@Operation(summary = "Risk Calculator")
	@LogAround
	@PostMapping(value = "/customers/ekyc/risk/calculate")
	public ResponseEntity<TmbServiceResponse<EkycRiskCalculateResponse>> getCustomerCalculateRisk(
			@Parameter(description = "X-Correlation-ID", example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader("X-Correlation-ID") @RequestHeaderNonNull String correlationId,
			@Valid @RequestBody CalculateRiskRequest reqBody) throws TMBCommonException, TMBCommonExceptionWithResponse {
		logger.info("CustomerEKYCCalRiskController  CalculateRiskEKYC method start Time : {} ",
				System.currentTimeMillis());
		TmbServiceResponse<EkycRiskCalculateResponse> oneServiceResponse = new TmbServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));
		try {

			oneServiceResponse.setStatus(CustomerServiceUtils.returnSuccessStatus());
			oneServiceResponse.setData(customerEKYCCalRiskService.callCalRiskService(correlationId, reqBody));
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);

		} catch (TMBCommonException|TMBCommonExceptionWithResponse e) {
			logger.error("CalculateRiskEKYC : TMBCommonException : {}", e.getMessage(), e);
			throw e;
		} catch (Exception e) {
			logger.error("CalculateRiskEKYC : Exception : {}", e.getMessage(), e);
			throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
					ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, e);
		}
	}

	@Operation(summary = "Risk Calculator V2")
	@LogAround
	@PostMapping(value = "/customers/v2/personal-risk-calculate")
	public ResponseEntity<TmbServiceResponse<EkycRiskCalculateResponse>> getRiskLevelCalculator(
			@Parameter(description = CustomerServiceConstant.HEADER_CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da", required = true) @Valid @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
			@Parameter(description = CustomerServiceConstant.HEADER_FLOW_NAME, example = "Activate") @RequestHeader(value = CustomerServiceConstant.HEADER_FLOW_NAME, required = false) String flowName,
			@Valid @RequestBody CalculateRiskRequest reqBody) throws TMBCommonException, TMBCommonExceptionWithResponse, ParseException, JsonProcessingException {
		return ResponseEntity.ok().body(customerEKYCCalRiskService.getCalculatorRisk(correlationId, flowName, reqBody));
	}

}
