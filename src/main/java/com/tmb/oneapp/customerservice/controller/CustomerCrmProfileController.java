package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.TTBPayloadType;
import com.tmb.common.model.CustomerProfileStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.model.PaymentAccumulateUsageRequest;
import com.tmb.oneapp.customerservice.model.data.DailyUsageData;
import com.tmb.oneapp.customerservice.model.data.PinFreeCountData;
import com.tmb.oneapp.customerservice.service.DeviceNameStatusServiceImpl;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.data.CustomerSettingsData;
import com.tmb.oneapp.customerservice.model.CustomerCrmProfile;
import com.tmb.oneapp.customerservice.model.CustomerProfileResponse;
import com.tmb.oneapp.customerservice.service.CustomerCrmProfileService;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.APP_VERSION;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.DEVICE_OS_TYPE;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.DEVICE_OS_VERSION;

@RestController
@Tag(name = "Customer crm profile")
public class CustomerCrmProfileController {
    private static final TMBLogger<CustomerDeviceController> logger = new TMBLogger<>(CustomerDeviceController.class);
    private final CustomerCrmProfileService customerCrmProfileService;
    private final DeviceNameStatusServiceImpl deviceNameStatusService;

    @Autowired
    public CustomerCrmProfileController(CustomerCrmProfileService customerCrmProfileService,
                                        DeviceNameStatusServiceImpl deviceNameStatusService) {
        this.customerCrmProfileService = customerCrmProfileService;
        this.deviceNameStatusService = deviceNameStatusService;
    }

    @LogAround
    @Operation(summary = "Get customer crm profile ")
    @GetMapping(value = "/customers/crmprofile", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<CustomerCrmProfile>> getCustomerCrmProfile(
            @Parameter(description = "CRMID", example = "001100000000000000000001184383", required = true)
            @RequestHeader(name = "X-CRMID", required = false) String crmId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID") String correlationId,
            @Parameter(description = "OS Type", example = "iOS")
            @RequestHeader(name = DEVICE_OS_TYPE, required = false) String deviceOSType,
            @Parameter(description = "OS Version", example = "9")
            @RequestHeader(name = DEVICE_OS_VERSION, required = false) String deviceOSVersion,
            @RequestHeader HttpHeaders headers) throws TMBCommonException {
        try {
            logger.payload(TTBPayloadType.INBOUND, headers, null);
            final String appVersion = headers.getFirst(APP_VERSION);

            CustomerCrmProfile customerCrmProfile = customerCrmProfileService
                    .getCustomerCrmProfile(correlationId, crmId, appVersion, deviceOSType, deviceOSVersion);

            TmbOneServiceResponse<CustomerCrmProfile> response = new TmbOneServiceResponse<>();
            response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE,
                    CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMER_EXP_SERVICE));
            response.setData(customerCrmProfile);

            HttpHeaders responseHeader = TMBUtils.getResponseHeaders();
            logger.payload(TTBPayloadType.OUTBOUND, responseHeader, TMBUtils.convertJavaObjectToString(response));
            return ResponseEntity.ok().headers(responseHeader).body(response);
        } catch (Exception e) {
            logger.error("Error while get customer crm profile : {}", e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);
        }
    }

    @LogAround
    @Operation(summary = "Get customer crm profile ")
    @GetMapping(value = "/customers/pre-login/crmprofile", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<CustomerCrmProfile>> getCustomerCrmProfilePreLogin(
            @Parameter(description = "Device ID",
                    example = "e05fbfa2584112a26e20eb9e3998ad61776958a015f23aeee492dcbb7609da76", required = true)
            @Valid @RequestHeader("Device-Id") String deviceId,
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @Valid @RequestHeader("X-Correlation-ID") String correlationId) throws TMBCommonException {
        try {
            CustomerProfileStatus customerProfileStatus = deviceNameStatusService.getCustomerDeviceNameStatus(deviceId);
            CustomerCrmProfile customerCrmProfile =
                    customerCrmProfileService.getCustomerCrmProfile(correlationId, customerProfileStatus.getCrmId());

            TmbOneServiceResponse<CustomerCrmProfile> response = new TmbOneServiceResponse<>();
            response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE,
                    CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMER_EXP_SERVICE));
            response.setData(customerCrmProfile);

            return ResponseEntity.ok().body(response);
        } catch (Exception e) {
            logger.error("{} Error while get customer crm profile : {}", correlationId, e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);
        }
    }
    
    
    @LogAround
    @Operation(summary = "Set profile settings for customer")
    @PostMapping(value = "/customers/crmprofile/settings",
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<CustomerProfileResponse>> setCustomerProfileSetting(
    	   @Parameter(description = "X-CRMID", example = "001100000000000000000001184383", required = true)
           @RequestHeader(name = "X-CRMID", required = false) String ignoredCrmId,
    	   @Parameter(description = "Correlation ID", example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
           @Valid @RequestHeader("X-Correlation-ID") String ignoredCorrelationId,
           @Parameter(description = "IP Address", example ="localhost", required = true)
           @Valid @RequestHeader(CustomerServiceConstant.HEADER_X_FORWARD_FOR) String ignoredIpAddress,
           @Parameter(description = "OS Type", example = "iOS")
           @RequestHeader(name = DEVICE_OS_TYPE, required = false) String ignoredDeviceOSType,
           @Parameter(description = "OS Version", example = "9")
           @RequestHeader(name = DEVICE_OS_VERSION, required = false) String ignoredDeviceOSVersion,
           @RequestHeader HttpHeaders headers,
    	   @RequestBody CustomerSettingsData customerSettingsData) throws TMBCommonException {
    		 try {
    		CustomerProfileResponse customerCrmProfile = customerCrmProfileService
                    .setCustomerProfileSetting(customerSettingsData, headers);
    	    logger.info("Customer crm profile in controller {}",customerCrmProfile);
            TmbOneServiceResponse<CustomerProfileResponse> response = new TmbOneServiceResponse<>();
            response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE,
                    CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMER_EXP_SERVICE));
            response.setData(customerCrmProfile);
            return ResponseEntity.ok().body(response);
            
        } catch (Exception e) {
            logger.error("Error while get customer crm profile : {}", e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);
        }
    }

    @LogAround
    @Operation(summary = "Update transaction daily limit")
    @PostMapping(value = "/customers/daily-usage")
    public ResponseEntity<TmbOneServiceResponse<String>> updateEBAccountUsageAmountDaily(
            @Parameter(description = "X-CRMID", example ="001100000000000000000001184383", required = true)
            @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @Valid @RequestHeader(name = "X-Correlation-ID") String ignoreCorrelationId,
            @RequestBody DailyUsageData dailyUsageData
            ) throws TMBCommonException {
        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        try {
            customerCrmProfileService.updateEBAccountUsageAmountDaily(crmId, dailyUsageData.getDailyUsage());
            response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE,
                    CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMER_EXP_SERVICE));
            response.setData("update success");
        } catch (Exception e) {
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);
        }

        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "Update pin free count")
    @PostMapping(value = "/customers/pin-free-count", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<String>> updatePinFreeCount(
            @Parameter(description = "X-CRMID", example ="001100000000000000000001184383", required = true)
            @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @Valid @RequestHeader(name = "X-Correlation-ID") String ignoreCorrelationId,
            @RequestBody PinFreeCountData pinFreeCountData
            ) throws TMBCommonException {
        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        try {
            customerCrmProfileService.updatePinFreeCount(crmId, pinFreeCountData.getPinFreeCount());
            response.setStatus(new TmbStatus(CustomerServiceConstant.SUCCESS_CODE,
                    CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMER_EXP_SERVICE));
            response.setData("update success");
        } catch (Exception e) {
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);
        }
        return ResponseEntity.ok(response);
    }

    @LogAround
    @Operation(summary = "Update Payment accumulate usage amount")
    @PostMapping(value = "/customers/update/payment-accumulate-usage", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<String>> updatePaymentAccumulateUsage(
            @Parameter(description = "X-CRMID", example ="001100000000000000000001184383", required = true)
            @RequestHeader(name = "X-CRMID") String crmId,
            @Parameter(description = "Correlation ID", example ="32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @Valid @RequestHeader(name = "X-Correlation-ID") String correlationId,
            @RequestBody PaymentAccumulateUsageRequest request
    ) throws TMBCommonException {
        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        try {
            customerCrmProfileService.updatePaymentAccumulateUsage(request, crmId, correlationId);
            response.setStatus(new TmbStatus(
                    ResponseCode.SUCCESS.getCode(),
                    ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService()
            ));
            response.setData("Update success");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Failed to process updatePaymentAccumuldateUsage", e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    e);
        }
    }

}




