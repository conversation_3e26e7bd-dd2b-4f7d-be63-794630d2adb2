package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.PersonalConsentResponse;
import com.tmb.oneapp.customerservice.model.TaxCertificateConsentsRequest;
import com.tmb.oneapp.customerservice.model.PersonalUpdateConsentRequest;
import com.tmb.oneapp.customerservice.model.PersonalUpdateConsentResponse;
import com.tmb.oneapp.customerservice.service.PersonalConsentService;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.DATA_NOT_FOUND;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_PARAM_X_COR_ID_DEFAULT;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_PARAM_CRM_ID_DEFAULT;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.X_HEADER_CRMID;
import static com.tmb.oneapp.customerservice.constant.ResponseCode.SUCCESS;

@RestController
@Tag(name = "Personal consent Controller")
public class PersonalConsentController {
    private static final TMBLogger<PersonalConsentController> logger = new TMBLogger<>(PersonalConsentController.class);

    private final PersonalConsentService personalConsentService;

    @Autowired
    public PersonalConsentController(PersonalConsentService personalConsentService) {
        this.personalConsentService = personalConsentService;
    }

    @LogAround
    @PostMapping(value = "/customers/personal/consents/get-consents")
    public ResponseEntity<TmbOneServiceResponse<PersonalConsentResponse>> getPersonalConsent(
           @Parameter(description = HEADER_CORRELATION_ID,
                    example = HEADER_PARAM_X_COR_ID_DEFAULT,
                    required = true)
            @RequestHeader(HEADER_CORRELATION_ID) String correlationId,
           @RequestBody TaxCertificateConsentsRequest requests) {
        logger.info("getPersonalConsent method start time {}", System.currentTimeMillis());
        TmbOneServiceResponse<PersonalConsentResponse> serviceResponse = new TmbOneServiceResponse<>();

        try {
            PersonalConsentResponse response = personalConsentService
                    .getConsent(correlationId, requests);
            serviceResponse.setData(response);

            serviceResponse.setStatus(new TmbStatus(SUCCESS.getCode(), SUCCESS.getMessage(),
                    SUCCESS.getService(), SUCCESS.getDesc()));

            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(serviceResponse);

        } catch (Exception e) {
            if(e.getMessage().contains("4001")) {
                serviceResponse.setStatus(new TmbStatus("4001", DATA_NOT_FOUND,
                        ResponseCode.FAILED.getService(), null));
            }
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(serviceResponse);
        }
    }

    @LogAround
    @PostMapping(value = "/customers/personal/consents/update-consents")
    public ResponseEntity<TmbOneServiceResponse<PersonalUpdateConsentResponse>> updatePersonalConsent(
            @Parameter(description = X_HEADER_CRMID, example =HEADER_PARAM_CRM_ID_DEFAULT, required = true)
            @RequestHeader(X_HEADER_CRMID) String crmId,
            @Parameter(description = HEADER_CORRELATION_ID, example =HEADER_PARAM_X_COR_ID_DEFAULT, required = true)
            @RequestHeader(HEADER_CORRELATION_ID) String correlationId,
            @RequestBody PersonalUpdateConsentRequest requests) {
        logger.info("updatePersonalConsent method start time {}", System.currentTimeMillis());
        TmbOneServiceResponse<PersonalUpdateConsentResponse> serviceResponse = new TmbOneServiceResponse<>();

        try {
            PersonalUpdateConsentResponse response = personalConsentService
                    .updateConsent(crmId,correlationId, requests);
            serviceResponse.setData(response);

            serviceResponse.setStatus(new TmbStatus(SUCCESS.getCode(), SUCCESS.getMessage(),
                    SUCCESS.getService(), SUCCESS.getDesc()));

            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(serviceResponse);
        } catch (Exception e) {
            serviceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(serviceResponse);
        }
    }
}
