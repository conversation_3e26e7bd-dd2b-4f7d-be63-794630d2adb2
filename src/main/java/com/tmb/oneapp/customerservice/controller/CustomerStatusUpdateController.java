package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.UpdateEbStatusRequest;
import com.tmb.oneapp.customerservice.model.UpdateStatusRequest;
import com.tmb.oneapp.customerservice.model.ods.ActivityEventRequest;
import com.tmb.oneapp.customerservice.service.CustomerActivationService;
import com.tmb.oneapp.customerservice.service.CustomerLogService;
import com.tmb.oneapp.customerservice.service.CustomerStatusService;
import com.tmb.oneapp.customerservice.service.EmployeeActivityService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.Arrays;

/**
 * Class responsible for updating the customer status in crm_cust_profile table
 */
@RestController
@Tag(name = "To update customer status by CRM ID")
public class CustomerStatusUpdateController {
    private final CustomerStatusService customerStatusService;
    private final EmployeeActivityService employeeActivityService;
    private final CustomerLogService customerLogService;
    private final CustomerActivationService customerActivationService;
    private static final TMBLogger<CustomerStatusUpdateController> logger = new TMBLogger<>(CustomerStatusUpdateController.class);

    /**
     * Constructor
     *
     * @param customerStatusService
     * @param customerActivationService
     */
    @Autowired
    public CustomerStatusUpdateController(CustomerStatusService customerStatusService, EmployeeActivityService employeeActivityService, CustomerLogService customerLogService, CustomerActivationService customerActivationService) {
        this.customerStatusService = customerStatusService;
        this.employeeActivityService = employeeActivityService;
        this.customerLogService = customerLogService;
        this.customerActivationService = customerActivationService;
    }

    /**
     * Method to update customer Status
     *
     * @param header
     * @param updateStatusRequest
     * @return
     */
    @Operation(summary = "Api to update customer status")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = "Transations ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da")})
    @PostMapping("/customers/update/status")
    @LogAround
    public ResponseEntity<TmbServiceResponse<String>> updateCustomerStatus(@RequestHeader HttpHeaders header,
                                                                           @Valid @RequestBody UpdateStatusRequest updateStatusRequest) {
        TmbServiceResponse<String> response = new TmbServiceResponse<>();
        String errorCode = customerStatusService.updateCustomerStatus(updateStatusRequest);
        if (CustomerServiceConstant.SUCCESS_CODE.equals(errorCode)) {
            response.setStatus(new Status(CustomerServiceConstant.SUCCESS_CODE, CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMERS_SERVICE, null));
            response.setData(CustomerServiceConstant.UPDATE_STATUS_SUCCESS_MESSAGE);
        } else {
            response.setStatus(new Status(CustomerServiceConstant.FAILED_CODE, CustomerServiceConstant.FAILED_MESSAGE, CustomerServiceConstant.CUSTOMERS_SERVICE, null));
            response.setData(CustomerServiceConstant.UPDATE_STATUS_FAILED_MESSAGE);
        }
        return ResponseEntity.ok().headers(TMBUtils.getResponseHeaders()).body(response);
    }

    /**
     * Method to update customer EB Status
     *
     * @param header
     * @param updateEbStatusRequest
     * @return
     */
    @Operation(summary = "Api to update customer eb status")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = "Transactions ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da"),
            @Parameter(name = "user-name", example = "TMBUSER090909", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER),
            @Parameter(name = "updateEbStatusRequest", required = true, schema = @Schema(type = "UpdateEbStatusRequest")),
            @Parameter(name = "type", example = "EB", required = true, schema = @Schema(type = "string"))
    })
    @PostMapping("/customers/profile/update/{type}")
    @LogAround
    public ResponseEntity<TmbServiceResponse<String>> updateCustomerProfileEbStatus(@RequestHeader HttpHeaders header,
                                                                                    @Valid @RequestBody UpdateEbStatusRequest updateEbStatusRequest,
                                                                                    @PathVariable String type) {
        TmbServiceResponse<String> response = new TmbServiceResponse<>();
        try {
            type = type.toUpperCase();
            if (!Arrays.asList(CustomerServiceConstant.CUSTOMER_STATUS_EB,
                    CustomerServiceConstant.CUSTOMER_STATUS_IB, CustomerServiceConstant.CUSTOMER_STATUS_MB)
                    .contains(type)) {
                response.setStatus(new Status(CustomerServiceConstant.FAILED_CODE, CustomerServiceConstant.FAILED_MESSAGE,
                        CustomerServiceConstant.CUSTOMERS_SERVICE, null));
                return ResponseEntity.badRequest().headers(header).body(response);
            }
            updateEbStatusRequest.setStatusType(type);
            String correlationId = header.getFirst(CustomerServiceConstant.CORRELATION_ID);
            String userName = employeeActivityService.chkAgentId(header.getFirst(CustomerServiceConstant.HEADER_USER_NAME), correlationId);
            String ipAddress = header.getFirst(CustomerServiceConstant.HEADER_X_FORWARD_FOR);
            String statusDesc = customerStatusService.getStatusDesc(updateEbStatusRequest.getStatus());
            String errorCode = customerStatusService.updateCustomerStatus(updateEbStatusRequest, userName, correlationId, statusDesc, ipAddress);
            if (CustomerServiceConstant.SUCCESS_CODE.equals(errorCode)) {
                response.setStatus(new Status(CustomerServiceConstant.SUCCESS_CODE, CustomerServiceConstant.SUCCESS_MESSAGE, CustomerServiceConstant.CUSTOMERS_SERVICE, null));
                response.setData(CustomerServiceConstant.UPDATE_STATUS_SUCCESS_MESSAGE);
                customerLogService.logCustomerActivityForUpdateStatus(correlationId, userName, statusDesc, updateEbStatusRequest, ipAddress, false);
                employeeActivityService.saveEmpActivity(correlationId, userName, statusDesc, updateEbStatusRequest, false);
                customerActivationService.updateCustomerEventsToODS(this.buildActivityEventRequest(correlationId, updateEbStatusRequest));
            } else {
                customerLogService.logCustomerActivityForUpdateStatus(correlationId, userName, statusDesc, updateEbStatusRequest, ipAddress, true);
                employeeActivityService.saveEmpActivity(correlationId, userName, statusDesc, updateEbStatusRequest, true);
                response.setStatus(new Status(CustomerServiceConstant.FAILED_CODE, CustomerServiceConstant.FAILED_MESSAGE, CustomerServiceConstant.CUSTOMERS_SERVICE, null));
                response.setData(CustomerServiceConstant.UPDATE_STATUS_FAILED_MESSAGE);
            }

            return ResponseEntity.ok().headers(TMBUtils.getResponseHeaders()).body(response);
        } catch (Exception e) {
            logger.error("Customer status updated failed : {}", e);
            response.setStatus(new Status(CustomerServiceConstant.FAILED_CODE, CustomerServiceConstant.FAILED_MESSAGE, CustomerServiceConstant.CUSTOMERS_SERVICE, null));

            return ResponseEntity.badRequest().headers(header).body(response);
        }
    }

    private ActivityEventRequest buildActivityEventRequest(String correlationId, UpdateEbStatusRequest updateRequest) {
        //add activity to OSD server
        ActivityEventRequest aceRequest = new ActivityEventRequest();
        aceRequest.setCrmId(updateRequest.getCrmId());
        aceRequest.setCorrelationId(correlationId);
        if (CustomerServiceConstant.CUSTOMER_STATUS_IB.equals(updateRequest.getStatusType())) {
            aceRequest.setIbStatus(updateRequest.getStatus());
        } else if (CustomerServiceConstant.CUSTOMER_STATUS_EB.equals(updateRequest.getStatusType())) {
            aceRequest.setEbStatus(updateRequest.getStatus());
        } else if (CustomerServiceConstant.CUSTOMER_STATUS_MB.equals(updateRequest.getStatusType())) {
            aceRequest.setMbStatus(updateRequest.getStatus());
        } else {
            return aceRequest;
        }
        return aceRequest;
    }
}
