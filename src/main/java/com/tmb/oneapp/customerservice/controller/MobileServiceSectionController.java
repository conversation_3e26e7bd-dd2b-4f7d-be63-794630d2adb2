package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.CrmIdRequest;
import com.tmb.oneapp.customerservice.model.CustomerMobileServiceResponse;
import com.tmb.oneapp.customerservice.service.MobileServiceSectionService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * Class responsible to handles to get the Mobile service  data for the customer
 */
@RestController
@Tag(name = "To get customer Mobile profile for Mobile Service by CRM ID")
public class MobileServiceSectionController {
    private final MobileServiceSectionService mobileServiceSectionService;
    private static final TMBLogger<MobileServiceSectionController> logger = new TMBLogger<>(MobileServiceSectionController.class);

    /**
     * @param mobileServiceSectionService Constructor to invoke the service
     */
    public MobileServiceSectionController(MobileServiceSectionService mobileServiceSectionService) {
        this.mobileServiceSectionService = mobileServiceSectionService;
    }


    /**
     * @param header pass header values
     * @return Customer Mobile Service  details
     */
    @Operation(summary = "Api to  Mb customer profile")
    @Parameters({
            @Parameter(name = CustomerServiceConstant.HEADER_CORRELATION_ID, description = "Correlation ID", required = true, schema = @Schema(type = "string"), in = ParameterIn.HEADER, example = "32fbd3b2-3f97-4a89-ae39-b4f628fbc8da")})
    @PostMapping(value = "/customers/profile/mb",produces = MediaType.APPLICATION_JSON_VALUE)
    @LogAround
    public ResponseEntity<TmbOneServiceResponse<CustomerMobileServiceResponse>> getCustMobileService(@RequestHeader HttpHeaders header,
                        @RequestBody @Valid CrmIdRequest crmIdRequest) throws TMBCommonException {
        String correlationId = header.get(CustomerServiceConstant.HEADER_CORRELATION_ID).get(0);
        TmbOneServiceResponse<CustomerMobileServiceResponse> oneMoblieServiceResponse = new TmbOneServiceResponse<>();
        try {
            CustomerMobileServiceResponse customerMobileServiceResponse = mobileServiceSectionService.getMobileService(crmIdRequest.getCrmId(),correlationId);

            if (customerMobileServiceResponse != null) {
                oneMoblieServiceResponse.setData(customerMobileServiceResponse);
                oneMoblieServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                        ResponseCode.SUCCESS.getService()));
            } else {
                oneMoblieServiceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                        ResponseCode.FAILED.getService()));
            }

            return ResponseEntity.ok().headers(TMBUtils.getResponseHeaders()).body(oneMoblieServiceResponse);

        }
        catch (TMBCommonException te) {
            logger.info(" Exception has been raised for Customer getMobileService for mobile customer profile details : {}", te.getMessage());
            throw new TMBCommonException(te.getMessage());
        }
        catch (Exception e) {
            logger.error("Unable to fetch data from database : {}", e);
            oneMoblieServiceResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService()));

            return ResponseEntity.badRequest().headers(header).body(oneMoblieServiceResponse);
        }
    }
}
