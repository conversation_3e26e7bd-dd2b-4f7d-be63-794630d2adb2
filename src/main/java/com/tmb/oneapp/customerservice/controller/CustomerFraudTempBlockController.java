package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.FraudTempBlockModel;
import com.tmb.oneapp.customerservice.service.FraudTempBlockService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;


@RestController
public class CustomerFraudTempBlockController {
    private static final TMBLogger<CustomerFraudTempBlockController> logger = new TMBLogger<>(CustomerFraudTempBlockController.class);
    private final FraudTempBlockService fraudTempBlockService;

    public CustomerFraudTempBlockController(FraudTempBlockService fraudTempBlockService) {
        this.fraudTempBlockService = fraudTempBlockService;
    }

    @LogAround
    @Operation(summary = "set temp block customer")
    @PostMapping(value = "/customers/fraud_temp_block")
    public ResponseEntity<TmbOneServiceResponse<FraudTempBlockModel>> setTempBlockCustomer(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)@RequestHeader(CustomerServiceConstant.HEADERS_CORRELATION_ID) String correlationId,
            @Parameter(description = "CRM ID", example = "001100000000000000000012004030", required = true) @RequestHeader(CustomerServiceConstant.HEADERS_CRMID) String crmId,
            @RequestBody FraudTempBlockModel fraudTempBlockModel) throws TMBCommonException {

        TmbOneServiceResponse<FraudTempBlockModel> response =new TmbOneServiceResponse<>();
        response.setData(fraudTempBlockModel);
        HttpHeaders responseHeader = TMBUtils.getResponseHeaders();
        responseHeader.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

        try {
            if (fraudTempBlockService.processFraudTempBlock(crmId, fraudTempBlockModel,correlationId)) {
                logger.info("0.[success] fraud set temp block");
                response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                        ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
            } else {
                logger.info("0.[fail] fraud set temp block");
                response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                        ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));
            }
            return ResponseEntity.ok().headers(responseHeader).body(response);
        } catch (Exception e) {
            logger.error("0.[exception]fraud set temp block customer has  : Exception : {}", e);
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.OK, null);
        }
    }
}
