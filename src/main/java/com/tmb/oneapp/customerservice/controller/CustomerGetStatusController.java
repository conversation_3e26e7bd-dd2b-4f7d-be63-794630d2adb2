package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.service.CustomerStatusImpl;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;

/**
 * CustomerTouchMigrationController request mapping will handle apis call and
 * then navigate to respective method to get migration details
 *
 */
@RestController
@Tag(name = "Customer Get Status Api")
public class CustomerGetStatusController {
	private final CustomerStatusImpl customerStatusImpl;
	private static final TMBLogger<CustomerGetStatusController> logger = new TMBLogger<>(
			CustomerGetStatusController.class);

	/**
	 * Constructor
	 *
	 * @param customerStatusImpl
	 */
	@Autowired
	public CustomerGetStatusController(CustomerStatusImpl customerStatusImpl) {
		super();
		this.customerStatusImpl = customerStatusImpl;
	}

	/**
	 * Description:- Inquiry customer status
	 * 
	 * @param crmId
	 * @return customer status description
	 */
	@LogAround
	@Operation(summary = "Get Customer Status Description")
	@GetMapping(value = {"/customers/eb-status/get/{CRM_ID}"})
	public ResponseEntity<TmbOneServiceResponse<String>> getCustomerStatusDesc(
			@Parameter(description = "CRMID", example = "001100000000000000000001184383", required = true) @PathVariable("CRM_ID") String crmId,
			@Parameter(description = "X-Correlation-ID", example = "b605a35e-106b-4ab2-a53c-30ac8908133b", required = true) @RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId) {
		TmbOneServiceResponse<String> oneServiceResponse = new TmbOneServiceResponse<>();
		HttpHeaders responseHeaders = new HttpHeaders();
		responseHeaders.set(CustomerServiceConstant.HEADER_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()));

		try {
			String statusDesc = customerStatusImpl.getCustomerStatusDesc(crmId);
			oneServiceResponse.setData(statusDesc);
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
					ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
			return ResponseEntity.ok().headers(responseHeaders).body(oneServiceResponse);
		} catch (Exception e) {
			logger.error("Unable to fetch data from oneapp db : {}", e);
			oneServiceResponse.setStatus(new TmbStatus(ResponseCode.DATABASE_CONNECTION_ERROR.getCode(),
					ResponseCode.DATABASE_CONNECTION_ERROR.getMessage(),
					ResponseCode.DATABASE_CONNECTION_ERROR.getService(),
					ResponseCode.DATABASE_CONNECTION_ERROR.getDesc()));

			return ResponseEntity.badRequest().headers(responseHeaders).body(oneServiceResponse);

		}

	}

}
