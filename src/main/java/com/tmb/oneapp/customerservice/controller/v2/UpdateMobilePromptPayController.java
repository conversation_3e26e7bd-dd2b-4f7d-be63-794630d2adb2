package com.tmb.oneapp.customerservice.controller.v2;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.updatemobilepromptpay.ChangeMobileV2Request;
import com.tmb.oneapp.customerservice.model.updatemobilepromptpay.UpdatePromptpayMobileV2Response;
import com.tmb.oneapp.customerservice.service.UpdateMobilePromptPayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class UpdateMobilePromptPayController {
    private final UpdateMobilePromptPayService updateMobilePromptPayService;

    public UpdateMobilePromptPayController(UpdateMobilePromptPayService updateMobilePromptPayService) {
        this.updateMobilePromptPayService = updateMobilePromptPayService;
    }

    @LogAround
    @Operation(summary = "Update or Add Mobile number and Deregister PromptPay mobile")
    @PostMapping(value = "/promptpay/mobile/update-mobile", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<UpdatePromptpayMobileV2Response>> updateMobilePromptpay(
            @Parameter(description = "CRM ID", example ="001100000000000000000000090000", required = true) @RequestHeader(name = CustomerServiceConstant.HEADERS_X_CRMID) String crmId,
            @Parameter(description = "X-Correlation-ID", example ="b605a35e-106b-4ab2-a53c-30ac8908133b", required = true) @RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @Valid @RequestBody ChangeMobileV2Request request) throws TMBCommonException {
        return ResponseEntity.ok().body(updateMobilePromptPayService.updateMobileNumberV2(crmId, correlationId, request));
    }

}
