package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.formatter.Formatter;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.DepositHoldBalanceRequest;
import com.tmb.oneapp.customerservice.model.SavingGoalResponse;
import com.tmb.oneapp.customerservice.model.deposit.DepositAccount;
import com.tmb.oneapp.customerservice.service.CustomerAccountDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Pattern;

@RestController
@Validated
public class CustomerAccountDetailController {
    private static final TMBLogger<CustomerAccountDetailController> logger = new TMBLogger<>(CustomerAccountDetailController.class);


    @Autowired
    private Formatter formatter;
    @Autowired
    private CustomerAccountDetailService customerAccountDetailService;
/*
Get customer account saving goal data
@param: correletation id
@param: crm id
@param request
@response
 */

    @Operation(summary = "Get customer account saving goal data")
    @LogAround
    @PostMapping(value = "/customers/deposit/hold/balance")
    public ResponseEntity<TmbServiceResponse<DepositAccount>>  depositHoldBalance(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @Parameter(description = "CRM ID", example = "001100000000000000000012004030", required = true) @Pattern(message = "CRM ID must be a number", regexp="^[0-9]{30}$") @RequestHeader(CustomerServiceConstant.HEADER_CUSTOMER_CRM_ID) String crmId,
            @Valid @RequestBody DepositHoldBalanceRequest request) throws TMBCommonException {

        logger.info("remove customer account saving goal data for correlation-ID: {}", correlationId);
        TmbServiceResponse<DepositAccount> response = customerAccountDetailService.getDepositHoldBalanceResponse(request);
        if(ResponseCode.SUCCESS.getCode().equals(response.getStatus().getCode())){
            return ResponseEntity.ok(response);
        }
        return ResponseEntity.badRequest().body(response);
    }


    /*
Delete saving goal data
@param: correletation id
@param: crm id
@param request
@response
 */

    @Operation(summary = "remove saving goal data")
    @LogAround
    @PostMapping(value = "/customers/deposit/hold/remove")
    public ResponseEntity<TmbOneServiceResponse<String>>  depositHoldRemove(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @Parameter(description = "CRM ID", example = "001100000000000000000012004030", required = true) @Pattern(message = "CRM ID must be a number", regexp="^[0-9]{30}$") @RequestHeader(CustomerServiceConstant.HEADER_CUSTOMER_CRM_ID) String crmId,
            @Parameter(description = "User Name", example = "tmbUser", required = true) @RequestHeader(CustomerServiceConstant.HEADER_USER_NAME) String userName,
            @Parameter(description = "IP Address", example = "localhost", required = true) @RequestHeader(CustomerServiceConstant.HEADER_X_FORWARD_FOR) String ipAddress,
            @Valid @RequestBody SavingGoalResponse request) throws TMBCommonException {

        logger.info("remove customer account saving goal data for correlation-ID: {}", correlationId);
        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        try {
            String sucRes= customerAccountDetailService.removeSavingGoal(crmId, correlationId, request);
            response.setData(sucRes);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService()));

        } catch (Exception e) {
            logger.error("Error while removeCustomerSavingGoal: {}", e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);
        }
        return ResponseEntity.ok(response);
    }




}
