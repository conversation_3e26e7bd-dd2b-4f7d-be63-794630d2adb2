package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.CustomerTransactionLimitApproveRequest;
import com.tmb.oneapp.customerservice.model.CustomerTransactionLimitRejectRequest;
import com.tmb.oneapp.customerservice.model.CustomerTransactionLimitRequest;
import com.tmb.oneapp.customerservice.model.CustomerTransactionLimitResponse;
import com.tmb.oneapp.customerservice.model.TxnTypeDescriptionEntity;
import com.tmb.oneapp.customerservice.service.CustomerLogService;
import com.tmb.oneapp.customerservice.service.CustomerTransactionService;
import com.tmb.oneapp.customerservice.service.EmployeeActivityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.HandlerMapping;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.List;

@RestController
@Validated
public class CustomerTransactionController {
    private static final TMBLogger<CustomerTransactionController> logger = new TMBLogger<>(CustomerTransactionController.class);

    private final CustomerTransactionService customerTransactionService;
    private final CustomerLogService customerLogService;
    private final EmployeeActivityService employeeActivityService;
    private final HttpServletRequest httpServletRequest;

    /**
     * doing dependency injection for CustomerTransactionController through
     * CustomerTransactionController Constructor
     */
    @Autowired
    public CustomerTransactionController(CustomerTransactionService customerTransactionService,
                                         CustomerLogService customerLogService,
                                         EmployeeActivityService employeeActivityService,
                                         HttpServletRequest httpServletRequest) {
        this.customerTransactionService = customerTransactionService;
        this.customerLogService = customerLogService;
        this.employeeActivityService = employeeActivityService;
        this.httpServletRequest = httpServletRequest;
    }

    @Operation(summary = "Get customer transaction type")
    @LogAround
    @GetMapping(value = "/customers/transaction-type/get")
    public ResponseEntity<TmbOneServiceResponse<List<TxnTypeDescriptionEntity>>> getCustomerTransactionType(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId) throws TMBCommonException {

        logger.info("Get customer transaction type for correlation-ID: {}", correlationId);
        TmbOneServiceResponse<List<TxnTypeDescriptionEntity>> response = new TmbOneServiceResponse<>();
        try {
            response.setData(customerTransactionService.txnTypeDescriptionList());
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
        } catch (Exception e) {
            logger.error("Error while getCustomerTransactionType: {}", e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);
        }
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Get customer Transaction limit detail")
    @LogAround
    @PostMapping(value = "/customers/transaction-limit/detail")
    public ResponseEntity<TmbOneServiceResponse<CustomerTransactionLimitResponse>> getCustomerTransactionLimitDetail(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @Parameter(description = "User Name", example = "tmbUser", required = true) @RequestHeader(CustomerServiceConstant.HEADER_USER_NAME) String userName,
            @Valid @RequestBody CustomerTransactionLimitRequest request) throws TMBCommonException {

        logger.info("Get customer service detail for correlation-ID: {}", correlationId);
        TmbOneServiceResponse<CustomerTransactionLimitResponse> response = new TmbOneServiceResponse<>();
        try {

            response.setData(customerTransactionService.getCustomerTransactionLimit(request));
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
        } catch (Exception e) {
            logger.error("Error while getCustomerServiceDetail: {}", e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);
        }
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Reject customer transaction daily limit request")
    @LogAround
    @PostMapping(value = {"/customers/transaction-limit/request/reject", "/customers/transaction-limit/request/cancel"})
    public ResponseEntity<TmbOneServiceResponse<String>> rejectCustomerTransactionLimitRequest(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @Parameter(description = "User Name", example = "tmbUser", required = true) @RequestHeader(CustomerServiceConstant.HEADER_USER_NAME) String userName,
            @Parameter(description = "IP Address", example = "localhost", required = true) @RequestHeader(CustomerServiceConstant.HEADER_X_FORWARD_FOR) String ipAddress,
            @Valid @RequestBody CustomerTransactionLimitRejectRequest request) throws TMBCommonException {

        logger.info("Reject customer transaction daily limit request for correlation-ID: {}", correlationId);
        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        try {
            String restOfTheUrl = (String) httpServletRequest.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE);
            if (restOfTheUrl.contains(CustomerServiceConstant.TRANSACTION_LIMIT_REQUEST_CANCEL_VAL)) {
                request = customerTransactionService.rejectCustomerTransactionLimitRequest(request, userName, true);
            } else {
                request = customerTransactionService.rejectCustomerTransactionLimitRequest(request, userName, false);
            }
            boolean success = request.getIsReject();
            if (success) {
                response.setData(CustomerServiceConstant.CUSTOMER_TRANSACTION_LIMIT_REQUEST_REJECT_SUCCESS);
                response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
                customerLogService.logForTransDailyLimitRequest(correlationId, userName, ipAddress, request, CustomerServiceConstant.ACTIVITY_CUSTOMER_TRANS_DAILY_REQUEST_REJECT);
                employeeActivityService.saveEmpActivityTransLimitRequest(correlationId, userName, request.getReasonReject(), request.getCrmId());
                return ResponseEntity.ok(response);
            } else {
                response.setData(CustomerServiceConstant.CUSTOMER_TRANSACTION_LIMIT_REQUEST_REJECT_FAIL);
                response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService()));
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            logger.error("Error while rejectCustomerTransactionLimitRequest: {}", e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.BAD_REQUEST,
                    null);
        }

    }


    @Operation(summary = "approve customer transaction daily limit request")
    @LogAround
    @PostMapping(value = {"/customers/transaction-limit/request/approve"})
    public ResponseEntity<TmbOneServiceResponse<String>> approveCustomerTransactionLimitRequest(
            @Parameter(description = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @Parameter(description = "User Name", example = "tmbUser", required = true) @RequestHeader(CustomerServiceConstant.HEADER_USER_NAME) String userName,
            @Parameter(description = "IP Address", example = "localhost", required = true) @RequestHeader(CustomerServiceConstant.HEADER_X_FORWARD_FOR) String ipAddress,
            @Valid @RequestBody CustomerTransactionLimitApproveRequest request) throws TMBCommonException {

        logger.info("Approve customer transaction daily limit request for correlation-ID: {}", correlationId);
        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        try {
            request = customerTransactionService.approveCustomerTransactionLimitRequest(request, userName);
            boolean success = request.getIsApproveTransApproveReq();
            if (success) {
                response.setData(CustomerServiceConstant.CUSTOMER_TRANSACTION_LIMIT_REQUEST_APPROVE_SUCCESS);
                response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
                customerLogService.logForTransDailyLimitApproveRequest(correlationId, userName, ipAddress, request, CustomerServiceConstant.ACTIVITY_CUSTOMER_TRANS_DAILY_REQUEST_APPROVE);
                employeeActivityService.saveEmpActivityTransLimitApproveRequest(correlationId, userName, request);
            } else {
                response.setData(CustomerServiceConstant.CUSTOMER_TRANSACTION_LIMIT_REQUEST_APPROVE_FAIL);
                response.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService()));
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            logger.error("Error while rejectCustomerTransactionLimitRequest: {}", e);
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);
        }
        return ResponseEntity.ok(response);
    }


}
