package com.tmb.oneapp.customerservice.controller;

import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.service.CustomerInternalCaseHousekeepingService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller class responsible for housekeeping customer case submit
 */
@RestController
@Tag(name = "Housekeeping customer case submit")
public class CustomerInternalCaseHousekeepingController {

    private static final TMBLogger<CustomerInternalCaseHousekeepingController> logger = new TMBLogger<>(CustomerInternalCaseHousekeepingController.class);
    private final CustomerInternalCaseHousekeepingService customerInternalCaseHousekeepingService;

    /**
     * Constructor
     *
     * @param customerInternalCaseHousekeepingService CustomerInternalCaseHousekeepingService
     */
    public CustomerInternalCaseHousekeepingController(CustomerInternalCaseHousekeepingService customerInternalCaseHousekeepingService) {
    	super();
        this.customerInternalCaseHousekeepingService = customerInternalCaseHousekeepingService;
    }

    /**
     * method : To call CustomerInternalCaseHousekeepingService
     *
     * @return String
     */
    @Operation(summary = "Call case submit housekeeping")
    @PostMapping(value = "/customers/internal/case/housekeeping", produces = MediaType.APPLICATION_JSON_VALUE)
    @LogAround
    public ResponseEntity<TmbOneServiceResponse<String>> callHousekeeping() {

        TmbOneServiceResponse<String> caseHousekeepingResponse = new TmbOneServiceResponse<>();

        try {
            String result = customerInternalCaseHousekeepingService.callHouseKeeping();
            caseHousekeepingResponse.setData(result);
            caseHousekeepingResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                            ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getDesc()));
            return ResponseEntity.status(HttpStatus.OK)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(caseHousekeepingResponse);
        } catch (Exception e) {
            logger.error("Unable to callHousekeeping data : {} ", e);
            caseHousekeepingResponse.setStatus(new TmbStatus(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                            ResponseCode.FAILED.getService(), ResponseCode.FAILED.getDesc()));

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .headers(TMBUtils.getResponseHeaders())
                    .body(caseHousekeepingResponse);
        }
    }
}