package com.tmb.oneapp.customerservice.controller;


import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.customerservice.constant.ResponseCode;
import com.tmb.oneapp.customerservice.model.ImageUploadResponse;
import com.tmb.oneapp.customerservice.model.UploadImageOTTRequest;
import com.tmb.oneapp.customerservice.service.InternationalTransferUploadImageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.CORRELATION_ID;

@RestController
public class InternationalTransferUploadImageController {
    private static TMBLogger<InternationalTransferUploadImageController> logger = new TMBLogger<>(InternationalTransferUploadImageController.class);
    private final InternationalTransferUploadImageService internationalTransferUploadImageService;

    public InternationalTransferUploadImageController(InternationalTransferUploadImageService internationalTransferUploadImageService) {
        this.internationalTransferUploadImageService = internationalTransferUploadImageService;
    }

    @LogAround
    @Operation(summary = "To upload image of recipient to firebase")
    @PostMapping(value = "/customers/OTT/uploadImage", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbOneServiceResponse<ImageUploadResponse>> uploadImage(
            @Parameter(description = CORRELATION_ID, example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @RequestHeader(CORRELATION_ID) String correlationId,
            @Parameter(description = "X-CRMID", example = "001100000000000000000000086006", required = true)
            @RequestHeader("X-CRMID") @Valid @RequestHeaderNonNull String crmId,
            @RequestBody UploadImageOTTRequest request
    ) throws TMBCommonException {
        try {
            logger.info("");
            TmbOneServiceResponse<ImageUploadResponse> response = new TmbOneServiceResponse<>();
            HttpHeaders responseHeaders = new HttpHeaders();
            ImageUploadResponse result = internationalTransferUploadImageService.uploadImage(request, correlationId, crmId);
            response.setStatus(generateSuccessTmbStatusHeader());
            response.setData(result);
            return ResponseEntity.ok().headers(responseHeaders).body(response);
        } catch (Exception e) {
            logger.error("Unable to upload image to firebase", e.getMessage());
            throw new TMBCommonException(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(), HttpStatus.BAD_REQUEST, null);
        }
    }

    TmbStatus generateSuccessTmbStatusHeader() {
        return new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                ResponseCode.SUCCESS.getService());
    }

}
