package com.tmb.oneapp.customerservice.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.CustomerRMIdETERequest;
import com.tmb.oneapp.customerservice.model.CustomerRMIdETEResponse;

@FeignClient(name = "${customer.ekyc.createCRM.service.name}", url = "${customer.ekyc.createCRM.service.url}")
public interface CustomerCreateCRMIDClient {
	@PostMapping(value = "${customer.ekyc.account.service.createRM.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<CustomerRMIdETEResponse> generateCustomerRMId(@RequestHeader HttpHeaders headers,
			@RequestBody CustomerRMIdETERequest request);
}
