package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.PersonalConsentResponse;
import com.tmb.oneapp.customerservice.model.TaxCertificateConsentsRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_APP_ID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_DEPOSIT_HOLD_REQUEST_UID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_DEPOSIT_HOLD_SERVICE_NAME;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.REQUEST_DATE_TIME;

@FeignClient(name = "${feign.personal-consents.service.name}",
        url = "${feign.personal-consents.service.url}")
public interface ETEConsentFeignClient {

    @PostMapping(value = "${feign.personal-consents.service.get.path}",
            consumes = CONTENT_TYPE_VALUE_UTF_8)
    public PersonalConsentResponse getPersonalConsent(
            @RequestHeader(value = HEADER_DEPOSIT_HOLD_SERVICE_NAME, required = true) final String serviceName,
            @RequestHeader(value = HEADER_DEPOSIT_HOLD_REQUEST_UID, required = true) final String requestUid,
            @RequestHeader(value = HEADER_APP_ID, required = true) final String requestAppId,
            @RequestHeader(value = REQUEST_DATE_TIME, required = true) final String requestDatetime,
            @RequestBody TaxCertificateConsentsRequest requests
    );

}
