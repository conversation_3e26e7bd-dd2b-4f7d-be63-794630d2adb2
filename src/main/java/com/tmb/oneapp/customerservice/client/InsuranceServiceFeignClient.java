package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.InsuranceCreateRequestResponse;
import com.tmb.oneapp.customerservice.model.InsuranceTaxInQuiryPolicyResponse;
import com.tmb.oneapp.customerservice.model.InsuranceTaxInquiryPolicyRequests;
import com.tmb.oneapp.customerservice.model.InsuranceTaxSubmitRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${feign.insurance.service.name}", url = "${feign.insurance.service.url}")
public interface InsuranceServiceFeignClient {
    @PostMapping(value = "/bahub/services/v1/taxInquiryPolicy",
            consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8)
    public InsuranceTaxInQuiryPolicyResponse getTaxInquiryPolicy(
            @RequestHeader(value = CustomerServiceConstant.AUTHORIZATION, required = true) final String authorization,
            @RequestBody InsuranceTaxInquiryPolicyRequests requests
    );

    @PostMapping(value = "/bahub/services/v1/taxCreateRequest",
            consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8)
    public InsuranceCreateRequestResponse getTaxInsuranceCreateRequest(
            @RequestHeader(value = CustomerServiceConstant.AUTHORIZATION, required = true) final String authorization,
            @RequestBody InsuranceTaxSubmitRequest requests
    );
}