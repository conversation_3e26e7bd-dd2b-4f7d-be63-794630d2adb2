package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.PostBiometricUpdateConsentResponse;
import com.tmb.oneapp.customerservice.model.EkycBiometricUpdateConsentECRequest;
import com.tmb.oneapp.customerservice.model.EkycBiometricUpdateConsentRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * Interface to get EKYC NDID customer data
 */
@FeignClient(name = "${customer.ekyc.ndid.service.v2.name}", url = "${customer.ete.ekyc.service.url}")
public interface CustomerEkycNDIDV2FeignClient {




    /**
     * endpoint to update biometric consent status
     *
     * @return PostBiometricGetConsentResponse biometric consent data
     */
    @PostMapping(value = "${customer.ete.ekyc.updateConsentEC.service.path}", consumes = "application/json;charset=UTF-8", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<PostBiometricUpdateConsentResponse> postBiometricUpdateConsent(
            @RequestHeader HttpHeaders headers,
            @RequestBody EkycBiometricUpdateConsentECRequest request);
    
    /**
     * endpoint to update biometric consent status
     *
     * @return PostBiometricGetConsentResponse biometric consent data
     */
    @PostMapping(value = "${customer.ete.ekyc.updateConsent.service.path}", consumes = "application/json;charset=UTF-8", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<PostBiometricUpdateConsentResponse> fecthBiometricUpdateConsent(
            @RequestHeader HttpHeaders headers,
            @RequestBody EkycBiometricUpdateConsentRequest request);



}
