package com.tmb.oneapp.customerservice.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.CustomerPersonalUpdateRequest;

@FeignClient(name = "${customer.ekyc.ial.service.name}", url = "${customer.ekyc.ial.service.url}")
public interface CustomerUpdateIALFeignClient {

	@PostMapping(value = "${customer.ekyc.ial.service.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<String> updateCustomerIALValue(@RequestHeader HttpHeaders headers,
			@RequestBody CustomerPersonalUpdateRequest request);

}
