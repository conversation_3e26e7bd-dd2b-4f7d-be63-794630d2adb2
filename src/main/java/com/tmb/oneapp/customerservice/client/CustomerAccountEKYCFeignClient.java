package com.tmb.oneapp.customerservice.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.CustomerAccountETERequest;
import com.tmb.oneapp.customerservice.model.CustomerAccountETEResponse;
import com.tmb.oneapp.customerservice.model.CustomerAccountNoETERequest;
import com.tmb.oneapp.customerservice.model.CustomerAccountNoETEResponse;

/**
 * interface for Customer EKYC Create Account
 *
 */
@FeignClient(name = "${customer.ekyc.account.service.name}", url = "${customer.ekyc.account.service.url}")
public interface CustomerAccountEKYCFeignClient {

	@PostMapping(value = "${customer.ekyc.account.service.generateaccount.path}",
			consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<CustomerAccountNoETEResponse> generateAccountNumber(@RequestHeader HttpHeaders headers,
			@RequestBody CustomerAccountNoETERequest request);

	@PostMapping(value = "${customer.ekyc.account.service.createAccount.path}",
			consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<CustomerAccountETEResponse> createCustomerAccount(@RequestHeader HttpHeaders headers,
			@RequestBody CustomerAccountETERequest request);

}
