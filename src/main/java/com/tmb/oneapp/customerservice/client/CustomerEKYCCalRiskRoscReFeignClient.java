package com.tmb.oneapp.customerservice.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.EKYCCalRiskETERequest;
import com.tmb.oneapp.customerservice.model.EKYCCalRiskRoscReETEResponse;

@FeignClient(name = "${customer.ekyc.calRiskRoscRe.service.name}", url = "${customer.ekyc.calRiskRoscRe.service.url}")
public interface CustomerEKYCCalRiskRoscReFeignClient {
	@PostMapping(value = "${customer.ekyc.calRiskRoscRe.service.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE, produces = CustomerServiceConstant.CONTENT_TYPE_VALUE)
	ResponseEntity<EKYCCalRiskRoscReETEResponse> callCalculateRisk(@RequestBody EKYCCalRiskETERequest reqBody);
}
