package com.tmb.oneapp.customerservice.client;
import com.tmb.oneapp.customerservice.model.edd.CustomerKycEddRequest;
import com.tmb.oneapp.customerservice.model.edd.CustomerKycEddResponse;
import com.tmb.oneapp.customerservice.model.edd.UpdateEddRequestEte;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${customer.edd.service.name}", url = "${customer.edd.service.url}")
public interface CustomerKycEddFeignClient {
    @PostMapping(value = "${customer.edd.get-edd-kyc.service.path}",
            consumes = "application/json;charset=utf-8", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<CustomerKycEddResponse>  getEddCustomer(
            @RequestHeader HttpHeaders headers,
            @RequestBody CustomerKycEddRequest request);

    @PostMapping(value = "${customer.edd.add-edd-kyc.service.path}",
            consumes = "application/json;charset=utf-8", produces = MediaType.APPLICATION_JSON_VALUE)
    void addEddCustomer(
            @RequestHeader HttpHeaders headers,
            @RequestBody UpdateEddRequestEte request);

    @PostMapping(value = "${customer.edd.update-edd-kyc.service.path}",
            consumes = "application/json;charset=utf-8", produces = MediaType.APPLICATION_JSON_VALUE)
    void updateEddCustomer(
            @RequestHeader HttpHeaders headers,
            @RequestBody UpdateEddRequestEte request);
}
