package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.ekycverifywithseven.EKYCCancelResponse;
import com.tmb.oneapp.customerservice.model.ekycverifywithseven.EKYCInquiryResponse;
import com.tmb.oneapp.customerservice.model.ekycverifywithseven.EKYCSubRequest;
import com.tmb.oneapp.customerservice.model.ekycverifywithseven.NDIDVerifyQRResponse;
import com.tmb.oneapp.customerservice.model.ekycverifywithseven.NDIDVerifyResultRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * interface to connect ETE NDID Verify vai 7-11
 */
@FeignClient(name = "NDID-services", url = "${customer.ndid.proxy.verify.qr.url}")
public interface CustomerNDIDProxyClient {
    @PostMapping(name = "${customer.ndid.proxy.verify.qr.service.name}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<NDIDVerifyQRResponse> ekycVerifyAddQR(@RequestHeader Map<String, String> headers,
                                                          @RequestBody EKYCSubRequest request);

    @PostMapping(name = "${customer.ndid.proxy.verify.inquiry.service.name}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<EKYCInquiryResponse> ekycInquiryStatus(@RequestHeader Map<String, String> headers,
                                                          @RequestBody EKYCSubRequest request);

    @PostMapping(name = "${customer.ndid.proxy.verify.inquiry.service.name}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<EKYCCancelResponse> cancelRequest(@RequestHeader Map<String, String> headers,
                                                     @RequestBody EKYCSubRequest request);

    @PostMapping(name = "${customer.ndid.proxy.verify.qr.service.name}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<NDIDVerifyQRResponse> ekycVerifyResult(@RequestHeader Map<String, String> headers,
                                                          @RequestBody NDIDVerifyResultRequest request);
}
