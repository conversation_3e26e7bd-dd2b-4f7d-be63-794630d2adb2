package com.tmb.oneapp.customerservice.client;

import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.GetCustomerEKYCETERequest;

/**
 * feign client for verify the customer
 */
@FeignClient(name = "${customer.ekyc.verify.service.name}", url = "${customer.ekyc.verify.service.url}")
public interface CustomerVerifyFeignClient {

	@PostMapping(value = "${customer.ekyc.verify.service.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<String> verifyCustomerthroughCitizenID(@RequestHeader Map<String, String> headers,
			@RequestBody GetCustomerEKYCETERequest request);

}
