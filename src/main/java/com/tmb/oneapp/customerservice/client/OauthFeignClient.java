package com.tmb.oneapp.customerservice.client;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.VerifyPinCacheRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import jakarta.validation.Valid;

/**
 * interface responsible for calling cache-service
 */
@FeignClient(name = "${oauth.name}", url = "${oauth.endpoint}")
public interface OauthFeignClient {
	@GetMapping(value = "/apis/oauth/cache/{key}", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<TmbOneServiceResponse<String>> getOauthCache(
			@RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) final String correlationId,
			@Valid @PathVariable("key") String keyVerify);
	@PostMapping(value = "/apis/oauth/v2/verify-pin-ref", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<TmbOneServiceResponse<String>> getOauthCacheV2(
			@RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) final String correlationId,
			@Valid @RequestBody VerifyPinCacheRequest verifyPinCacheRequest);
}