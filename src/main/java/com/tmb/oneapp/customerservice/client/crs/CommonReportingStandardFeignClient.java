package com.tmb.oneapp.customerservice.client.crs;

import com.tmb.oneapp.customerservice.model.crs.CommonReportingStandardRequest;
import com.tmb.oneapp.customerservice.model.crs.CommonReportingStandardResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${customer.csr.service.name}", url = "${customer.csr.service.url}")
public interface CommonReportingStandardFeignClient {
    @PostMapping(value = "${customer.csr.create-kyc-crs.service.path}",
            consumes = "application/json;charset=utf-8", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<CommonReportingStandardResponse> createKycCrs (
            @RequestHeader HttpHeaders headers,
            @RequestBody CommonReportingStandardRequest request);

}
