package com.tmb.oneapp.customerservice.client;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.activity.ActivityLogSearchRequest;
import com.tmb.oneapp.customerservice.model.activity.CustomerHistoryActivityResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import com.tmb.oneapp.customerservice.model.activity.Activity;
import com.tmb.oneapp.customerservice.model.activity.ActivityRequest;

import java.util.List;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADERS_DEVICE_ID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_X_FORWARD_FOR;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.X_CRMID;

@FeignClient(name = "${customer.activity.service.name}", url = "${customer.activity.url}")
public interface ActivityFeignClient {
    /**
     * callCustomerActivityLog method consume activity log from activity-service
     * @param correlationId
     * @param acceptLanguage
     * @param timestamp
     * @param request
     * @return
     */
    @PostMapping(value = "${customer.activity.service.log.history.status.url}", consumes = "application/json", produces = "application/json")
    TmbOneServiceResponse<List<CustomerHistoryActivityResponse>> callCustomerActivityLog(@RequestHeader(HEADER_CORRELATION_ID) String correlationId,
                                                                                         @RequestHeader(CustomerServiceConstant.HEADER_ACCEPT_LANGUAGE) String acceptLanguage,
                                                                                         @RequestHeader(CustomerServiceConstant.HEADER_TIMESTAMP) String timestamp,
                                                                                         @RequestBody ActivityLogSearchRequest request);

    @PostMapping(value = "${customer.activity.service.log.status.url}", consumes = "application/json", produces = "application/json")
    TmbOneServiceResponse<ActivityRequest> saveActivityLog(
            @RequestHeader(HEADER_CORRELATION_ID) String correlationId,
            @RequestHeader(X_CRMID) String crmId,
            @RequestHeader(HEADERS_DEVICE_ID) String deviceId,
            @RequestHeader(HEADER_X_FORWARD_FOR) String ipAddress,
            @RequestBody Activity request);
}
