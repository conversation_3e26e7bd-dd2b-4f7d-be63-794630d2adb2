package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.UpdatePromptpayMobileRequest;
import com.tmb.oneapp.customerservice.model.UpdatePromptpayMobileResponse;
import com.tmb.oneapp.customerservice.model.updatemobilepromptpay.UpdatePromptpayMobileV2Request;
import com.tmb.oneapp.customerservice.model.updatemobilepromptpay.UpdatePromptpayMobileV2Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;


@FeignClient(name = "${promptpay.registered.data.name}", url = "${promptpay.registered.data.url}")
public interface RegisterPromptpayClient {
    @PostMapping(value = "${promptpay.registered.data.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8)
    ResponseEntity<UpdatePromptpayMobileResponse> updatePromptpayRegistered(@RequestHeader HttpHeaders headers,
                                                                            @RequestBody UpdatePromptpayMobileRequest updatePromptpayMobileRequest);

    @PostMapping(value = "/v4.0/internal/registered-services/promptpay/mobile/update-mobile", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8)
    ResponseEntity<UpdatePromptpayMobileV2Response> updatePromptPayRegisteredV4(@RequestHeader HttpHeaders headers,
                                                                                @RequestBody UpdatePromptpayMobileV2Request updatePromptpayMobileRequest);
}