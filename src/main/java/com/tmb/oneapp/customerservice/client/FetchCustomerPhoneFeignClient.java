package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.EteCustomerRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.tmb.oneapp.customerservice.model.GetCustomerPhoneRequestETERequest;
import com.tmb.oneapp.customerservice.model.GetCustomerPhoneRequestETEResponse;

@FeignClient(name = "${customer.phone.service.name}", url = "${customer.phone.service.url}")
public interface FetchCustomerPhoneFeignClient {

	@PostMapping(value = "${customer.phone.service.path}", consumes = "application/json;charset=UTF-8", produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<GetCustomerPhoneRequestETEResponse> getCustomerProfilePhoneDetails(
			@RequestHeader HttpHeaders headers, @RequestBody GetCustomerPhoneRequestETERequest request);


	@PostMapping(value = "${customer.create.phone.service.path}", consumes = "application/json;charset=UTF-8", produces = MediaType.APPLICATION_JSON_VALUE)
	void createPhoneCustomer(@RequestHeader HttpHeaders headers, @RequestBody EteCustomerRequest request);

	@PostMapping(value = "${customer.delete.phone.service.path}", consumes = "application/json;charset=UTF-8", produces = MediaType.APPLICATION_JSON_VALUE)
	void deletePhoneCustomer(@RequestHeader HttpHeaders headers, @RequestBody EteCustomerRequest request);

}
