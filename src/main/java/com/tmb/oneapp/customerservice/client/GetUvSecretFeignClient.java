package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.GetSecretResponse;
import com.tmb.oneapp.customerservice.model.GetSecretUvFeignRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * get Uv screat from uv service
 */
@FeignClient(name = "${customer.uvsecret.service.name}", url = "${customer.uvsecret.service.url}")
public interface GetUvSecretFeignClient {

    /**
     * @param getSecretUvFeignRequest callCustomersService method consume customer details from
     *                                core banking
     */
    @PostMapping(value = "${customer.uvsecret.service.path}", consumes = "application/json", produces = "application/json")
    public ResponseEntity<GetSecretResponse> callUvService(@RequestBody GetSecretUvFeignRequest getSecretUvFeignRequest);
}
