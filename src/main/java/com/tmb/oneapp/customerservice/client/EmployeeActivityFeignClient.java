
package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.EmployeeActivity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * EmployeeActivityFeignClient interface consume employee activity for employee
 * info
 */

@FeignClient(name = "${employee.activity.service.name}", url = "${employee.activity.url}")
public interface EmployeeActivityFeignClient {

	/**
	 * @param correlationId
	 * @param data     callEmployee Activity Service method to log employee activity details
	 */
	@PostMapping(value = "${employee.activity.service.url}", consumes = "application/json", produces = "application/json")
	public ResponseEntity<String> callEmployeeActivitytFullService(@RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
			@RequestBody EmployeeActivity data);

}
