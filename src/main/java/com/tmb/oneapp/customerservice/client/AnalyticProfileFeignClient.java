package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.EteQueryRequest;
import com.tmb.oneapp.customerservice.model.GetCustomersGetAnalyticProfileResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * AnalyticProfileFeignClient interface consume province data from core banking
 * service
 */
@FeignClient(name = "${analytic.profile.service.name}", url = "${analytic.profile.service.url}")
public interface AnalyticProfileFeignClient {

    /**
     * @param headers getAnalyticProfile method consume province data from
     *                core banking
     */
    @PostMapping(value = "${analytic.profile.service.getAnalyticProfile.path}", consumes = "application/json", produces = "application/json")
    public ResponseEntity<GetCustomersGetAnalyticProfileResponse> getAnalyticProfile(@RequestHeader Map<String, String> headers,
                                                                                     @RequestBody EteQueryRequest request);

}
