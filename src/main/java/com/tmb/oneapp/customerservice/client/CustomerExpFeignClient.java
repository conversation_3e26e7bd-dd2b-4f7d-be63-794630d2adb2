package com.tmb.oneapp.customerservice.client;

import com.tmb.common.logger.LogAround;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.customerservice.model.AccountSaving;
import com.tmb.oneapp.customerservice.model.favorite.image.UpdateFavoriteImageRequest;
import com.tmb.oneapp.customerservice.model.favorite.image.UploadFavoriteImageResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import jakarta.validation.Valid;

@FeignClient(name = "${customer.exp.service.name}", url = "${customer.exp.service.url}")
public interface CustomerExpFeignClient {

    @LogAround
    @PostMapping(value = "/apis/customer/favorite/image", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<UploadFavoriteImageResponse>> updateFavoriteImage(
            @RequestHeader("x-correlation-id") @Valid String correlationId, @RequestHeader("x-crmid") @Valid String crmId,
            @RequestBody UpdateFavoriteImageRequest updateFavoriteImageRequest);

    @LogAround
    @GetMapping(value = "/apis/customer/accounts/saving/all", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<AccountSaving>> getAllAccountsSaving(
            @RequestHeader(name = "X-CRMID", required = false) String crmId,
            @Valid @RequestHeader("X-Correlation-ID") String correlationId);
}

