package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.DepositHoldBalanceRemoveRequest;
import com.tmb.oneapp.customerservice.model.DepositHoldBalanceRequest;
import com.tmb.oneapp.customerservice.model.RemoveDepositHoldBalanceResponse;
import com.tmb.oneapp.customerservice.model.deposit.DepositGetHoldBalance;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * DepositHoldServiceFeignClient interface consume customer deposit hold details from core banking
 * service
 */
@FeignClient(name = "${deposit.hold.service.name}", url = "${deposit.hold.service.url}")
public interface DepositHoldServiceFeignClient {

	/**
	 * To get deposit hold balance from core banking service
	 * @param serviceName
	 * @param uid
	 * @param dateTime
	 * @param appId
	 * @param request
	 * @return
	 */
	@PostMapping(value = "${deposit.hold.service.get.balance.path}", consumes = "application/json;charset=utf-8", produces = "application/json;charset=utf-8")
	DepositGetHoldBalance getDepositHoldBalance(@RequestHeader(CustomerServiceConstant.HEADER_DEPOSIT_HOLD_SERVICE_NAME) String serviceName,
												@RequestHeader(CustomerServiceConstant.HEADER_DEPOSIT_HOLD_REQUEST_UID) String uid,
												@RequestHeader(CustomerServiceConstant.HEADER_DEPOSIT_HOLD_REQUEST_DATETIME) String dateTime,
												@RequestHeader(CustomerServiceConstant.HEADER_DEPOSIT_HOLD_REQUEST_APP_ID) String appId,
												@RequestBody DepositHoldBalanceRequest request);



	/**
	 * To get deposit hold balance from core banking service
	 * @param serviceName
	 * @param uid
	 * @param dateTime
	 * @param appId
	 * @param request
	 * @return
	 */
	@PostMapping(value = "${deposit.hold.service.remove.balance.path}", consumes = "application/json;charset=utf-8", produces = "application/json;charset=utf-8")
	RemoveDepositHoldBalanceResponse removeDepositHoldBalance(@RequestHeader(CustomerServiceConstant.HEADER_DEPOSIT_HOLD_SERVICE_NAME) String serviceName,
															  @RequestHeader(CustomerServiceConstant.HEADER_DEPOSIT_HOLD_REQUEST_UID) String uid,
															  @RequestHeader(CustomerServiceConstant.HEADER_DEPOSIT_HOLD_REQUEST_DATETIME) String dateTime,
															  @RequestHeader(CustomerServiceConstant.HEADER_DEPOSIT_HOLD_REQUEST_APP_ID) String appId,
															  @RequestBody DepositHoldBalanceRemoveRequest request);

}
