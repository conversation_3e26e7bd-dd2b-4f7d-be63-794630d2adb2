package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.ECASUserDeleteRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.ECASPinETERequest;

@FeignClient(name = "${customer.ecas.service.name}", url = "${customer.ecas.service.url}")
public interface ECASFeignClient {
	@PostMapping(value = "${customer.ekyc.account.service.createPIN.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<String> createECASPin(@RequestBody ECASPinETERequest request);

	@PostMapping(value = "${customer.ekyc.account.service.delete.user.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<String> deleteUser(@RequestBody ECASUserDeleteRequest request);

}
