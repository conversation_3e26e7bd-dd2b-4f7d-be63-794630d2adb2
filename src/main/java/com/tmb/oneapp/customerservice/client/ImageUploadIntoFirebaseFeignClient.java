package com.tmb.oneapp.customerservice.client;

import java.util.Map;

import com.tmb.oneapp.customerservice.model.ImageUploadResponse;
import com.tmb.oneapp.customerservice.model.commonfr.VerifyLivenessResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.ProfileImageData;

@FeignClient(name = "${customer.touch.firbase.upload.image.service.name}", url = "${customer.touch.firbase.upload.image.url}")
public interface ImageUploadIntoFirebaseFeignClient {

	/**
	 * endpoint for upload image into firebase
	 * 
	 * @param requestHeadersParameter
	 * @param file
	 * @param path
	 * @return
	 */
	@PostMapping(value = "/apis/firebase/internal/upload/image", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	public ResponseEntity<TmbOneServiceResponse<ProfileImageData>> uploadImage(
			@RequestHeader Map<String, String> requestHeadersParameter,
			@RequestPart(value = CustomerServiceConstant.FILE) MultipartFile file,
			@RequestPart(value = CustomerServiceConstant.PATH) String path,
			@RequestPart(value = CustomerServiceConstant.FILE_NAME) String fileName);

	@PostMapping(value = "/apis/firebase/internal/upload/image", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	ResponseEntity<TmbOneServiceResponse<ImageUploadResponse>> uploadImageDifferentResponse(
			@RequestHeader Map<String, String> requestHeadersParameter,
			@RequestPart(value = CustomerServiceConstant.FILE) MultipartFile file,
			@RequestPart(value = CustomerServiceConstant.PATH) String path,
			@RequestPart(value = CustomerServiceConstant.FILE_NAME) String fileName);

	@PostMapping(value = "/v1/firebase/internal/common-fr/verify-liveness", consumes = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<TmbOneServiceResponse<VerifyLivenessResponse>> verifyLiveness(
			@RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
			@RequestHeader(name = CustomerServiceConstant.X_HEADER_CRMID) String crmId);
}
