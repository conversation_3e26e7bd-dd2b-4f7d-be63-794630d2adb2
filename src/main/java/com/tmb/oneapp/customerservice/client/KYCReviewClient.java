package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.profile.KycReviewRequest;
import com.tmb.oneapp.customerservice.model.profile.KycReviewResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${kyc.review.service}", url = "${kyc.review.url}")
public interface KYCReviewClient {
	@PostMapping(value = "${kyc.review.token.endpoint}")
	ResponseEntity<KycReviewResponse> getKycReviewAccessToken(@RequestHeader("Authorization") String authHeader, @RequestBody KycReviewRequest reqBody);
}