package com.tmb.oneapp.customerservice.client;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.pdpa.PDPAContentQuery;
import com.tmb.oneapp.customerservice.model.pdpa.PDPAContentResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${ete.pdpa.content.service.name}", url = "${ete.pdpa.content.url}")
public interface PDPAFormFeignClient {

    @PostMapping(value = "${ete.pdpa.content.endpoint}", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    TmbOneServiceResponse<PDPAContentResponse> getPDPAForm(
            @RequestHeader(value = CustomerServiceConstant.HEADER_SERVICE_NAME) String serviceName,
            @RequestHeader(value = CustomerServiceConstant.HEADER_REQUEST_UUID) String uuid,
            @RequestHeader(value = CustomerServiceConstant.HEADER_APP_ID) String appId,
            @RequestHeader(value = CustomerServiceConstant.HEADER_REQUEST_DATE_TIME) String reqDateTime,
            @RequestBody PDPAContentQuery request
    );

    @PostMapping(value = "${ete.pdpa.content.market.endpoint}", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    TmbOneServiceResponse<PDPAContentResponse> getMarketConductForm(
            @RequestHeader(value = CustomerServiceConstant.HEADER_SERVICE_NAME) String serviceName,
            @RequestHeader(value = CustomerServiceConstant.HEADER_REQUEST_UUID) String uuid,
            @RequestHeader(value = CustomerServiceConstant.HEADER_APP_ID) String appId,
            @RequestHeader(value = CustomerServiceConstant.HEADER_REQUEST_DATE_TIME) String reqDateTime,
            @RequestBody PDPAContentQuery request
    );

}