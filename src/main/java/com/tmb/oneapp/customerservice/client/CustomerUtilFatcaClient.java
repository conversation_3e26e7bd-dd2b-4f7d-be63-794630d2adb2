package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.CustomerUtilFatcaRequest;
import com.tmb.oneapp.customerservice.model.CustomerUtilFatcaResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * client for calculate-fatca
 */
@FeignClient(name = "${customer.fatca.calculate.service.name}", url = "${customer.fatca.calculate.url}")
public interface CustomerUtilFatcaClient {
    @PostMapping(value = "${customer.fatca.calculate.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<CustomerUtilFatcaResponse> fatcaCulaulate(@RequestHeader Map<String, String> headers, @RequestBody CustomerUtilFatcaRequest request);
}
