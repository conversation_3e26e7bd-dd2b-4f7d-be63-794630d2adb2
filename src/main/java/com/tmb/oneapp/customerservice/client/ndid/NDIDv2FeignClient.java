package com.tmb.oneapp.customerservice.client.ndid;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.ndid.NDIDResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.SERVICE_NAME;

@FeignClient(name = "${ndid.service.v2.feign.client.name}",
        url = "${ndid.service.v2.feign.client.url}")
public interface NDIDv2FeignClient {

    String GET_V2_NDID_DATA_SERVICE_NAME = "/v2/ndid/data";
    @GetMapping(value = GET_V2_NDID_DATA_SERVICE_NAME,
            consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<NDIDResponse> getV2NDIDData(
            @RequestHeader(value = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @RequestHeader(value = SERVICE_NAME,
                    defaultValue = GET_V2_NDID_DATA_SERVICE_NAME) String serviceName,
            @RequestHeader(value = "Last-Modified") String lastMModified,
            @RequestHeader(value = "App-ID") String appId,
            @RequestParam("reference_id") String referenceId,
            @RequestParam("required_data_flag") String requireDataFlag);

}