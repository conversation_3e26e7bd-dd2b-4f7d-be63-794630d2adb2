package com.tmb.oneapp.customerservice.client.ekyc;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.ndid.GetBiometricResponse;
import com.tmb.oneapp.customerservice.model.ndid.PostBiometricComparisonRequest;
import com.tmb.oneapp.customerservice.model.ndid.PostBiometricComparisonResponse;
import com.tmb.oneapp.customerservice.model.ndid.PostProspectBiometricResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.EKYC_HEADER_APP_ID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.EKYC_HEADER_DOCUMENT_ID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.EKYC_HEADER_INITIAL_VECTOR;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.EKYC_HEADER_LAST_MODIFIED;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.SERVICE_NAME;

@FeignClient(name = "${ekyc.service.v2.feign.client.name}",
            url = "${ekyc.service.v2.feign.client.url}")
public interface EKYCv2FeignClient {

    String POST_V2_FACES_BIOMETRICS_COMPARISON_SERVICE_NAME = "/v2/faces/biometrics-comparison";
    @PostMapping(value = POST_V2_FACES_BIOMETRICS_COMPARISON_SERVICE_NAME,
            produces = MediaType.APPLICATION_JSON_VALUE,
            consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<PostBiometricComparisonResponse> postV2FacesBiometricsComparison(
            @RequestHeader(value = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @RequestHeader(value = SERVICE_NAME,
                    defaultValue = POST_V2_FACES_BIOMETRICS_COMPARISON_SERVICE_NAME) String serviceName,
            @RequestHeader(value = EKYC_HEADER_LAST_MODIFIED) String lastModified,
            @RequestHeader(value = EKYC_HEADER_APP_ID) String appId,
            @RequestBody PostBiometricComparisonRequest request);

    String GET_V2_FACES_BIOMETRICS_COMPARISON_SERVICE_NAME = "/v2/faces/biometrics-comparison";
    @GetMapping(value = GET_V2_FACES_BIOMETRICS_COMPARISON_SERVICE_NAME,
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<GetBiometricResponse> getV2FacesBiometricsComparison(
            @RequestHeader(value = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @RequestHeader(value = SERVICE_NAME,
                    defaultValue = GET_V2_FACES_BIOMETRICS_COMPARISON_SERVICE_NAME) String serviceName,
            @RequestHeader(value = EKYC_HEADER_LAST_MODIFIED) String lastModified,
            @RequestHeader(value = EKYC_HEADER_APP_ID) String appId,
            @RequestHeader(value = EKYC_HEADER_DOCUMENT_ID) String documentId,
            @RequestHeader(value = EKYC_HEADER_INITIAL_VECTOR) String initialVector,
            @RequestParam(value = "biometric_ref_id", required = false) String biometricRefId,
            @RequestParam(value = "primary_flag", required = false, defaultValue = "N") String primaryFlag
    );

    String POST_V2_PROSPECTS_BIOMETRICS_SERVICE_NAME = "/v2/prospects/biometrics";
    @PostMapping(value = POST_V2_PROSPECTS_BIOMETRICS_SERVICE_NAME,
            produces = MediaType.APPLICATION_JSON_VALUE,
            consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<PostProspectBiometricResponse> postV2ProspectBiometrics(
            @RequestHeader(value = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @RequestHeader(value = SERVICE_NAME,
                    defaultValue = POST_V2_PROSPECTS_BIOMETRICS_SERVICE_NAME) String serviceName,
            @RequestHeader(value = EKYC_HEADER_LAST_MODIFIED) String lastMModified,
            @RequestHeader(value = EKYC_HEADER_APP_ID) String appId,
            @RequestBody PostBiometricComparisonRequest request);

    String GET_V2_PROSPECTS_BIOMETRICS_SERVICE_NAME = "/v2/prospects/biometrics";
    @GetMapping(value = GET_V2_PROSPECTS_BIOMETRICS_SERVICE_NAME,
            consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<GetBiometricResponse> getV2ProspectBiometrics(
            @RequestHeader(value = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @RequestHeader(value = SERVICE_NAME,
                    defaultValue = GET_V2_PROSPECTS_BIOMETRICS_SERVICE_NAME) String serviceName,
            @RequestHeader(value = EKYC_HEADER_LAST_MODIFIED) String lastModified,
            @RequestHeader(value = EKYC_HEADER_APP_ID) String appId,
            @RequestHeader(value = "Biometric-Data-Source") String biometricDataSource,
            @RequestHeader(value = "Biometric-Data-Method") String biometricDataMethod,
            @RequestHeader(EKYC_HEADER_DOCUMENT_ID) String documentID,
            @RequestHeader(EKYC_HEADER_INITIAL_VECTOR) String initialVector);

    String POST_V2_PROSPECTS_BIOMETRICS_COMPARISON_SERVICE_NAME = "/v2/prospects/biometrics-comparison";
    @PostMapping(value = POST_V2_PROSPECTS_BIOMETRICS_COMPARISON_SERVICE_NAME,
            produces = MediaType.APPLICATION_JSON_VALUE,
            consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<PostBiometricComparisonResponse> postV2ProspectBiometricsComparison(
            @RequestHeader(value = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
            @RequestHeader(value = SERVICE_NAME,
                    defaultValue = POST_V2_PROSPECTS_BIOMETRICS_COMPARISON_SERVICE_NAME) String serviceName,
            @RequestHeader(value = EKYC_HEADER_LAST_MODIFIED) String lastModified,
            @RequestHeader(value = EKYC_HEADER_APP_ID) String appId,
            @RequestBody PostBiometricComparisonRequest request);

}
