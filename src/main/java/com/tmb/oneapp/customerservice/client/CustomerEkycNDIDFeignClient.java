package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.EkycIdpConsentRequestFormat;
import com.tmb.oneapp.customerservice.model.EkycIdpConsentResponseFormat;
import com.tmb.oneapp.customerservice.model.EkycInquireNdidRequestBodyRequestFormat;
import com.tmb.oneapp.customerservice.model.EkycInquireNdidRequestResponseFormat;
import com.tmb.oneapp.customerservice.model.EkycNDIDConditionResponse;
import com.tmb.oneapp.customerservice.model.PostNDIDConditionFlagRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * Interface to get EKYC NDID customer data
 */
@FeignClient(name = "${customer.ekyc.ndid.service.name}", url = "${customer.ekyc.ndid.service.url}")
public interface CustomerEkycNDIDFeignClient {

    /**
     * endpoint to retrieve NDID accept conditions
     *
     * @return EkycNDIDConditionResponse NDID accept status
     */
    @PostMapping(value = "${customer.ekyc.ndid.condition_flag.service.path}", consumes = "application/json;charset=UTF-8", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<EkycNDIDConditionResponse> postConditionFlag(
            @RequestBody PostNDIDConditionFlagRequest request);

    /**
     * endpoint to get customer Ekyc Inquire NDID Incoming List Request
     *
     * @param ekycInquireNdidRequestBodyRequestFormat EkycInquireNdidRequestBodyRequestFormat
     * @return ResponseEntity<List < EkycInquireNdidRequestResponseFormat>>
     */
    @PostMapping(value = "${customer.ekyc.ndid.incoming.list}", consumes = "application/json;charset=UTF-8", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<List<EkycInquireNdidRequestResponseFormat>> getEkycInquireNdidRequestResponse(@RequestBody EkycInquireNdidRequestBodyRequestFormat ekycInquireNdidRequestBodyRequestFormat);

    /**
     * endpoint to PUT customer Ekyc Idp Consent
     *
     * @return ResponseEntity<EkycIdpConsentResponseFormat>
     */
    @PutMapping(value = "${customer.ekyc.ndid.consent}", consumes = "application/json;charset=UTF-8", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<EkycIdpConsentResponseFormat> putEkycIdpConsent(
            @RequestBody EkycIdpConsentRequestFormat ekycIdpConsentRequestFormat
    );

}
