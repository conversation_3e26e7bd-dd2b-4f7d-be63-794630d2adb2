package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.SalesforceAuthenticationRequestFormat;
import com.tmb.oneapp.customerservice.model.SalesforceAuthenticationResponseFormat;
import feign.codec.Encoder;
import feign.form.spring.SpringFormEncoder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * interface for get authentication for Salesforce
 */
@FeignClient(name = "${tmb.salesforce.authentication.name}", url = "${tmb.salesforce.authentication.url}", configuration = SalesforceAuthenticationFeignClient.Configuration.class)
public interface SalesforceAuthenticationFeignClient {
    /**
     * endpoint to get customer case status from Salesforce
     * <p>
     * //     * @param salesforceAuthenticationRequestFormat
     *
     * @return ResponseEntity<SalesforceAuthenticationResponseFormat>
     */
    @PostMapping(value = "${tmb.salesforce.authentication.path}", consumes = "application/x-www-form-urlencoded", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<SalesforceAuthenticationResponseFormat> getAuthentication(@RequestBody SalesforceAuthenticationRequestFormat salesforceAuthenticationRequestFormat);

    class Configuration {
        @Bean
        Encoder feignFormEncoder(ObjectFactory<HttpMessageConverters> converters) {
            return new SpringFormEncoder(new SpringEncoder(converters));
        }
    }
}
