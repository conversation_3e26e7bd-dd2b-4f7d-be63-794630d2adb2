package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.CustomersProfileETEResponse;
import com.tmb.oneapp.customerservice.model.EteQueryRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${customer.profile.service.name}", url = "${customer.profile.service.url}")
public interface CustomersProfileFeignClient {

    @PostMapping(value = "${customer.profile.endpoint.get.profile}", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<CustomersProfileETEResponse> getCustomersProfile(@RequestHeader HttpHeaders headers, @RequestBody EteQueryRequest request);
}
