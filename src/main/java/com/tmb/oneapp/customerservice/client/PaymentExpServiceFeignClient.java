package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.PromptPayValidationETEResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * Feign Client to connect Accounts Service
 */
@FeignClient(name = "${feign.payment-exp-service.name}", url = "${feign.payment-exp-service.url}")
public interface PaymentExpServiceFeignClient {
	
	@PostMapping(value = "${feign.payment-exp-service.endpoint.transfer.promptpay-validation}", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<PromptPayValidationETEResponse> validatePromptpay(
					@RequestHeader Map<String, String> headrs,
					@RequestBody String promptPayRequest
	);
	
}
