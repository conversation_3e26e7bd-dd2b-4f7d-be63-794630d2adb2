package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.EteCustomerRequest;
import com.tmb.oneapp.customerservice.model.PersonConsentsUpdateRequest;
import com.tmb.oneapp.customerservice.model.PersonalUpdateConsentResponse;
import com.tmb.oneapp.customerservice.model.PersonalUpdateCustomerResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_APP_ID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_DEPOSIT_HOLD_REQUEST_UID;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.HEADER_DEPOSIT_HOLD_SERVICE_NAME;
import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.REQUEST_DATE_TIME;

@FeignClient(name = "${feign.personal-consents.service.update.name}",
        url = "${feign.personal-consents.service.update.url}")
public interface ETEUpdateConsentFeignClient {

    @PostMapping(value = "${feign.personal-consents.service.update.path}",
            consumes = CONTENT_TYPE_VALUE_UTF_8)
    public PersonalUpdateConsentResponse updatePersonalConsent(
            @RequestHeader(value = HEADER_DEPOSIT_HOLD_SERVICE_NAME, required = true) final String serviceName,
            @RequestHeader(value = HEADER_DEPOSIT_HOLD_REQUEST_UID, required = true) final String requestUid,
            @RequestHeader(value = HEADER_APP_ID, required = true) final String requestAppId,
            @RequestHeader(value = REQUEST_DATE_TIME, required = true) final String requestDatetime,
            @RequestBody PersonConsentsUpdateRequest requests
    );

    @PostMapping(value = "${feign.personal-consents.service.update.path}",
            consumes = CONTENT_TYPE_VALUE_UTF_8)
    PersonalUpdateCustomerResponse updatePersonalCustomer(
            @RequestHeader HttpHeaders headers,
            @RequestBody EteCustomerRequest requests
    );
}
