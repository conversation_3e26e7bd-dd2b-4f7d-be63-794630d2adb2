package com.tmb.oneapp.customerservice.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

@FeignClient(name = "${tmb.service.clear.session.name}", url = "${tmb.service.clear.session.url}")
public interface TMBServiceClient {

    @PostMapping(value = "${tmb.service.clear.session.path}")
    String clearSessionFromCC(@RequestHeader Map<String, String> headers);
}