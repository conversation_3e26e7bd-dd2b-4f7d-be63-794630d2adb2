package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.TaxCertificateConsentsRequest;
import com.tmb.oneapp.customerservice.model.TaxCertificateConsentsResponse;
import com.tmb.oneapp.customerservice.model.TaxCertificateConsentsUpdateRequest;
import com.tmb.oneapp.customerservice.model.TaxCertificateUpdateResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${feign.tax-certificate-consents.service.name}",
        url = "${feign.tax-certificate-consents.service.url}")
public interface TaxCertificateConsentsFeignClient {
    @PostMapping(value = "${feign.tax-certificate-consents.get.path}",
            consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8)
    public TaxCertificateConsentsResponse getTaxCertificateConsents(
            @RequestHeader(value = CustomerServiceConstant.HEADER_DEPOSIT_HOLD_SERVICE_NAME, required = true) final String serviceName,
            @RequestHeader(value = CustomerServiceConstant.HEADER_DEPOSIT_HOLD_REQUEST_UID, required = true) final String requestUid,
            @RequestHeader(value = CustomerServiceConstant.HEADER_APP_ID, required = true) final String requestAppId,
            @RequestHeader(value = CustomerServiceConstant.REQUEST_DATE_TIME, required = true) final String requestDatetime,
            @RequestBody TaxCertificateConsentsRequest requests
    );

    @PostMapping(value = "${feign.tax-certificate-consents.update.path}",
            consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8)
    public TaxCertificateUpdateResponse updateTaxCertificateConsents(
            @RequestHeader(value = CustomerServiceConstant.HEADER_DEPOSIT_HOLD_SERVICE_NAME, required = true) final String serviceName,
            @RequestHeader(value = CustomerServiceConstant.HEADER_DEPOSIT_HOLD_REQUEST_UID, required = true) final String requestUid,
            @RequestHeader(value = CustomerServiceConstant.HEADER_APP_ID, required = true) final String requestAppId,
            @RequestHeader(value = CustomerServiceConstant.REQUEST_DATE_TIME, required = true) final String requestDatetime,
            @RequestBody TaxCertificateConsentsUpdateRequest requests
    );

}