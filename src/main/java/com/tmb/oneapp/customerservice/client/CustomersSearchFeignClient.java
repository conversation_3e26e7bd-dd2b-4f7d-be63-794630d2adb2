package com.tmb.oneapp.customerservice.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * CustomersSearchFeignClient interface consume customer details from core banking
 * service
 */
@FeignClient(name = "${customer.search.service.name}", url = "${customer.search.service.url}")
public interface CustomersSearchFeignClient {

	/**
	 * @param reqTemplate
	 * @param headers     callCustomersService method consume customer details from
	 *                    core banking
	 */
	@PostMapping(value = "${customer.search.service.path}", consumes = "application/json", produces = "application/json")
	public ResponseEntity<String> callCustomersService(@RequestHeader Map<String, String> headers,
			@RequestBody String reqTemplate);

}
