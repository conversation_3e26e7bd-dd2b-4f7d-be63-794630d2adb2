package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.EteUnBlockRequest;
import com.tmb.oneapp.customerservice.model.profile.KycCustomerInfo;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${kyc.get.customer.service.name}", url = "${kyc.get.customer.details.url}")
public interface CustomerKycFeignClient {

	@CircuitBreaker(name = "customersGetKyc")
	@PostMapping(value = "${kyc.get.customer.endpoint}", consumes = "application/json;charset=UTF-8", produces = "application/json")
	ResponseEntity<KycCustomerInfo> getCustomerKyc(@RequestHeader HttpHeaders headers,
												   @RequestBody String request);

	@PostMapping(value = "${kyc.unblock.endpoint}", consumes = "application/json;charset=UTF-8", produces = "application/json")
	void unblockKyc(@RequestHeader HttpHeaders headers,
					@RequestBody EteUnBlockRequest request);
}