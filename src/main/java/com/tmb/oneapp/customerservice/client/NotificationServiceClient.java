package com.tmb.oneapp.customerservice.client;

import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.notification.PushInquiryModel;
import com.tmb.oneapp.customerservice.model.ods.ActivityEventRequest;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.request.notification.NotificationRequest;
import com.tmb.common.model.response.notification.NotificationResponse;

import jakarta.validation.Valid;


@FeignClient(name = "notification-service", url = "${notification-service.url}")
public interface NotificationServiceClient {

	@PostMapping(value = "${notification-service.e-noti.send-message.endpoint}")
	TmbOneServiceResponse<NotificationResponse> sendMessage(
			@RequestHeader(value = CustomerServiceConstant.HEADER_CORRELATION_ID) final String xCorrelationId,
			NotificationRequest request);

	@GetMapping(value = "${notification-service.get.push.settings.endpoint}")
	TmbServiceResponse<PushInquiryModel> getPushNotificationSettings(
			@Valid @RequestHeader(value = CustomerServiceConstant.HEADERS_FRUAD_X_CRM_ID) final String crmID,
			@RequestHeader(value = CustomerServiceConstant.HEADER_CORRELATION_ID) final String xCorrelationId
	);

	/**
	 * endpoint to add customer activity to ODS server
	 */
	@PostMapping(value = "${notification-service.save.customer.activity.to.ods}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE, produces = CustomerServiceConstant.CONTENT_TYPE_VALUE)
	ResponseEntity<TmbOneServiceResponse<Boolean>> saveCustomerActivityToOds(@Valid @RequestBody ActivityEventRequest event,
																			 @Parameter(description = "X-Correlation-ID", example = "c28f91e4-881e-4387-a597-4a39c2822b3c", required = true)
																			 @RequestHeader(value = CustomerServiceConstant.HEADER_CORRELATION_ID) final String correlationId);
}