package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.universallink.VerifyUniversalLinkETEResponse;
import com.tmb.oneapp.customerservice.model.universallink.VerifyUniversalLinkRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${customer.ekyc.ndid.universal-link.name}", url = "${customer.ekyc.ndid.universal-link.uri}")
public interface UniversalLinkFeignClient {
    @PostMapping(value = "${customer.ekyc.ndid.verify-universal-link}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<VerifyUniversalLinkETEResponse> verifyUniversalLink(@RequestHeader HttpHeaders headers,
                                                                       @RequestBody VerifyUniversalLinkRequest request);
}
