package com.tmb.oneapp.customerservice.client;

import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.EKYCGetFatcaFlagRequest;
import com.tmb.oneapp.customerservice.model.EKYCGetFatcaFlagResponse;

/**
 * interface for validation for customer details from DOPA Service
 */
@FeignClient(name = "${ekyc.fatca.verify.service.name}", url = "${ekyc.fatca.verify.service.url}")
public interface EKycFatcaVerifyFeignClient {

	@PostMapping(value = "${ekyc.fatca.verify.service.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EKYCGetFatcaFlagResponse> validationFatcaAnswer(@RequestHeader Map<String, String> headers,
			@RequestBody EKYCGetFatcaFlagRequest answers);

}
