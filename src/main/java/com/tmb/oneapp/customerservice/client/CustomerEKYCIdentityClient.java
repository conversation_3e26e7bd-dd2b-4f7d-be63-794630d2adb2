package com.tmb.oneapp.customerservice.client;

import java.util.Map;

import com.tmb.oneapp.customerservice.client.ndid.NDIDv2FeignClient;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.CustomerEKYCIdentityCloseClientResponse;
import com.tmb.oneapp.customerservice.model.CustomerEKYCIdentityCloseRequest;
import com.tmb.oneapp.customerservice.model.CustomerEKYCStatusRequest;
import com.tmb.oneapp.customerservice.model.EKYCGetConsentRequest;
import com.tmb.oneapp.customerservice.model.EKYCGetConsentResponse;
import com.tmb.oneapp.customerservice.model.EKYCIdentityETEResponse;

@FeignClient(name = "${customer.ekyc.identity.service.name}", url = "${customer.ekyc.identity.service.url}")
public interface CustomerEKYCIdentityClient {

	/**
	 * Calculate period between versions
	 * @deprecated
	 * This method is no longer acceptable to call service '/v3.0/internal/kyc/identity/check-request-status' after the release of 9.2.
	 * <p> Use {@link NDIDv2FeignClient#getV2NDIDData(String, String, String, String, String, String)} instead.
	 *
	 * @param headers Map
	 * @param request CustomerEKYCStatusRequest
	 * @return computed time
	 */
	@Deprecated(since = "r9.2", forRemoval = true)
	@PostMapping(name = "${customer.ekyc.identity.service.status.name}", value = "${customer.ekyc.identity.service.status.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EKYCIdentityETEResponse> checkRequestStatus(@RequestHeader Map<String, String> headers, @RequestBody CustomerEKYCStatusRequest request);

	@PostMapping(name = "${customer.ekyc.identity.service.close.name}", value = "${customer.ekyc.identity.service.close.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<CustomerEKYCIdentityCloseClientResponse> requestClose(@RequestHeader Map<String, String> headers,
			@RequestBody CustomerEKYCIdentityCloseRequest customerEKYCIdentityCloseRequest);

	@PostMapping(name = "${customer.ekyc.ndidconsent.service.name}", value = "${customer.ekyc.ndidconsent.service.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EKYCGetConsentResponse> getNDIDConsent(@RequestHeader Map<String, String> headers,
			@RequestBody EKYCGetConsentRequest customerEKYCIdentityCloseRequest);
}
