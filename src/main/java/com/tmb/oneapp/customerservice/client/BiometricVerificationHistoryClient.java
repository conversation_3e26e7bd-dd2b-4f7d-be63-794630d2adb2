package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.BiometricVerificationHistoryModel;
import com.tmb.oneapp.customerservice.model.BiometricVerificationHistoryRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * interface for get customer biometric verification history
 */
@FeignClient(name = "${biometric.verification.history.data.name}", url = "${biometric.verification.history.data.url}")
public interface BiometricVerificationHistoryClient {

    /**
     * endpoint to get customer biometric verification history
     *
     */
    @PostMapping(value = "${biometric.verification.history.data.path}", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<List<BiometricVerificationHistoryModel>> getBiometricVerificationHistory(@RequestBody BiometricVerificationHistoryRequest request);

}