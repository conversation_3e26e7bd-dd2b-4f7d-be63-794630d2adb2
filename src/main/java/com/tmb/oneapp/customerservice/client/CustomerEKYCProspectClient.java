package com.tmb.oneapp.customerservice.client;

import java.util.Map;

import com.tmb.oneapp.customerservice.client.ekyc.EKYCv2FeignClient;
import com.tmb.oneapp.customerservice.model.ndid.PostBiometricComparisonRequest;
import com.tmb.oneapp.customerservice.model.prospect.EKYCProspectUpdateProspectResponse;
import com.tmb.oneapp.customerservice.model.prospect.EKYCGetProspectNameEngETERequest;
import com.tmb.oneapp.customerservice.model.prospect.EKYCProspectSearchProspectCustomerDataResponse;
import com.tmb.oneapp.customerservice.model.prospect.EKYCProspectCustomerETEResponse;
import com.tmb.oneapp.customerservice.model.prospect.EKYCProspectSearchProspectDataResponse;
import com.tmb.oneapp.customerservice.model.prospect.EKYCGetProspectNameThaiETERequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.EKYCDeleteProspectETERequest;
import com.tmb.oneapp.customerservice.model.EKYCGetProspectETERequest;
import com.tmb.oneapp.customerservice.model.EKYCImageETERequest;
import com.tmb.oneapp.customerservice.model.EKYCProspectETEGetRequest;
import com.tmb.oneapp.customerservice.model.EKYCProspectETERequest;
import com.tmb.oneapp.customerservice.model.EKYCProspectETEResonse;
import com.tmb.oneapp.customerservice.model.EKYCProspectImageETEResonse;
import com.tmb.oneapp.customerservice.model.EKYCProspectSearchETEResponse;
import com.tmb.oneapp.customerservice.model.EKYCVerifyBioMetricCustomerETERequest;
import com.tmb.oneapp.customerservice.model.EKYCVerifyBioMetricCustomerETEResponse;
import com.tmb.oneapp.customerservice.model.EYCProspectDeleteResponse;

/**
 * interface to connect ETE Customer prospect Service
 */
@FeignClient(name = "${customer.ekyc.prospect.service.name}", url = "${customer.ekyc.prospect.service.url}")
public interface CustomerEKYCProspectClient {

	@PostMapping(value = "${customer.ekyc.prospect.service.create.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EKYCProspectETEResonse> createProspectData(@RequestHeader Map<String, String> headers,
			@RequestBody EKYCProspectETERequest request);

	@PostMapping(value = "${customer.ekyc.prospect.service.create.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EKYCProspectETEResonse> postProspectCreate(@RequestHeader Map<String, String> headers,
			@RequestBody Map<String, Object> request);

	@PostMapping(value = "${customer.ekyc.prospect.service.update.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EKYCProspectUpdateProspectResponse> updateProspectData(@RequestHeader Map<String, String> headers,
																		  @RequestBody EKYCProspectSearchProspectCustomerDataResponse request);

	@PostMapping(value = "${customer.ekyc.prospect.service.update.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EKYCProspectCustomerETEResponse> updateProspectCustomerData(
			@RequestHeader Map<String, String> headers, @RequestBody EKYCProspectSearchProspectCustomerDataResponse request);

	@PostMapping(value = "${customer.ekyc.prospect.service.get.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EKYCProspectSearchProspectCustomerDataResponse> getProspectData(@RequestHeader Map<String, String> headers,
																				   @RequestBody EKYCProspectETEGetRequest request);

	@PostMapping(value = "${customer.ekyc.prospect.service.get.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EKYCProspectSearchProspectCustomerDataResponse> getProspectCustomerData(@RequestHeader Map<String, String> headers,
																						   @RequestBody EKYCProspectETEGetRequest request);

	/**
	 * Calculate period between versions
	 * @deprecated
	 * This method is no longer acceptable to call service '/v3.0/internal/kyc/prospect/add-image' after the release of 9.2.
	 * <p> Use {@link EKYCv2FeignClient#postV2ProspectBiometrics(String, String, String, String, PostBiometricComparisonRequest)}  instead.
	 *
	 * @param headers Map
	 * @param request EKYCImageETERequest
	 * @return computed time
	 */
	@Deprecated(since = "r9.2", forRemoval = true)
	@PostMapping(value = "${customer.ekyc.prospect.service.image.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EKYCProspectImageETEResonse> saveImageData(@RequestHeader Map<String, String> headers,
			@RequestBody EKYCImageETERequest request);

	@PostMapping(value = "${customer.ekyc.prospect.service.search.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EKYCProspectSearchETEResponse> searchProspectData(@RequestHeader Map<String, String> headers,
			@RequestBody EKYCGetProspectETERequest request);

	@PostMapping(value = "${customer.ekyc.prospect.service.search.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EKYCProspectSearchProspectDataResponse> searchCcProspectCustomerData(
			@RequestHeader Map<String, String> headers, @RequestBody EKYCGetProspectETERequest request);

	@PostMapping(value = "${customer.ekyc.prospect.service.search.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EKYCProspectSearchProspectDataResponse> searchProspectDataByEngName(
			@RequestHeader Map<String, String> headers, @RequestBody EKYCGetProspectNameEngETERequest request);

	@PostMapping(value = "${customer.ekyc.prospect.service.search.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EKYCProspectSearchProspectDataResponse> searchProspectDataByThaiName(
			@RequestHeader Map<String, String> headers, @RequestBody EKYCGetProspectNameThaiETERequest request);

	@PostMapping(value = "${customer.ekyc.prospect.service.delete.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EYCProspectDeleteResponse> deleteProspectData(@RequestHeader Map<String, String> headers,
			@RequestBody EKYCDeleteProspectETERequest request);

	/**
	 * Calculate period between versions
	 * @deprecated
	 * This method is no longer acceptable to call service '/v3.0/internal/kyc/prospect/compare-prospect-biometric' after the release of 9.2.
	 * <p> Use {@link EKYCv2FeignClient#postV2ProspectBiometricsComparison(String, String, String, String, PostBiometricComparisonRequest)} instead.
	 *
	 * @param headers Map
	 * @param request EKYCVerifyBioMetricCustomerETERequest
	 * @return computed time
	 */
	@Deprecated(since = "r9.2", forRemoval = true)
	@PostMapping(value = "${customer.ekyc.prospect.service.verify.biometric.path}",
			consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8,
			produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EKYCVerifyBioMetricCustomerETEResponse> verfiyBioMetricData
	(@RequestHeader Map<String, String> headers, @RequestBody EKYCVerifyBioMetricCustomerETERequest request);

}
