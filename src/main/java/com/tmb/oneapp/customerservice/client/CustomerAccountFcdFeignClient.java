package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.CustomerAccountETERequest;
import com.tmb.oneapp.customerservice.model.CustomerAccountETEResponse;
import com.tmb.oneapp.customerservice.model.CustomerAccountNoETERequest;
import com.tmb.oneapp.customerservice.model.CustomerAccountNoETEResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * interface for Customer EKYC Create Account
 *
 */
@FeignClient(name = "${customer.fcd.account.service.name}", url = "${customer.fcd.account.service.url}")
public interface CustomerAccountFcdFeignClient {

	@PostMapping(value = "${customer.fcd.account.service.create.path}",
			consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<CustomerAccountETEResponse> createFcdAccount(@RequestHeader HttpHeaders headers,
																@RequestBody CustomerAccountETERequest request);

}
