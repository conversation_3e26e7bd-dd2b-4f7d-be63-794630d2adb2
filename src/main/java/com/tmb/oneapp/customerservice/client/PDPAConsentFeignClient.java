package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.pdpa.PDPAConsentQuery;
import com.tmb.oneapp.customerservice.model.pdpa.PDPAGetConsentResponse;
import com.tmb.oneapp.customerservice.model.pdpa.PDPAUpdateConsentRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${ete.pdpa.consent.service.name}", url = "${ete.pdpa.consent.url}", qualifiers = {"real-pdpa-content-service"})
public interface PDPAConsentFeignClient {

    @PostMapping(value = "${ete.pdpa.consent.get.endpoint}", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<PDPAGetConsentResponse> getPDPAConsent(
            @RequestHeader(value = CustomerServiceConstant.HEADER_SERVICE_NAME) String serviceName,
            @RequestHeader(value = CustomerServiceConstant.HEADER_REQUEST_UUID) String uuid,
            @RequestHeader(value = CustomerServiceConstant.HEADER_APP_ID) String appId,
            @RequestHeader(value = CustomerServiceConstant.HEADER_REQUEST_DATE_TIME) String reqDateTime,
            @RequestBody PDPAConsentQuery request
    );

    @PostMapping(value = "${ete.pdpa.consent.update.endpoint}", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<PDPAGetConsentResponse> updatePDPAConsent(
            @RequestHeader(value = CustomerServiceConstant.HEADER_SERVICE_NAME) String serviceName,
            @RequestHeader(value = CustomerServiceConstant.HEADER_REQUEST_UUID) String uuid,
            @RequestHeader(value = CustomerServiceConstant.HEADER_APP_ID) String appId,
            @RequestHeader(value = CustomerServiceConstant.HEADER_REQUEST_DATE_TIME) String reqDateTime,
            @RequestBody PDPAUpdateConsentRequest request
    );
}