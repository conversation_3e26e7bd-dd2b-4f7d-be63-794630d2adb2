package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.ExchangeRateResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import static com.tmb.oneapp.customerservice.constant.CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8;

/**
 * interface for getting exchange rate
 */
@FeignClient(name = "${tmb.exchange.rate.service.name}", url = "${tmb.exchange.rate.service.url}")
public interface ExchangeRateFeignClient {

    /**
     * endpoint to get exchange rate
     *
     * @param headers header values
     * @return exchange rate data
     */
    @GetMapping(value = "${tmb.exchange.rate.service.path}", consumes = CONTENT_TYPE_VALUE_UTF_8, produces = CONTENT_TYPE_VALUE_UTF_8)
    ResponseEntity<ExchangeRateResponse> getOdsExchangeRate(@RequestHeader HttpHeaders headers);

}
