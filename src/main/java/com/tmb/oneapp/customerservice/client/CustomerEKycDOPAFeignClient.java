package com.tmb.oneapp.customerservice.client;

import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Retryable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.CustomerEKYCDOPARequest;
import com.tmb.oneapp.customerservice.model.CustomerEKYCDopaResponse;

/**
 * interface for validation for customer details from DOPA Service
 */
@FeignClient(name = "${customer.ekyc.dopa.service.name}", url = "${customer.ekyc.dopa.service.url}")
public interface CustomerEKycDOPAFeignClient {

	@Retryable(maxAttempts = CustomerServiceConstant.EKYC_DOPA_CALL_RETRYATTEMPTS)
	@PostMapping(value = "${customer.ekyc.dopa.service.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<CustomerEKYCDopaResponse> getCustomerProfileValidation(@RequestHeader Map<String, String> headers,
			@RequestBody CustomerEKYCDOPARequest request);

}
