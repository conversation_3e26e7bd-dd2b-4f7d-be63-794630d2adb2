package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.PostGetDipchipResponse;
import com.tmb.oneapp.customerservice.model.PostGetDipchipRequest;
import com.tmb.oneapp.customerservice.model.PostBiometricUpdateConsentECResponse;
import com.tmb.oneapp.customerservice.model.PostBiometricGetConsentResponseV2;
import com.tmb.oneapp.customerservice.model.EteQueryTypeValueRequest;
import com.tmb.oneapp.customerservice.model.EkycBiometricUpdateConsentECRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * Interface to get EKYC NDID customer data
 */
@FeignClient(name = "${customer.ekyc.ndid.service.dipchip.name}", url = "${customer.ete.ekyc.dipchip.service.url}")
public interface CustomerEkycDipchipFeignClient {


    /**
     * endpoint to get dipchip data
     *
     * @return EkycBiometricGetResponseFormat biometric get data
     */
    @PostMapping(value = "${customer.ekyc.service.path.ekycService.path}", consumes = "application/json;charset=UTF-8", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<PostGetDipchipResponse> postEkycServiceGetDipchip(
            @RequestHeader HttpHeaders headers,
            @RequestBody PostGetDipchipRequest request);

        
    /**
     * endpoint to update biometric consent status
     *
     * @return PostBiometricGetConsentResponse biometric consent data
     */
    @PostMapping(value = "${customer.ete.ekyc.updateConsentEC.service.path}", consumes = "application/json;charset=UTF-8", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<PostBiometricUpdateConsentECResponse> postBiometricUpdateECConsent(
            @RequestHeader HttpHeaders headers,
            @RequestBody EkycBiometricUpdateConsentECRequest request);

    /**
     * endpoint to retrieve biometric consent status
     *
     * @return PostBiometricGetConsentResponse biometric consent data
     */
    @PostMapping(value = "${customer.ete.ekyc.getConsent.service.path}", consumes = "application/json;charset=UTF-8", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<PostBiometricGetConsentResponseV2> postBiometricGetConsent(
            @RequestHeader HttpHeaders headers,
            @RequestBody EteQueryTypeValueRequest request);
}
