package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.CustomerDetailsByAccountNoResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * FetchAccountGetOwnersService interface consume customer details from core banking
 * service
 */
@FeignClient(name = "${customer.account.service.name}", url = "${customer.account.service.url}")
public interface FetchAccountGetOwnersFeignClient {

	/**
	 * @param reqTemplate
	 * @param headers     callCustomersService method consume customer details from
	 *                    core banking
	 */
	@PostMapping(value = "${customer.account.service.path}", consumes = "application/json", produces = "application/json")
	public ResponseEntity<CustomerDetailsByAccountNoResponse> callFetchAccountGetOwnersService(@RequestHeader Map<String, String> headers,
																							   @RequestBody String reqTemplate);
}
