package com.tmb.oneapp.customerservice.client;

import java.util.Map;

import com.tmb.oneapp.customerservice.client.ekyc.EKYCv2FeignClient;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.EKYCGetImageETERequest;
import com.tmb.oneapp.customerservice.model.EKYCGetImageETEResonse;

/**
 * interface to connect ETE Customer prospect Service
 */
@FeignClient(name = "${customer.ekyc.prospect.image.service.name}", url = "${customer.ekyc.prospect.image.service.url}")
public interface CustomerEKYCGetImageClient {

	/**
	 * Calculate period between versions
	 * @deprecated
	 * This method is no longer acceptable to call service "/v3.0/internal/kyc/prospect/get-image" after the release of 9.2.
	 * <p> Use {@link EKYCv2FeignClient#getV2ProspectBiometrics(String, String, String, String, String, String, String, String)} instead.
	 *
	 * @param headers Map
	 * @param request EKYCGetImageETERequest
	 * @return computed time
	 */
	@Deprecated(since = "r9.2", forRemoval = false)
	@PostMapping(value = "${customer.ekyc.prospect.service.get.image.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EKYCGetImageETEResonse> getImageData(@RequestHeader Map<String, String> headers,
			@RequestBody EKYCGetImageETERequest request);
}
