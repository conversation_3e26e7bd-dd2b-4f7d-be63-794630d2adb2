package com.tmb.oneapp.customerservice.client;

import java.util.Map;

import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.tmb.oneapp.customerservice.model.PromptPayValidationETEResponse;

/**
 * interface for get promptpay validation verification history
 */
@FeignClient(name = "${promptpay.validation.data.name}", url = "${promptpay.validation.data.url}")
public interface PromptPayValidationClient {

    /**
     * endpoint to get promptpay validation verification history
     *
     */
    @CircuitBreaker(name = "creditTransferPromptpayValidation")
    @PostMapping(value = "${promptpay.validation.data.path}", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<PromptPayValidationETEResponse> getPromptPayValidation(@RequestHeader Map<String, String> headrs,
			@RequestBody String jsonObb);

}