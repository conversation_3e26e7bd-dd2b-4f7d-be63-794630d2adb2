package com.tmb.oneapp.customerservice.client;

import com.tmb.common.logger.LogAround;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@FeignClient(name = "${tmb.service.get-base64-favorite-image.name}", url = "${tmb.service.get-base64-favorite-image.url}")
public interface TMBGetFavoriteImageServiceClient {
    @LogAround
    @PostMapping(value = "${tmb.service.get-base64-favorite-image.path}")
    ResponseEntity<String> getFavoriteImage(@RequestHeader Map<String, String> headers, @RequestParam(value = "crmid") String crmId, @RequestParam(value = "personalizedid") String favoriteId);
}