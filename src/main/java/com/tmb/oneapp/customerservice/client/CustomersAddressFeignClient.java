package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.CustomersProfileETEResponse;
import com.tmb.oneapp.customerservice.model.EteQueryRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * CustomersAddressFeignClient interface consume customer address from ETE service
 */
@FeignClient(name = "${customer.address.service.name}", url = "${customer.address.service.url}")
public interface CustomersAddressFeignClient {

    /**
     * @param headers               the header
     * @param eteQueryRequest the customer search request
     */
    @PostMapping(value = "${customer.address.service.path}", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<CustomersProfileETEResponse> getCustomerAddress(@RequestHeader HttpHeaders headers,
                                                                   @RequestBody EteQueryRequest eteQueryRequest);
}
