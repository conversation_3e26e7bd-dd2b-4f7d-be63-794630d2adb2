package com.tmb.oneapp.customerservice.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * interface for get customer touch profile Image
 *
 */
@FeignClient(name = "${customer.touch.profile.image.service.name}", url = "${customer.touch.profile.image.url}")
public interface CustomerServiceFeignClient {

	/**
	 * endpoint to get customer touch profile Image
	 *
	 * @param crmId
	 * @return
	 */
	@PostMapping(value = "/services/TMBMIBService0/getBase64ProfilePicture", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<String> getBase64TouchProfilePicture(@RequestParam("crmid") String crmId);

}
