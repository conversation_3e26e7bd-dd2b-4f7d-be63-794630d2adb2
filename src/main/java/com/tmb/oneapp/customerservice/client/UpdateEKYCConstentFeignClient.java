package com.tmb.oneapp.customerservice.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.UpdateConstentETERequest;

@FeignClient(name = "${customer.ekyc.updateConstent.service.name}", url = "${customer.ekyc.updateConstent.service.url}")
public interface UpdateEKYCConstentFeignClient {
	@PostMapping(value = "${customer.ekyc.updateConstent.service.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<String> updateConstentEKYC(@RequestHeader HttpHeaders headers,
			@RequestBody UpdateConstentETERequest request);
}
