package com.tmb.oneapp.customerservice.client;

import jakarta.validation.Valid;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.CacheData;

/**
 * interface responsible for calling cache-service
 */
@FeignClient(name = "${cache.name}", url = "${cache.endpoint}")
public interface CacheFeignClient {

	@PutMapping(value = "/apis/cache/generate/{key}/{digits}", produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public ResponseEntity<TmbOneServiceResponse<String>> generateReference(
			@RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) final String correlationId,
			@Valid @PathVariable("key") String activityid, @Valid @PathVariable("digits") final String digits);
	
	@PostMapping(value = "/apis/cache/", produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public ResponseEntity<TmbOneServiceResponse<String>> postData(
			@RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) final String correlationId,
			@RequestBody CacheData request);
	
	
	@GetMapping(value = "/apis/cache/{key}", produces = "application/json")
	@ResponseBody
	public ResponseEntity<TmbOneServiceResponse<String>> getdata(@Valid @PathVariable("key") String cacheKey,
			@RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) final String correlationId);

	@DeleteMapping(value = "/apis/cache/{key}", produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public ResponseEntity<TmbOneServiceResponse<String>> deleteData(
			@RequestHeader(CustomerServiceConstant.HEADER_CORRELATION_ID) final String correlationId,
			@Valid @PathVariable("key") String activityid);

}