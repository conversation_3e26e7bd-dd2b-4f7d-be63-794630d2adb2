package com.tmb.oneapp.customerservice.client;

import java.util.List;

import com.tmb.oneapp.customerservice.model.BankInfo;
import com.tmb.oneapp.customerservice.model.BillerTopUpDetailResponse;
import com.tmb.oneapp.customerservice.model.CategoryDetail;
import com.tmb.oneapp.customerservice.model.LicenseDetail;
import com.tmb.oneapp.customerservice.model.CommonAddressList;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.PathVariable;
import com.tmb.common.model.CommonData;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;

/**
 * Feign Client to connect Common Service
 * 
 *
 */
@FeignClient(name = "${common.service.name}", url = "${common.service.endpoint}")
public interface CommonFeignClient {

	@GetMapping(value = "${feign.common.get.config}", produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	ResponseEntity<TmbOneServiceResponse<List<CommonData>>> getCommonConfig(
			@RequestParam(CustomerServiceConstant.TRANSFER_COMMON_CONFIG_FEIGN_REQUEST_PARAM_SEARCH) String searchType,
			@RequestHeader HttpHeaders headers);

	@GetMapping(value = "${feign.common.get.address.list}", produces = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8)
	@ResponseBody
	ResponseEntity<TmbOneServiceResponse<List<CommonAddressList>>> getAddressList(@RequestHeader HttpHeaders headers);

	@GetMapping(value = "${feign.common.get.bank.info.endpoint}", consumes = MediaType.APPLICATION_JSON_VALUE)
	TmbOneServiceResponse<List<BankInfo>> bankInfoList();

	@GetMapping(value = "${feign.common.get.biller.pay.endpoint}", consumes = MediaType.APPLICATION_JSON_VALUE)
	TmbOneServiceResponse<BillerTopUpDetailResponse> getBillerBillPayDetail(
			@RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
			@PathVariable("COMPCODE") String compCode);

	@GetMapping(value = "${feign.common.get.ic.license}", consumes = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<TmbOneServiceResponse<List<LicenseDetail>>> fetchIcLicense();

	@GetMapping(value = "${feign.common.get.category.endpoint}", consumes = MediaType.APPLICATION_JSON_VALUE)
	TmbOneServiceResponse<List<CategoryDetail>> getCategoryDetail(@RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId);

	@GetMapping(value = "${feign.common.get.common.config}", produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	ResponseEntity<TmbOneServiceResponse<List<CommonData>>> getCommonConfigModule(
			@RequestParam(CustomerServiceConstant.TRANSFER_COMMON_CONFIG_FEIGN_REQUEST_PARAM_SEARCH) String searchType,
			@RequestHeader HttpHeaders headers);
}
