package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.ETEResponse;
import com.tmb.oneapp.customerservice.model.PostGetStatementAsRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * Interface for dstatement
 */
@FeignClient(name = "${customer.ekyc.ndid.as.service.name}", url = "${customer.ekyc.ndid.as.service.url}")
public interface CustomerEkycASFeignClient {

    /**
     * endpoint to send dstatement data to RP
     */
    @PostMapping(value = "${customer.ekyc.ndid.as.getStatementAs.service.path}", consumes = "application/json;charset=utf-8", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<ETEResponse> postGetStatementAs(
            @RequestHeader HttpHeaders headers,
            @RequestBody PostGetStatementAsRequest request);


}
