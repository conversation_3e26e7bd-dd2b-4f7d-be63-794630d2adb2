package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.client.ekyc.EKYCv2FeignClient;
import com.tmb.oneapp.customerservice.model.FetchCustomerCitizenIDETEResponse;
import com.tmb.oneapp.customerservice.model.FetchCustomerCitizenIDETERequest;
import com.tmb.oneapp.customerservice.model.EkycBiometricGetResponseFormat;
import com.tmb.oneapp.customerservice.model.EkycBiometricCompareRequestFormat;
import com.tmb.oneapp.customerservice.model.EkycBiometricGetRequestFormat;
import com.tmb.oneapp.customerservice.model.EkycBiometricCompareResponseFormat;
import com.tmb.oneapp.customerservice.model.ndid.PostBiometricComparisonRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * interface for get customer details based on citizen image
 */
@FeignClient(name = "${customer.ekyc.service.name}", url = "${customer.ekyc.service.url}")
public interface CustomerEKycFeignClient {

	@PostMapping(value = "${customer.ekyc.service.path}", 
			consumes = "application/json;charset=UTF-8", 
			produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<FetchCustomerCitizenIDETEResponse> getCitizenID(
			@RequestBody FetchCustomerCitizenIDETERequest request);


	/**
	 * Calculate period between versions
	 * @deprecated
	 * This method is no longer acceptable to call service '/v3.0/internal/kyc/biometric/get-biometric' after the release of 9.2.
	 * <p> Use {@link EKYCv2FeignClient#getV2FacesBiometricsComparison(String, String, String, String, String, String)} instead.
	 *
	 * @param request EkycBiometricGetRequestFormat
	 * @return computed time
	 */
	@Deprecated(since = "r9.2", forRemoval = true)
	@PostMapping(value = "${customer.ekyc.service.path.biometricGet.path}", 
			consumes = "application/json;charset=UTF-8",
			produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EkycBiometricGetResponseFormat> fetchBiometricGet(
			@RequestBody EkycBiometricGetRequestFormat request);

	/**
	 * Calculate period between versions
	 * @deprecated
	 * This method is no longer acceptable to call service '/biometric/verification/face/compare' after the release of 9.2.
	 * <p> Use {@link EKYCv2FeignClient#postV2FacesBiometricsComparison(String, String, String, String, PostBiometricComparisonRequest)} instead.
	 *
	 * @param request EkycBiometricGetRequestFormat
	 * @return computed time
	 */
	@Deprecated(since = "r9.2", forRemoval = true)
	@PostMapping(value = "${customer.ekyc.service.path.biometricCompare.path}", 
			consumes = "application/json;charset=UTF-8", 
			produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EkycBiometricCompareResponseFormat> fetchBiometricCompare(
			@RequestBody EkycBiometricCompareRequestFormat request);


}