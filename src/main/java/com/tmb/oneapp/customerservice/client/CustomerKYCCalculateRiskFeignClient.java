package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.CalRiskPersonalRequest;
import com.tmb.oneapp.customerservice.model.ekycriskcalculate.RiskCalculateResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "${customer.kyc.calculate.risk.service.name}", url = "${customer.kyc.calculate.risk.service.url}")
public interface CustomerKYCCalculateRiskFeignClient {
    @PostMapping(value = "${customer.kyc.calculate.risk.service.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8)
    ResponseEntity<RiskCalculateResponse> getRiskPersonal(@RequestBody CalRiskPersonalRequest reqBody);
}
