package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.BZBEntryPointRequest;
import com.tmb.oneapp.customerservice.model.BZBEntryPointResponse;
import com.tmb.oneapp.customerservice.model.BZBRedeemHistoryRequest;
import com.tmb.oneapp.customerservice.model.BZBRedeemHistoryResponse;
import feign.Logger;
import feign.codec.Encoder;
import feign.form.spring.SpringFormEncoder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${bzb.service.name}", url = "${bzb.url}", configuration = BZBFeignClient.Configuration.class)
public interface BZBFeignClient {

    @PostMapping(value = "${bzb.token.endpoint}", consumes = "application/x-www-form-urlencoded", produces = "application/json")
    BZBEntryPointResponse getEntryPoint(@RequestHeader HttpHeaders headers,
                                        @RequestBody BZBEntryPointRequest request);

    @PostMapping(value = "${bzb.redeem.cashback}", consumes = "application/x-www-form-urlencoded", produces = "application/json")
    BZBRedeemHistoryResponse createRedeemHistory(@RequestHeader HttpHeaders headers, @RequestBody BZBRedeemHistoryRequest request);

    class Configuration {
        @Bean
        Encoder feignFormEncoder(ObjectFactory<HttpMessageConverters> converters) {
            return new SpringFormEncoder(new SpringEncoder(converters));
        }

        @Bean
        Logger.Level feignLoggerLevel() {
            return Logger.Level.FULL;
        }
    }
}