package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.EkycIdpConsentRequestFormat;
import com.tmb.oneapp.customerservice.model.EkycInquireNdidRequestBodyRequestFormat;
import com.tmb.oneapp.customerservice.model.EkycInquireNdidRequestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * Interface to get EKYC NDID customer data
 */
@FeignClient(name = "${customer.ekyc.ndid.service.name.v2}", url = "${customer.ekyc.ndid.service.url.v2}")
public interface CustomerEkycNDIDFeignClient2 {
    /**
     * endpoint to get customer Ekyc Inquire NDID Incoming List Request
     *
     * @param serviceName                             serviceName
     * @param requestUid                              requestUid
     * @param requestAppId                            requestAppId
     * @param requestDateTime                         requestDateTime
     * @param ekycInquireNdidRequestBodyRequestFormat EkycInquireNdidRequestBodyRequestFormat
     * @return ResponseEntity<List < EkycInquireNdidRequestResponseFormat2>>
     */
    @PostMapping(value = "${customer.ekyc.ndid.incoming.list.v2}", consumes = "application/json;charset=UTF-8", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<EkycInquireNdidRequestResponse> getEkycInquireNdidRequestResponse2(
            @RequestHeader(value = CustomerServiceConstant.SERVICE_NAME) String serviceName,
            @RequestHeader(value = CustomerServiceConstant.REQUEST_UID) String requestUid,
            @RequestHeader(value = CustomerServiceConstant.REQUEST_APP_ID) String requestAppId,
            @RequestHeader(value = CustomerServiceConstant.REQUEST_DATE_TIME) String requestDateTime,
            @RequestBody EkycInquireNdidRequestBodyRequestFormat ekycInquireNdidRequestBodyRequestFormat
    );

    /**
     * endpoint to PUT customer Ekyc Idp Consent
     *
     * @param serviceName     serviceName
     * @param requestUid      requestUid
     * @param requestAppId    requestAppId
     * @param requestDateTime requestDateTime
     * @return ResponseEntity<EkycIdpConsentResponseFormat>
     */
    @PostMapping(value = "${customer.ekyc.ndid.consent.v2}", consumes = "application/json;charset=UTF-8", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<Object> putEkycIdpConsent2(
            @RequestHeader(value = CustomerServiceConstant.SERVICE_NAME) String serviceName,
            @RequestHeader(value = CustomerServiceConstant.REQUEST_UID) String requestUid,
            @RequestHeader(value = CustomerServiceConstant.REQUEST_APP_ID) String requestAppId,
            @RequestHeader(value = CustomerServiceConstant.REQUEST_DATE_TIME) String requestDateTime,
            @RequestBody EkycIdpConsentRequestFormat ekycIdpConsentRequestFormat
    );

}
