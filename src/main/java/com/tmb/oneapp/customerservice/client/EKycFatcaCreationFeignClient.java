package com.tmb.oneapp.customerservice.client;

import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.CustomerGetFatcaResponse;
import com.tmb.oneapp.customerservice.model.EKYCCreateFatcaCustomerRequest;
import com.tmb.oneapp.customerservice.model.EKYCCreateFatcaCustomerResponse;
import com.tmb.oneapp.customerservice.model.EteQueryRequest;
/**
 * interface for validation for customer details from DOPA Service
 */
@FeignClient(name = "${ekyc.fatca.customers.service.name}", url = "${ekyc.fatca.create.service.url}")
public interface EKycFatcaCreationFeignClient {

	@PostMapping(value = "${ekyc.fatca.create.service.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EKYCCreateFatcaCustomerResponse> createFatcaAnswer(@RequestHeader Map<String, String> headers,
			@RequestBody EKYCCreateFatcaCustomerRequest answers);
	
	@PostMapping(value = "${ekyc.fatca.get.service.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<CustomerGetFatcaResponse> getCustomerFatca(@RequestHeader Map<String, String> headers,
			@RequestBody EteQueryRequest request);

}
