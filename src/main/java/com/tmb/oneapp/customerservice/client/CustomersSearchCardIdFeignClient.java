package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.creditcard.CardIdDetailResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * CustomersSearchFeignClient interface consume customer details from core banking
 * service
 */
@FeignClient(name = "${customer.creditcard.service.url}", url = "${customer.creditcard.service.url}")
public interface CustomersSearchCardIdFeignClient {

	/**
	 * @param cardId
	 * @param headers     callCustomersService method consume customer details from
	 *                    core banking
	 */
	@GetMapping(value = "${customer.creditcard.service.path}", consumes = "application/json", produces = "application/json")
	public ResponseEntity<CardIdDetailResponse> callCustomerCreditCardService(@RequestHeader Map<String, String> headers,
																			  @PathVariable("CARD_ID") String cardId);

}
