package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.PostNDIDGetConsentStatusRequest;
import com.tmb.oneapp.customerservice.model.PostNDIDGetConsentStatusResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${ndid.consent-status.service.name}", url = "${ndid.consent-status.service.url}")
public interface CustomerEKYCNdidConsentStatusFeignClient {

    /**
     * endpoint to retrieve NDID accept conditions
     *
     * @return EkycNDIDConditionResponse NDID accept status
     */
    @PostMapping(value = "${ndid.consent-status.service.path}", consumes = "application/json;charset=UTF-8", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<PostNDIDGetConsentStatusResponse> postGetConsentStatus(
            @RequestHeader HttpHeaders headers,
            @RequestBody PostNDIDGetConsentStatusRequest request);
}