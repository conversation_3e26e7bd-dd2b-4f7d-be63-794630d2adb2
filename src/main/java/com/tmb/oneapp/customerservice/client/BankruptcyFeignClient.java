package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.model.BankruptcyStatusResponse;
import com.tmb.oneapp.customerservice.model.EteBaseResponse;
import com.tmb.oneapp.customerservice.model.EteBaseQueryRequest;
import com.tmb.oneapp.customerservice.model.BankruptcyStatusRequest;
import com.tmb.oneapp.customerservice.model.BankruptcyJudgementStatusResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

@FeignClient(name = "${ete.bankruptcy.name}", url = "${ete.bankruptcy.url}")
public interface BankruptcyFeignClient {

    @PostMapping(value = "/v1.0/internal/kyc/citizen/status/get-bankrupt-status", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<EteBaseResponse<BankruptcyStatusResponse>> getStatusByCitizenId(
            @RequestHeader Map<String, Object> header,
            @RequestBody EteBaseQueryRequest<BankruptcyStatusRequest> request
    );

    @PostMapping(value = "/v1.0/internal/kyc/passport-no/status/get-bankrupt-status", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<EteBaseResponse<BankruptcyStatusResponse>> getStatusByPassport(
            @RequestHeader Map<String, Object> header,
            @RequestBody EteBaseQueryRequest<BankruptcyStatusRequest> request
    );

    @PostMapping(value = "/v1.0/internal/kyc/juristic-no/status/get-bankrupt-status", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<EteBaseResponse<BankruptcyStatusResponse>> getStatusByJuristic(
            @RequestHeader Map<String, Object> header,
            @RequestBody EteBaseQueryRequest<BankruptcyStatusRequest> request
    );

    @PostMapping(value = "/v1.0/internal/kyc/status/get-bankrupt-status-judgement", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<EteBaseResponse<BankruptcyJudgementStatusResponse>> getJudgementStatus(
            @RequestHeader Map<String, Object> header,
            @RequestBody EteBaseQueryRequest<BankruptcyStatusRequest> request
    );


}