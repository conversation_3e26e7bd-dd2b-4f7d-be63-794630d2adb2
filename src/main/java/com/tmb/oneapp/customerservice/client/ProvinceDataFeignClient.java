package com.tmb.oneapp.customerservice.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * ProvinceDataFeignClient interface consume province data from core banking
 * service
 */
@FeignClient(name = "${province.data.name}", url = "${province.data.url}")
public interface ProvinceDataFeignClient {

	/**
	 * @param headers     callProvinceDataService method consume province data from
	 *                    core banking
	 */
	@GetMapping(value = "${province.data.path}")
	public ResponseEntity<String> callProvinceDataService(@RequestHeader Map<String, String> headers, @RequestParam(value = "cl_type") String clType);

}
