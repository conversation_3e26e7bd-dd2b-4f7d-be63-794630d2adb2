package com.tmb.oneapp.customerservice.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.EKYCCalRiskETERequest;
import com.tmb.oneapp.customerservice.model.EKYCCalRiskETEResponse;

@FeignClient(name = "${customer.ekyc.calRisk.service.name}", url = "${customer.ekyc.calRisk.service.url}")
public interface CustomerEKYCCalRiskFeignClient {
	@PostMapping(value = "${customer.ekyc.calRisk.service.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8)
	ResponseEntity<EKYCCalRiskETEResponse> callCalculateRisk(@RequestBody EKYCCalRiskETERequest reqBody);
}
