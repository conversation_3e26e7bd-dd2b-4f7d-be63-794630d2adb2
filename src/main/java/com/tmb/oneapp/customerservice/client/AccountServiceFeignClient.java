package com.tmb.oneapp.customerservice.client;

import com.tmb.common.model.CommonData;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.AccountInfoResponse;
import com.tmb.oneapp.customerservice.model.OpenAccountBranchPrefix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * Feign Client to connect Accounts Service
 */
@FeignClient(name = "${accounts.service.name}", url = "${accounts.service.url}")
public interface AccountServiceFeignClient {

	@GetMapping(value = "${accounts.service.endpoint.get.info}", consumes = MediaType.APPLICATION_JSON_VALUE)
	TmbServiceResponse<AccountInfoResponse> getAccountInfo(
			@RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
			@RequestHeader(name = CustomerServiceConstant.HEADER_ACCOUNT_NUMBER) String accountNumber,
			@RequestHeader(name = CustomerServiceConstant.HEADER_ACCOUNT_TYPE) String accountType);

	@GetMapping(value = "${accounts.service.endpoint.get.open.account}", produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<TmbOneServiceResponse<CommonData>> getOpenAccountConfiguration(
			@RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId);

	@GetMapping(value = "/v1/accounts-service/open-account/get-branch-prefix")
	ResponseEntity<TmbServiceResponse<OpenAccountBranchPrefix>> getOpenAccountBranchPrefix(
			@RequestHeader(value = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId);

	@GetMapping(value = "/v1/accounts-service/open-account/get-next-branch-prefix/{currentBranch}")
	ResponseEntity<TmbServiceResponse<OpenAccountBranchPrefix.BranchPrefix>> getOpenAccountNextBranchPrefix(
			@RequestHeader(value = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
			@PathVariable("currentBranch") String currentBranch);
}
