package com.tmb.oneapp.customerservice.client;

import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.UpdateEStatement;

/**
 * CustomersEStatementFeignClient interface consume customer e-statement from
 * core banking service
 */
@FeignClient(name = "${customer.eStatement.service.name}", url = "${customer.eStatement.service.url}")
public interface CustomersEStatementFeignClient {

	/**
	 * @param reqTemplate
	 * @param headers     callCustomersServiceGetEStatement method consume customer
	 *                    e-statement from core banking
	 */
	@PostMapping(value = "${customer.eStatement.service.estatement.get.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<String> callCustomersServiceGetEStatement(@RequestHeader Map<String, String> headers,
			@RequestBody String reqTemplate);
	
	@PostMapping(value = "${customer.eStatement.service.estatement.update.path}", consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8, produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<String> updateEStatment(@RequestHeader Map<String, String> headersReqMap,
			@RequestBody UpdateEStatement requestUpdate);

}
