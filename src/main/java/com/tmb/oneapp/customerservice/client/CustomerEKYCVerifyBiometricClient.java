package com.tmb.oneapp.customerservice.client;

import java.util.Map;

import com.tmb.oneapp.customerservice.client.ekyc.EKYCv2FeignClient;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.EKYCVerifyBioMetricCustomerETERequest;
import com.tmb.oneapp.customerservice.model.EKYCVerifyBioMetricCustomerETEResponse;

@FeignClient(name = "${customer.ekyc.prospect.biometric.service.name}", url = "${customer.ekyc.prospect.biometric.service.url}")
public interface CustomerEKYCVerifyBiometricClient {

	/**
	 * Calculate period between versions
	 * @deprecated
	 * This method is no longer acceptable to call service '/v3.0/internal/kyc/biometric/verify-biometric' after the release of 9.2.
	 * <p> Use {@link EKYCv2FeignClient#getV2FacesBiometricsComparison(String, String, String, String, String, String)} instead.
	 *
	 * @param headers Map
	 * @param request EKYCVerifyBioMetricCustomerETERequest
	 * @return computed time
	 */
	@Deprecated(since = "r9.2", forRemoval = true)
	@PostMapping(value = "${customer.ekyc.prospect.biometric.service.path}",
			consumes = CustomerServiceConstant.CONTENT_TYPE_VALUE_UTF_8,
			produces = MediaType.APPLICATION_JSON_VALUE)
	ResponseEntity<EKYCVerifyBioMetricCustomerETEResponse> verfiyBioMetricData
	(@RequestHeader Map<String, String> headers, @RequestBody EKYCVerifyBioMetricCustomerETERequest request);

}