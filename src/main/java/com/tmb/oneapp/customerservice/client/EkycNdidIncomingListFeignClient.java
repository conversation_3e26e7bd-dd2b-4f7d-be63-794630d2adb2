package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.EkycInquireNdidRequestBodyRequestFormat;
import com.tmb.oneapp.customerservice.model.EkycInquireNdidRequestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${customer.ekyc.ndid.incoming.list.name}", url = "${customer.ekyc.ndid.incoming.list.url}")
public interface EkycNdidIncomingListFeignClient {
    /**
     * endpoint to get customer Ekyc Inquire NDID Incoming List Request
     *
     * @param serviceName                             serviceName
     * @param requestUid                              requestUid
     * @param requestAppId                            requestAppId
     * @param requestDateTime                         requestDateTime
     * @param acronym                                 acronym
     * @param ekycInquireNdidRequestBodyRequestFormat EkycInquireNdidRequestBodyRequestFormat
     * @return ResponseEntity<List < EkycInquireNdidRequestResponseFormat2>>
     */
    @PostMapping(value = "${customer.ekyc.ndid.incoming.list.v2}", consumes = "application/json;charset=UTF-8", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<EkycInquireNdidRequestResponse> getEkycInquireNdidRequestResponse2(
            @RequestHeader(value = CustomerServiceConstant.SERVICE_NAME) String serviceName,
            @RequestHeader(value = CustomerServiceConstant.REQUEST_UID) String requestUid,
            @RequestHeader(value = CustomerServiceConstant.REQUEST_APP_ID) String requestAppId,
            @RequestHeader(value = CustomerServiceConstant.REQUEST_DATE_TIME) String requestDateTime,
            @RequestHeader(value = CustomerServiceConstant.ACRONYM) String acronym,
            @RequestBody EkycInquireNdidRequestBodyRequestFormat ekycInquireNdidRequestBodyRequestFormat
    );
}
