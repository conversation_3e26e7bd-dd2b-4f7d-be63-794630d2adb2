package com.tmb.oneapp.customerservice.client;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.BillerTopUpDetailResponse;
import com.tmb.oneapp.customerservice.model.PromptPayValidationETEResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * Feign Client to connect Accounts Service
 */
@FeignClient(name = "payment-service-feign-client", url = "${feign.payment-service.url}")
public interface PaymentServiceFeignClient {

	@GetMapping(value = "/v1/payment-service/billers/details/comp-code", consumes = MediaType.APPLICATION_JSON_VALUE)
	TmbOneServiceResponse<BillerTopUpDetailResponse> getBillerBillPayDetail(
			@RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId,
			@RequestParam("compCode") String compCode);
	
}
