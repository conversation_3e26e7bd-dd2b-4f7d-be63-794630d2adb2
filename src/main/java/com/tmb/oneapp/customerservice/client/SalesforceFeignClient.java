package com.tmb.oneapp.customerservice.client;

import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.SalesforceEncryptedDataFormat;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * interface for get data from Salesforce
 */
@FeignClient(name = "${tmb.salesforce.name}", url = "${tmb.salesforce.url}")
public interface SalesforceFeignClient {
    /**
     * endpoint to get customer case status
     *
     * @param authorization
     * @param reqId
     * @param integrationSystem
     * @param map
     *
     * @return ResponseEntity<SalesforceEncryptedDataFormat>
     */
    @PostMapping(value = "${tmb.salesforce.pwa.path}")
    public ResponseEntity<SalesforceEncryptedDataFormat> fetchPwaData(@RequestHeader(value = CustomerServiceConstant.SALESFORCE_AUTHENTICATION) String authorization,
                                                                    @RequestHeader(value = CustomerServiceConstant.SALESFORCE_REQ_ID) String reqId,
                                                                    @RequestHeader(value = CustomerServiceConstant.SALESFORCE_INTEGRATION_SYSTEM) String integrationSystem,
                                                                    @RequestBody Map<String, String> map);
}
