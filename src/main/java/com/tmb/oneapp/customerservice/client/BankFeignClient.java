package com.tmb.oneapp.customerservice.client;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.BankInfo;
import com.tmb.oneapp.customerservice.model.CategoryDetail;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

@FeignClient(name = "${bank.service.name}", url = "${bank.service.endpoint}")
public interface BankFeignClient {
    @GetMapping(value = "${feign.bank.get.bank.info.endpoint}", consumes = MediaType.APPLICATION_JSON_VALUE)
    TmbOneServiceResponse<List<BankInfo>> bankInfoList();

    @GetMapping(value = "${feign.bank.get.category.endpoint}", consumes = MediaType.APPLICATION_JSON_VALUE)
    TmbOneServiceResponse<List<CategoryDetail>> getCategoryDetail(@RequestHeader(name = CustomerServiceConstant.HEADER_CORRELATION_ID) String correlationId);
}
