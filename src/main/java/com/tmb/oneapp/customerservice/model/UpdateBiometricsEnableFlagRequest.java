package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UpdateBiometricsEnableFlagRequest {

    @NotNull
    @NotEmpty
    @NotBlank
    @Schema(description = "biometricFlag", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("biometric_flag")
    private Integer biometricFlag;
}
