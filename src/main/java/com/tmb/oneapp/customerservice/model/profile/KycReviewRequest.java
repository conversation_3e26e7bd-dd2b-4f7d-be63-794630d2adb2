package com.tmb.oneapp.customerservice.model.profile;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class KycReviewRequest {
    private String rmId;

    private String redirectSuccess;

    private String redirectClose;

    private String redirectKeepSessionAlive;

    private String redirectLockAccessPin;

    private String redirectAccessPin;

    private String redirectOpenUrl;

    private String documentType;

    private String channel;

    private String customerType;

    private String state;

    private String product;
}
