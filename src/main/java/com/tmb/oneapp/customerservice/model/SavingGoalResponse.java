package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SavingGoalResponse {
    private String accountId;
    private String holdId;
    private String transactionsSeqnum;
    private String transactionsAmounts;
    private String goalName;
    private String category;
    private String saveAmount;
    private String goalAmount;
    private String percentageOfProcess;
    private String scheduleAmount;
    private String scheduleType;
    private String startDate;
    private String status;

    private String scheduleRefId;
    private String itemId;
    private String transactionType;
    private String accountType;
    private String productCode;
    private String noFixedAccountNumber;

}
