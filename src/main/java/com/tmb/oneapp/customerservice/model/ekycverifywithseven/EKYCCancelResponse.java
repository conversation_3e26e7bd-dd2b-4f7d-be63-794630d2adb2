package com.tmb.oneapp.customerservice.model.ekycverifywithseven;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.customerservice.model.EKYCProspectETEResponseStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EKYCCancelResponse {
    private EKYCProspectETEResponseStatus status;
    private NDIDCancelResult ndidResult;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class NDIDCancelResult {
        private String rquid;
        private String status;
    }
}
