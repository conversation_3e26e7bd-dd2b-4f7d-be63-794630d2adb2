package com.tmb.oneapp.customerservice.model.ekycverifybiometric;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tmb.oneapp.customerservice.model.EkycBiometricResponseBiometric;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class EkycVerifyBiometricRequestBiometric extends EkycBiometricResponseBiometric {
    @NotNull
    @Schema(description = "Length", example = "17453", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("length")
    private String length;
}