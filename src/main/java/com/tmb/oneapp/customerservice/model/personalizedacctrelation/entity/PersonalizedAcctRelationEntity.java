package com.tmb.oneapp.customerservice.model.personalizedacctrelation.entity;

import com.tmb.oneapp.customerservice.model.personalizedinformation.entity.PersonalizedInformation;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.util.Date;

@Getter
@Setter
@ToString
@Entity
@Table(name = "personalized_acct_relation", schema = "CRMADM")
@IdClass(PersonalizedAcctRelationCompositeKey.class)
public class PersonalizedAcctRelationEntity {
    @Id
    @Column(name = "crm_id")
    private String crmId;

    @ManyToOne
    @JoinColumn(name = "personalized_id", referencedColumnName = "personalized_id")
    private PersonalizedInformation personalizedInformation;

    @Id
    @Column(name = "personalized_acct_id")
    private String personalizedAcctId;

    @Id
    @Column(name = "bank_cd")
    private String bankCd;

    @Column(name = "personalized_acct_status")
    private String personalizedAcctStatus;

    @Column(name = "acct_favorite_flag")
    private String acctFavoriteFlag;

    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_date")
    private Date createDate;

    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_date")
    private Date updateDate;

    @Column(name = "personalized_acct_name")
    private String personalizedAcctName;

    @Column(name = "joint_flag")
    private String jointFlag;

    @Id
    @Column(name = "app_id")
    private String appId;

    @Column(name = "PERSONALIZED_ACCT_NICKNAME")
    private String personalizedAcctNickname;

    @Column(name = "HEART_FLAG")
    private String heartFlag;

    @Column(name = "SWITCH_TO_ALLFREE")
    private String switchToAllfree;

    @Temporal(TemporalType.DATE)
    @Column(name = "SWITCH_DATE")
    private Date switchDate;

    @Column(name = "PERSONALIZED_DISPLAY_ACCT_STATUS")
    private String personalizedDisplayAcctStatus;

    @Column(name = "PERSONALIZED_ACCT_SORT_ORDER")
    private Integer personalizedAcctSortOrder;

    @Column(name = "VIEW_ONLY_FLAG")
    private String viewOnlyFlag;

}
