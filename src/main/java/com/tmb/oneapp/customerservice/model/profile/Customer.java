package com.tmb.oneapp.customerservice.model.profile;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "profile", "kyc", "additional_kyc", "source_of_incomes", "consents", "addresses", "phones" })
@Data
public class Customer {

	@JsonProperty("profile")
	public Profile profile;
	@JsonProperty("kyc")
	public Kyc kyc;
	@JsonProperty("additional_kyc")
	public AdditionalKyc additionalKyc;
	@JsonProperty("source_of_incomes")
	public List<SourceOfIncome> sourceOfIncomes = null;
	@JsonProperty("consents")
	public List<Consent> consents = null;
	@JsonProperty("addresses")
	public List<Address> addresses = null;
	@JsonProperty("phones")
	public List<Phone> phones = null;

}