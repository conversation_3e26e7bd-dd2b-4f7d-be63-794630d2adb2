package com.tmb.oneapp.customerservice.model.pdpa;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PDPAGetConsentCustomer {
    private PDPAGetConsentProfile profile;
    private List<PDPAGetConsentDetail> consents;
}
