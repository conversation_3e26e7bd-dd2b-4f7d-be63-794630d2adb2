package com.tmb.oneapp.customerservice.model.commonfr;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Setter;
import lombok.Getter;
import jakarta.validation.constraints.NotNull;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CommonFRUpdateBlockRequest {

    @NotNull(message = "reset flag must not be null")
    private Boolean resetFlag;
}
