package com.tmb.oneapp.customerservice.model.rosccustomerprofile.search;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.tmb.oneapp.customerservice.model.rosccustomerprofile.RoscCustomerProfile;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ResponseData {

    @JacksonXmlProperty(localName = "header")
    private Header header;

    @JacksonXmlProperty(localName = "body")
    private RoscCustomerProfile body;
}