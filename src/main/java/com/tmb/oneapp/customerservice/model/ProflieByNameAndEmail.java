package com.tmb.oneapp.customerservice.model;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProflieByNameAndEmail extends ProflieByCitizenAndMobile {

    @JsonProperty("ref_id")
    public String refId;

    @JsonProperty("email_address")
    public String emailAddress;

}
