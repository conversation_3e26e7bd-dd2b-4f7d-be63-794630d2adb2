package com.tmb.oneapp.customerservice.model.ekycgetbiometric;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EkycGetBiometricRequestBiometric {
    @NotNull
    @Schema(description = "Method", example = "selfie", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("method")
    private String method;
    @Schema(description = "Source", example = "TMB", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("source")
    private String source;
}