package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SendEmailRecordModel {
    private String rmId;
    private SendEmailParamsModel params;
    private SendEmailParamEmailModel email;
}