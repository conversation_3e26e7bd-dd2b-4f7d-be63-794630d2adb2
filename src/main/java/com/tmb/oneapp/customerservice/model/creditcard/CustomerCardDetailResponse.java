package com.tmb.oneapp.customerservice.model.creditcard;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerCardDetailResponse {
    @JsonProperty("rm_id" )
    private String rmId;
    @JsonProperty("identification_id" )
    private String identificationId;

}
