package com.tmb.oneapp.customerservice.model.personalizedinformation.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.Date;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PersonalizedInformationResponse {
    private String personalizedId;
    private String personalizedName;
    private String personalizedPictureId;
    private String personalizedMailId;
    private String personalizedMobileNumber;
    private String personalizedFacebookId;
    private String personalizedStatus;
    private String ownFlag;
    private String favoriteFlag;
    private String crmId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Bangkok")
    private Date createDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Bangkok")
    private Date updateDate;
    private String appId;
}
