package com.tmb.oneapp.customerservice.model.prospect;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.EKYCProspectETEConsent;
import com.tmb.oneapp.customerservice.model.EKYCProspectETEIdentification;
import com.tmb.oneapp.customerservice.model.EKYCProspectETEOfficeAddress;
import com.tmb.oneapp.customerservice.model.EKYCProspectETEPrimaryAddress;
import com.tmb.oneapp.customerservice.model.EKYCProspectETERisk;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EKYCccSearchProspectETECustomer {
    @JsonProperty("prospect_id")
    private String ccProspectId;
    @JsonProperty("channel")
    private String ccChannel;
    @JsonProperty("identification")
    private EKYCProspectETEIdentification ccIdentification;
    @JsonProperty("title_thai")
    private String prospectCustTitleThai = CustomerServiceConstant.BLANK;
    @JsonProperty("title_eng")
    private String prospectCustTitleEng = CustomerServiceConstant.BLANK;
    @JsonProperty("firstname_thai")
    private String prospectCustFirstnameThai = CustomerServiceConstant.BLANK;
    @JsonProperty("firstname_eng")
    private String prospectCustFirstnameEng = CustomerServiceConstant.BLANK;
    @JsonProperty("lastname_thai")
    private String prospectCustLastnameThai = CustomerServiceConstant.BLANK;
    @JsonProperty("lastname_eng")
    private String prospectCustLastnameEng = CustomerServiceConstant.BLANK;
    @JsonProperty("birth_date")
    private String prospectCustBirthDate = CustomerServiceConstant.BLANK;
    @JsonProperty("gender")
    private String prospectCustGender = CustomerServiceConstant.BLANK;
    @JsonProperty("marital_status")
    private String prospectCustMaritalStatus = CustomerServiceConstant.BLANK;
    @JsonProperty("nationality")
    private String prospectCustNationality = CustomerServiceConstant.BLANK;
    @JsonProperty("other_nationality")
    private String prospectCustOtherNationality = CustomerServiceConstant.BLANK;
    @JsonProperty("education")
    private String prospectCustEducation = CustomerServiceConstant.BLANK;
    @JsonProperty("occupation")
    private String prospectCustOccupation = CustomerServiceConstant.BLANK;
    @JsonProperty("occupation_specify")
    private String prospectCustOccupationSpecify = CustomerServiceConstant.BLANK;
    @JsonProperty("income")
    private String prospectCustIncome = CustomerServiceConstant.BLANK;
    @JsonProperty("country_for_income")
    private String prospectCustCountryForIncome = CustomerServiceConstant.BLANK;
    @JsonProperty("source_for_income")
    private String prospectCustSourceForIncome = CustomerServiceConstant.BLANK;
    @JsonProperty("mobile_no")
    private String prospectCustMobileNumber = CustomerServiceConstant.BLANK;
    @JsonProperty("phone_number")
    private String prospectCustPhoneNumber = CustomerServiceConstant.BLANK;
    @JsonProperty("phone_extension")
    private String prospectCustPhoneExtension = CustomerServiceConstant.BLANK;
    @JsonProperty("email")
    private String prospectCustEmail = CustomerServiceConstant.BLANK;
    @JsonProperty("purpose_for_saving_flag")
    private String prospectCustPurposeForSavingFlag = CustomerServiceConstant.BLANK;
    @JsonProperty("purpose_for_other")
    private String prospectCustPurposeForOther = CustomerServiceConstant.BLANK;
    @JsonProperty("risk")
    private EKYCProspectETERisk prospectCustRisk;
    @JsonProperty("registered_address")
    private EKYCProspectETEPrimaryAddress prospectCustRegisteredAddress;
    @JsonProperty("primary_address")
    private EKYCProspectETEPrimaryAddress prospectCustPrimaryAddress;
    @JsonProperty("office_address")
    private EKYCProspectETEOfficeAddress prospectCustOfficeAddress;
    @JsonProperty("consent")
    private EKYCProspectETEConsent prospectCustConsent;
    @JsonProperty("business_code")
    private String prospectCustBussinessCode = CustomerServiceConstant.BLANK;
    @JsonProperty("country_of_birth")
    private String prospectCustCountryOfBirth = CustomerServiceConstant.THAILAND_CODE;
    @JsonProperty("not_pass_qa_reason")
    private String prospectCustNotPassQAReason = CustomerServiceConstant.BLANK;
    @JsonProperty("not_pass_qa_update_date")
    private String prospectCustNotPassQAUpdateDate = CustomerServiceConstant.BLANK;
    @JsonProperty("not_pass_qa_empid")
    private String prospectCustNotPassQaEmpId = CustomerServiceConstant.BLANK;
    @JsonProperty("verify_method")
    private String prospectCustVerifyMethod = CustomerServiceConstant.BLANK;
    @JsonProperty("flow_state")
    private String prospectCustFlowState;
    @JsonProperty("validate_date")
    private String prospectCustValidateDate = CustomerServiceConstant.BLANK;
    @JsonProperty("device_id")
    private String prospectCustDeviceId = CustomerServiceConstant.BLANK;
    @JsonProperty("reference_id")
    private String prospectCustReferenceId = CustomerServiceConstant.BLANK;
    @JsonProperty("product_code")
    private String prospectCustProductCode = CustomerServiceConstant.BLANK;
}
