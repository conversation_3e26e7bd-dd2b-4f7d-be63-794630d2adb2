package com.tmb.oneapp.customerservice.model.personalizefacerecognition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;


@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "CRM_ONEAPP_LEVEL_ATTR", schema = "CRMADM")
public class CrmOneAppLevelAttrEntity {

    @Id
    @Column(name = "CRM_ID")
    private String crmId;

    @Column(name = "FR_CUST_TYPE")
    private String frCustomerType;

    @Column(name = "CREATED_DATE")
    private Date createdDate;

    @Column(name = "UPDATED_DATE")
    private Date updatedDate;
}
