
package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@ToString
public class PromptPayValidationDataETE {

	private Sender sender;
    private Receiver receiver;
    private String amount;
    private String fee;
    private String rtpTransactionReference;
    private String transactionReference;
    private String transactionCreatedDatetime;
    private String senderType;
    private String receiverType;
    private String chargeCode;
    private Vat vat;
    private Vat tax;
    private Balance balance;
    private Terminal terminal;

}
