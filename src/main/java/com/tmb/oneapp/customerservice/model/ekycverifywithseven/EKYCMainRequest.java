package com.tmb.oneapp.customerservice.model.ekycverifywithseven;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@SuperBuilder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EKYCMainRequest {
    private String rquid;
    private String appId;
    private String initialVector;
    private String encryptedData;
    private String verifiedChannel;
}
