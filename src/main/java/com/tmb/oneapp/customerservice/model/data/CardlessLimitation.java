package com.tmb.oneapp.customerservice.model.data;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CardlessLimitation {
	
	@Schema(example="1900000",requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("cardless_max_limit_amt" )
	private Integer cardlessMaxLimitAmt;
	
	@Schema(example="0",requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("cardless_accu_usg_amt")
	private Integer cardlessAccuUsgAmt;

}
