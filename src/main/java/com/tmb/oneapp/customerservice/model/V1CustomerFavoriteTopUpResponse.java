package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class V1CustomerFavoriteTopUpResponse {

    private String favoriteId;
    private String billerNickname;
    private String billerCompcode;
    private String billerName;
    private String refNo1;
    private String refNo2;
    private String fromAccNo;
    private String fromAccName;
    private Date addedDate;
    private String heartFlag;
    private Integer isDeleted;
    private String note;
    private String pictureId;
    private String billerLogoImage;
    private Date deletedDate;
    private String imageUrl;
    private String defaultImageUrl;
    private String displayOrder;
    private String billerType;
    private String billerId;
    private String creditCardFlag;
    private Date expiredDate;
}
