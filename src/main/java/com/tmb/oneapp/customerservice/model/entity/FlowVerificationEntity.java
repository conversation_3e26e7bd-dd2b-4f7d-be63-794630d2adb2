package com.tmb.oneapp.customerservice.model.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "FLOW_VERIFICATION", schema = "CRMADM")
public class FlowVerificationEntity {
    @Id
    @Column(name = "UUID")
    private String uuId;

    @Column(name = "CRM_ID")
    private String crmId;

    @Column(name = "ACTION_TYPE_ID")
    private String actionTypeId;

    @Column(name = "CHANNEL_ID")
    private String channelId;

    @Column(name = "FLOW_STATUS_ID")
    private String flowStatusId;

    @Column(name = "APPLY_DATE")
    private Date applyDate;

    @Column(name = "ACTIVATION_DATE")
    private Date activationDate;

    @Column(name = "EXPIRED_DATE")
    private Date expiredDate;

    @Column(name = "VERIFICATION_COUNTER")
    private Integer verificationCounter;

    @Column(name = "NOTE")
    private String note;

    @Column(name = "last_update_empid")
    private String lastUpdateEmpId;

    @Column(name = "update_date")
    private Date updateDate;
}
