package com.tmb.oneapp.customerservice.model.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.Hibernate;

import java.time.LocalDate;
import java.util.Objects;

@Data
@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "CUST_COMMON_MODULE", schema = "CUSTCRMUSR")
public class CustCommonModuleEntity {

    @Id
    @Column(name = "CITIZEN_ID")
    private String citizenId;
    @Column(name = "MODULE_NAME")
    private String moduleName;
    @Column(name = "ACTIVITY_LOG")
    private String activityLog;
    @Column(name = "FLOW_NAME")
    private String flowName;
    @Column(name = "FLOW_RESULT")
    private String flowResult;
    @Lob
    @Column(name = "BIO_DATA")
    private String bioData;
    @Column(name = "STATUS")
    private String status;
    @Column(name = "CRM_ID")
    private String crmId;
    @Column(name = "FATCA_ANS")
    private String fatcaAns;
    @Column(name = "TITLE")
    private String title;
    @Column(name = "FIRST_NAME_TH")
    private String firstNameTh;
    @Column(name = "LAST_NAME_TH")
    private String lastNameTh;
    @Column(name = "FIRST_NAME_EN")
    private String firstNameEn;
    @Column(name = "LAST_NAME_EN")
    private String lastNameEn;
    @Column(name = "DATE_OF_BIRTH")
    private LocalDate dateOfBirth;
    @Column(name = "ISSUE_DATE")
    private LocalDate issueDate;
    @Column(name = "EXPIRY_DATE")
    private LocalDate expiryDate;
    @Column(name = "LASER_CODE")
    private String laserCode;
    @Column(name = "VERIFY_METHOD")
    private String verifyMethod;
    @Column(name = "CREATE_DATE")
    private LocalDate createDate;
    @Column(name = "UPDATE_DATE")
    private LocalDate updateDate;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        CustCommonModuleEntity that = (CustCommonModuleEntity) o;
        return citizenId != null && Objects.equals(citizenId, that.citizenId);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
}
