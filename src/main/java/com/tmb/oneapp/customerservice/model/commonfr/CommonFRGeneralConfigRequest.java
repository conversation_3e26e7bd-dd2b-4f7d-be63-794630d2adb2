package com.tmb.oneapp.customerservice.model.commonfr;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonFRGeneralConfigRequest {
    private String configType;
    private String configCode;
    private String flow;
    private String value;
    private Boolean activeFlag;
}
