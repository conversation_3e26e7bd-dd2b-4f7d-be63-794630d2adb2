package com.tmb.oneapp.customerservice.model.editprofile;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.tmb.oneapp.customerservice.model.profile.Address;
import com.tmb.oneapp.customerservice.model.profile.Consent;
import com.tmb.oneapp.customerservice.model.profile.Kyc;
import com.tmb.oneapp.customerservice.model.profile.Phone;
import com.tmb.oneapp.customerservice.model.profile.Profile;
import com.tmb.oneapp.customerservice.model.profile.SourceOfIncome;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "profile", "kyc", "additional_kyc", "source_of_incomes", "consents", "addresses", "phones" })
@Data
public class CustomerProfile {
    @JsonProperty("profile")
    public Profile profile;
    @JsonProperty("kyc")
    public Kyc kyc;
    @JsonProperty("additional_kyc")
    public AdditionalKycProfile additionalKyc;
    @JsonProperty("source_of_incomes")
    public List<SourceOfIncome> sourceOfIncomes = null;
    @JsonProperty("consents")
    public List<Consent> consents = null;
    @JsonProperty("addresses")
    public List<Address> addresses = null;
    @JsonProperty("phones")
    public List<Phone> phones = null;
}
