package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TaxCertificateConsentsModel {
    private String acctCtl1;
    private String acctCtl2;
    private String acctCtl3;
    private String acctCtl4;
    private String acctNbr;
    private String channel;
    private String flag;
    private String createDate;
    private String updateDate;
    private String updateBy;
}