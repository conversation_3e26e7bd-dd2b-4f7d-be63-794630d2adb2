package com.tmb.oneapp.customerservice.model.prospect;

import java.util.HashMap;
import java.util.Map;

import com.tmb.oneapp.customerservice.constant.FlowStates;
import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.EKYCProspectETECustomer;

import lombok.experimental.Accessors;

@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProspectSearchFlowState extends EKYCProspectETECustomer {
	private static final Map<String, String> flowStateMapper;
	static {
		flowStateMapper = new HashMap<>();
		for(FlowStates flow : FlowStates.values()){
			String key = flow.name().toUpperCase();
			String value = flow.toString().toUpperCase();
			flowStateMapper.put(key, value);
		}
		flowStateMapper.put(FlowStates.EMPTY.name().toUpperCase(),
				FlowStates.DASH.toString().toUpperCase());
	}

	@JsonProperty("flow_state")
	private String flowState = CustomerServiceConstant.BLANK;

	public void setFlowState(String flowState) {
		this.flowState = flowState;
	}

	public String getFlowState() {
		String res = flowStateMapper.get(flowState.toUpperCase());
		return StringUtils.isNotEmpty(res) ? res.toUpperCase()
				: CustomerServiceConstant.EKYC_PROSPECT_FLOW_STATE_DEFAULT;
	}

}
