package com.tmb.oneapp.customerservice.model.ndid;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown=true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DataResponse {

    private String identifier;
    private String namespace;
    private String channelId;
    private int createdAt;
    private int updatedAt;
    private int expiresAt;
    private String role;
    private CustomerData data;
    @JsonProperty("ndid_data")
    private List<NDIDData> nDIDData;
    private Optional optional;
    private String responseCode;
    private String responseDescTh;
    private String responseDescEn;
    private IDP idp;

    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown=true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class CustomerData {
        private CustomerName customerName;
        private String birthDate;
        private String cardNumber;
        private String cardType;
        @JsonProperty("card_issueing_country")
        private String cardIssuingCountry;
        @JsonProperty("card_issuee_date")
        private String cardIssueDate;
        private String cardExpiryDate;
        private CustomerAddress customerAddress;
        private String occupationDescription;
        private String officeName;
        private String thaiOfficeStreetAddress1;
        private String thaiOfficeStreetAddress2;
        @JsonProperty("thai_office_subdistrict")
        private String thaiOfficeSubDistrict;
        private String thaiOfficeDistrict;
        private String thaiOfficeProvince;
        private String thaiOfficeZipcode;
        private String thaiOfficeFullAddress;
        private String thaiOfficeCountry;
        private String officeTelephoneNumber;
        @JsonProperty("office_telephone_extensio")
        private String officeTelephoneExtension;
        private String homeTelephoneNumber;
        private String homeTelephoneExtension;
        private String mobileTelephoneNumber;
        private String emailAddress;
        private String sourceOfIncomeCountry;
        private String sourceOfIncome;
        private String education;
        private String income;
        private String nationality;
        private String nationalityOther;
        private String maritalStatus;
        private String gender;
        private String lastTransactionDate;
        private String selfieImageData;
        private String selfieImageSize;
        private String selfieImageType;
    }

    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown=true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class CustomerName {
        private String thaiTitle;
        private String thaiFirstName;
        private String thaiMiddleName;
        private String thaiLastName;
        private String thaiFullName;
        private String englishTitle;
        private String englishFirstName;
        private String englishMiddleName;
        private String englishLastName;
        private String englishFullName;
    }

    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown=true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class CustomerAddress {

        private String thaiIdCardStreetAddress1;
        private String thaiIdCardStreetAddress2;
        @JsonProperty("thai_id_card_subdistrict")
        private String thaiIdCardSubDistrict;
        private String thaiIdCardDistrict;
        private String thaiIdCardProvince;
        private String thaiIdCardZipcode;
        private String thaiIdCardFullAddress;
        private String thaiIdCardCountry;
        private String thaiContactStreetAddress1;
        private String thaiContactStreetAddress2;
        @JsonProperty("thai_contact_subdistrict")
        private String thaiContactSubDistrict;
        private String thaiContactDistrict;
        private String thaiContactProvince;
        private String thaiContactZipcode;
        private String thaiContactFullAddress;
        private String overseasFullAddress;
        private String thaiContactCountry;
    }

    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown=true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class NDIDData {
        private String sourceNodeId;
        private String serviceId;
        private String sourceSignature;
        private String signatureSignMethod;
        private String dataSalt;
        private String data;
    }

    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown=true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class Optional {
        private String name;
        private String applicationCode;
        private String applicationName;
        private String description;
    }

    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown=true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class IDP {
        private String id;
        @JsonProperty("asNodeId")
        private String asNodeId;
        private String provider;
        private String industryCode;
        private String companyCode;
        private String applicationCode;
        private String applicationName;
        private String description;
        private Boolean agent;
        private String descriptionTh;
        private String descriptionEn;
        private Double maxAal;
        private Double maxIal;
    }

}
