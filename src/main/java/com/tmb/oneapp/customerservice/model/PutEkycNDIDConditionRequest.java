package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PutEkycNDIDConditionRequest {
    @Schema(description = "Citizen Id", example = "3479085501688", requiredMode = Schema.RequiredMode.REQUIRED)
    private String citizenId;
    @Schema(description = "Event", example = "revoked", requiredMode = Schema.RequiredMode.REQUIRED)
    private String event;
    @Schema(description = "Consent version", example = "ndid03", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String consentVersion;

}
