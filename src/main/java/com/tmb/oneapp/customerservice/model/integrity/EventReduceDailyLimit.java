package com.tmb.oneapp.customerservice.model.integrity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventReduceDailyLimit {
    private String eventName;
    private String crmId;
    private String deviceId;
    private String deviceModel;
    private String reasonToChange;
    private String reasonFailed;

    private String newInterMaxLimitCurrent;
    private String newInterMaxLimitHistory;
    private String newBillPayMaxDailyLimit;
    private String newBillPayMaxLimitHistory;
    private String newCardLessMaxDailyLimit;
    private String newCardLessMaxLimitHistory;
    private String newLocalTransLimitIncludesTopUp;
    private String newMaxLocalTransLimitIncludesTopUpHistory;

    private String oldLocalTransLimitIncludesTopUp;
    private String oldCardLessMaxDailyLimit;
    private String oldBillPayMaxDailyLimit;
    private String oldInterMaxLimitCurrent;
    private String oldMaxLocalTransLimitIncludesTopUpHistory;
    private String oldCardLessMaxLimitHistory;
    private String oldBillPayMaxLimitHistory;
    private String oldInterMaxLimitHistory;

}
