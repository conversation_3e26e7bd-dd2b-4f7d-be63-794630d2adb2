package com.tmb.oneapp.customerservice.model.address.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerProfile {

    @JsonProperty(value = "cc_id")
    private String ccId;

    @JsonProperty(value = "rm_id")
    private String crmId;
}
