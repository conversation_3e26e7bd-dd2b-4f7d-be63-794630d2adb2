package com.tmb.oneapp.customerservice.model.taxdoc.attributeId;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
public class DepositTaxTxnAttributeId implements Serializable {
    private static final long serialVersionUID = -8992603531260498818L;
    private String taxtAccNo;

    private Date taxtTranDate;
}
