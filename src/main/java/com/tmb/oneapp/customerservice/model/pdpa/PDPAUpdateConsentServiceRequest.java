package com.tmb.oneapp.customerservice.model.pdpa;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PDPAUpdateConsentServiceRequest {

    @Schema(description = "Consent Type", example = "P", requiredMode = Schema.RequiredMode.REQUIRED)
    @Pattern(regexp = "[P|M]") // P : pdpa, M : market
    @NotBlank
    private String consentType;
    @Schema(description = "Consent Flag", example = "Y", requiredMode = Schema.RequiredMode.REQUIRED)
    @Pattern(regexp = "[Y|N]")
    @NotBlank
    private String flag;
    @Schema(description = "Consent Version Number", example = "9", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank
    private String versionNumber;
    @Schema(description = "Consent Signup Date", example = "2021-07-08", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank
    private String signupDate;
}
