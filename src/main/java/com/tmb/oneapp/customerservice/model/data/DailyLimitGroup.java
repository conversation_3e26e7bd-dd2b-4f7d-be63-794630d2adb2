package com.tmb.oneapp.customerservice.model.data;

import com.tmb.common.model.CustomerProfilingGroupLimitConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DailyLimitGroup {
    private String groupId;
    private String eligibleGroupId;
    private List<CustomerProfilingGroupLimitConfig> groupLimitConfigs;
}
