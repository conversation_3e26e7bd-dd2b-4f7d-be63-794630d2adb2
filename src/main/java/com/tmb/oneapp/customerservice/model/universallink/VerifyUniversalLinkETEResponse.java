package com.tmb.oneapp.customerservice.model.universallink;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.customerservice.model.EteMessageStatus;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class VerifyUniversalLinkETEResponse {
    private EteMessageStatus status;
    private NdidResultStatus ndidResult;

}
