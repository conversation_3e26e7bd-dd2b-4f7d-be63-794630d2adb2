package com.tmb.oneapp.customerservice.model.activity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.index.Indexed;

import java.util.Date;
import java.util.List;

@Data
@Getter
@Setter
@AllArgsConstructor
@Builder
@Document(collection = "#{@databaseCollection.getCollectionName()}")
@NoArgsConstructor
public class ActivityRequest {
    @Id
    @Field("_id")
    private String id;

    @Field("correlation_id")
    private String correlationid;

    @Indexed(name = "activity_type_id_index")
    @Field("activity_type_id")
    private String activityid;

    @Field("activity_type")
    private String activitytype;

    @Indexed(name = "timestamp_index")
    @Field("activity_date")
    private Date timestamp;

    @Field("device_id")
    private String deviceid;

    @Indexed(name = "crm_id_index")
    @Field("crm_id")
    private String crmid;

    @Field("channel")
    private String channel;

    @Field("app_version")
    private String app_version;

    @JsonProperty("ip_address")
    @Field("ip_address")
    private String ipaddress;

    @Field("activity_status")
    private String activitystatus;

    @Field("fail_reason")
    private String failreason;

    @Field("emp_id")
    private String empId;

    @Field("activitydescription")
    private List<ActivityDescription> activitydescription;

    @Indexed(name = "last_updated_date_index")
    @Field("last_updated_date")
    private Date lastUpdatedDate;
}