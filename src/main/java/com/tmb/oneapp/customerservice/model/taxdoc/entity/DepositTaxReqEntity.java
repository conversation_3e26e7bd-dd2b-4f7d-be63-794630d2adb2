package com.tmb.oneapp.customerservice.model.taxdoc.entity;

import lombok.Data;
import lombok.ToString;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;

@Entity
@Data
@ToString
@Table(name = "DEPOSIT_TAX_REQ", schema = "CRMADM")
public class DepositTaxReqEntity {
    @Id
    @Column(name = "REQUEST_ID")
    private String requestId;
    @Column(name = "CRM_ID")
    private String crmId;

    @Column(name = "ACCT_NO")
    private String acctNo;

    @Column(name = "TAX_YEAR")
    private String taxYear;

    @Column(name = "TAX_REQ_DATETIME")
    private Date taxReqDatetime;

    @Column(name = "EMAIL")
    private String email;

    @Column(name = "PWD_PROTECTED")
    private String pwdProtected;

    @Column(name = "BATCH_FLAG")
    private String batchFlag;

    @Column(name = "BATCH_PROCESS_DATE")
    private Date batchProcessDate;
}
