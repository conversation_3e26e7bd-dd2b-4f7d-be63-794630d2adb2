package com.tmb.oneapp.customerservice.model.activity;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.common.model.BaseEvent;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper=false)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VerifyIalAndEkycActivityLog extends BaseEvent {
    private String upliftStatus;
    private String oldIalLevel;
    private String currentIalLevel;
    private String oldEkycFlag;
    private String currentEkycFlag;
    private String flow;
    private String allowToContinue;
    private String customerVersion;
    private String hideableDeviceId;
}
