package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ViewOnlyHistoryDTO {
    @NotBlank(message = "CRM ID cannot be blank.")
    private String crmId;
    @NotBlank(message = "Account Number cannot be blank.")
    private String accountNo;
    private String viewOnlyFlag;
    private String channel;
    private String employeeId;
    private String reason;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "Asia/Bangkok")
    private LocalDateTime timeStamp;

}
