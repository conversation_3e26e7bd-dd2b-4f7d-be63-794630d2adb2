package com.tmb.oneapp.customerservice.model.favorite;

import com.tmb.oneapp.customerservice.caldb.model.PayeeEntity;
import com.tmb.oneapp.customerservice.model.AccountInfoResponse;
import com.tmb.oneapp.customerservice.model.BillerInfoResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface V1FavoriteBillPayMapper {
    V1FavoriteBillPayMapper INSTANCE = Mappers.getMapper(V1FavoriteBillPayMapper.class);

    @Mapping(target = "favoriteId", source = "payeeEntity.payeeId")
    @Mapping(target = "billerNickname", source = "payeeEntity.payeeNickname")
    @Mapping(target = "billerCompcode", source = "payeeEntity.billerCompcode")
    @Mapping(target = "refNo1", source = "payeeEntity.referenceNumber")
    @Mapping(target = "refNo2", source = "payeeEntity.referenceNumber2")
    @Mapping(target = "fromAccNo", source = "payeeEntity.fromAcctId")
    @Mapping(target = "fromAccName", source = "accountInfoResponse.accountName")
    @Mapping(target = "addedDate", source = "payeeEntity.createdDate")
    @Mapping(target = "heartFlag", source = "payeeEntity.heartFlag")
    @Mapping(target = "isDeleted", source = "payeeEntity.isDeleted")
    @Mapping(target = "note", source = "payeeEntity.memoText")
    @Mapping(target = "pictureId", ignore = true)
    @Mapping(target = "imageUrl", ignore = true)
    @Mapping(target = "defaultImageUrl", source = "billerInfo.imageUrl")
    @Mapping(target = "billerLogoImage", source = "billerInfo.imageUrl")
    @Mapping(target = "billerName", source = "billerInfo.nameTh")
    @Mapping(target = "displayOrder", source = "payeeEntity.displayOrder")
    @Mapping(target = "billerType", source = "payeeEntity.billerGroupType")
    @Mapping(target = "billerId", source = "payeeEntity.billerId")
    @Mapping(target = "creditCardFlag", source = "billerInfo.creditCardFlag")
    @Mapping(target = "expiredDate", ignore = true)

    V1FavoriteBillPay toV1FavoriteBillPay(PayeeEntity payeeEntity, BillerInfoResponse billerInfo, AccountInfoResponse accountInfoResponse);

}
