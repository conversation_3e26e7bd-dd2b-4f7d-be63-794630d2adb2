package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.ToString;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * CustomerAPP Entity class
 */
@ToString
@Data
@Entity
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Table(schema = "CRMADM", name = "CRM_APP_LEVEL_ATTR")
@IdClass(CustomerAppLevelAttributeId.class)
public class CustomerAppLevelAttribute {
    @Id
    @Column(name = "CRM_ID")
    private String crmId;

    @Id
    @Column(name = "APP_ID")
    private String appId;

    @Column(name = "TXN_LIMIT_AMT")
    private Integer txnLimitAmt;

    @Column(name = "MAX_LIMIT_AMT_CURRENT")
    private Integer maxLimitAmtCurrent;

    @Column(name = "MAX_LIMIT_AMT_HIST")
    private Integer maxLimitAmtHist;

    @Column(name = "MAX_LIMIT_AMT_REQUEST")
    private Integer maxLimitAmtRequest;

    @Column(name = "ACCU_USG_AMT_DAILY")
    private Integer accuUsgAmtDaily;

    @Column(name = "FIRST_ACTIVATION_TIMESTAMP")
    private Date firstActivationTimestamp;

    @Column(name = "LANGUAGE_CD")
    private String languageCd = "TH";

    @Column(name = "TOKEN_DEVICE_FLAG")
    private String tokenDeviceFlag;

    @Column(name = "STATUS_ID")
    private String statusId;

    @Column(name = "OTT_MAX_LIMIT_AMT_CURRENT")
    private Integer ottMaxLimitAmtCurrent;

    @Column(name = "OTT_MAX_LIMIT_TEMP_FLAG")
    private String ottMaxLimitTempFlag;

    @Column(name = "OTT_MAX_LIMIT_AMT_HIST")
    private Integer ottMaxLimitAmtHist;

    @Column(name = "OTT_MAX_LIMIT_AMT_REQUEST")
    private Integer ottMaxLimitAmtRequest;

    @Column(name = "OTT_ACCU_USG_AMT_DAILY")
    private Integer ottMaxLimitAmtDaily;

    @Column(name = "ACCU_USG_AMT_PP")
    private Integer accuUsgAmtPp;

    @Column(name = "ACCU_USG_AMT_INHOUSE")
    private Integer accuUsgAmtInHouse;

    @Column(name = "ACCU_USG_AMT_ORFT")
    private Integer accuUsgAmtOrft;

    @Column(name = "ACCU_USG_AMT_SMART")
    private Integer accuUsgAmtSmart;

    @Column(name = "PIN_FREE_SEETING_FLAG")
    private String pinFreeSeetingFlag;

    @Column(name = "PIN_FREE_TR_LIMIT")
    private Integer pinFreeTrLimit;

    @Column(name = "PIN_FREE_BP_LIMIT")
    private Integer pinFreeBpLimit;

    @Column(name = "PIN_FREE_CREATED")
    private Date pinFreeCreated;

    @Column(name = "PIN_FREE_TR_LAST_UPDATED")
    private Date pinFreeTrLastUpdated;

    @Column(name = "PIN_FREE_BP_LAST_UPDATED")
    private Date pinFreeBpLastUpdated;

    @Column(name = "PIN_FREE_TXN_COUNT")
    private Integer pinFreeTxnCount;

    @Column(name = "PIN_FREE_QR_LIMIT")
    private BigDecimal pinFreeQrLimit;

    @Column(name = "PIN_FREE_ACCU_USG_AMT")
    private BigDecimal pinFreeAccuUsgAmt;

    @Column(name = "SPECIFY")
    private String specify;

    @Column(name = "CONSENT_VERSION")
    private String consentVersion;

    @Column(name = "CONSENT_DATE")
    private Date consentDate;

    @Column(name = "DOCUMENT_TYPE")
    private String documentType;

    @Column(name = "BIOMETRIC_TYPE")
    private String biometricType;

    @Column(name = "CONFIDENT_LEVEL")
    private String confidentLevel;

    @Column(name = "LAST_UPDATE_CHANNEL")
    private String lastUpdateChannel;

    @Column(name = "LAST_UPDATE_DATE")
    private Date lastUpdateDate;

    @Column(name = "OBJ_ACCOUNT_OPEN")
    private String objAccountOpen;

    @Column(name = "DEFAULT_ACCT_ID")
    private String defaultAcctId;

    @Column(name = "LAST_EB_UPDATED_REASON")
    private String lastEbUpdateReason;

    @Column(name = "MASK_ACCT_ID_FLAG")
    private String maskAcctIdFlag;

    @Column(name = "LAST_EB_UPDATED_DATE")
    private Date lastEbUpdatedDate;

    @Column(name = "LAST_EB_UPDATED_USER")
    private String lastEbUpdatedUser;

    @Column(name = "LAST_NOTPASS_VERIFY_REASON")
    private String lastNotPassVerifyReason;

    @Column(name = "LAST_NOTPASS_VERIFY_DATE")
    private Date lastNotPassVerifyDate;

    @Column(name = "LAST_NOTPASS_VERIFY_USER")
    private String lastNotPassVerifyUser;

    @Column(name = "AUTO_SAVE_SLIP_MAIN")
    private String autoSaveSlipMain;

    @Column(name = "AUTO_SAVE_SLIP_OWN")
    private String autoSaveSlipOwn;

    @Column(name = "AUTO_SAVE_SLIP_MAIN_UPDATED")
    private Date autoSaveSlipMainUpdated;

    @Column(name = "AUTO_SAVE_SLIP_OWN_UPDATED")
    private Date autoSaveSlipOwnUpdated;

    @Column(name = "QUICK_BALANCE_SETTING_FLAG")
    private String quickBalanceSettingFlag;

    @Column(name = "QUICK_BALANCE_DISPLAY_FLAG")
    private String quickBalanceDisplayFlag;

    @Column(name = "QUICK_BALANCE_ACCOUNT_NO")
    private String quickBalanceAccountNo;

    @Column(name = "QUICK_BALANCE_ACCOUNT_TYPE")
    private String quickBalanceAccountType;

    @Column(name = "CARDLESS_MAX_LIMIT_AMT")
    private Integer cardlessMaxLimitAmt;

    @Column(name = "CARDLESS_ACCU_USG_AMT")
    @Getter(AccessLevel.NONE)
    private Integer cardlessAccuUsgAmt;

    public Integer getCardlessAccuUsgAmt() {
        if (this.cardlessAccuUsgAmt == null) return 0;
        return this.cardlessAccuUsgAmt;
    }

    @Column(name = "EMAIL_NOTI_LOGIN_ALERT")
    private String emailNotiLoginAlert;

    @Column(name = "EMAIL_NOTI_TRANS_ALERT")
    private String emailNotiTransAlert;

    @Column(name = "EMAIL_NOTI_LOGIN_ALERT_UPDATED_DATE")
    private Date emailNotiLoginUpdateDate;

    @Column(name = "EMAIL_NOTI_TRANS_ALERT_UPDATED_DATE")
    private Date emailNotiTransAlertUpdateDate;

    @Column(name = "EKYC_VERIFY_METHOD")
    private String ekycVerifyMethod;

    @Column(name = "EB_MAX_LIMIT_TEMP_FLAG")
    private String ebMaxLimitTempFlag;

    @Column(name = "EXCHANGE_MAX_LIMIT_AMT")
    private BigDecimal exchangeMaxLimitAmt;

    @Column(name = "EXCHANGE_ACCU_USG_AMT")
    private BigDecimal exchangeAccuUsgAmt;

    @Column(name = "BILLPAY_MAX_LIMIT_AMT")
    private Integer billpayMaxLimitAmt;

    @Column(name = "BILLPAY_ACCU_USG_AMT")
    private BigDecimal billpayAccuUsgAmt;

    @Column(name = "BILLPAY_MAX_LIMIT_AMT_HIST")
    private Integer billpayMaxLimitAmtHist;

    @Column(name = "BILLPAY_MAX_LIMIT_TEMP_FLAG")
    private String billpayMaxLimitTempFlag;

    @Column(name = "CARDLESS_MAX_LIMIT_AMT_HIST")
    private Integer cardlessMaxLimitAmtHist;

    @Column(name = "CARDLESS_MAX_LIMIT_TEMP_FLAG")
    private String cardlessMaxLimitTempFlag;

    @Column(name = "PAYMENT_ACCU_USG_AMT")
    private BigDecimal paymentAccuUsgAmt;

    @Column(name = "LAST_MB_UPDATED_REASON")
    private String lastMbUpdatedReason;

    @Column(name = "LAST_MB_UPDATED_DATE")
    private Date lastMbUpdatedDate;

    @Column(name = "LAST_MB_UPDATED_USER")
    private String lastMbUpdatedUser;
}