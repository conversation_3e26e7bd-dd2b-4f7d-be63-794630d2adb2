package com.tmb.oneapp.customerservice.model.favorite.interfaceobject;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CustomerFavoriteListResponse {
    private List<CustomerTransferResponse> transfer;
    private List<CustomerBillerResponse> biller;
    private List<CustomerTopupResponse> topup;

}
