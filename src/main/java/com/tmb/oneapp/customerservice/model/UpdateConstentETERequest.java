package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UpdateConstentETERequest {
	private String channel;
	@JsonProperty("reference_id")
	private String rferenceId;
	@JsonProperty("response_descrition")
	private String responseDesc;
}
