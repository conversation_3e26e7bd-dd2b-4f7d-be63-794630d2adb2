package com.tmb.oneapp.customerservice.model.favorite.image;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UpdateFavoriteImageRequest {
  @Schema( description = "module", example = "topup", requiredMode = Schema.RequiredMode.REQUIRED )
  private String module;
  
  @NotBlank
  @Pattern(message = "favoriteId must be a number", regexp="^[0-9]+$")
  @Schema( description = "favoriteId", example = "491", requiredMode = Schema.RequiredMode.REQUIRED )
  private String favoriteId;
  
  @Schema( description = "personalizedId", example = "2000000000" )
  private String personalizedId;
  
  @Schema( description = "favoriteNumber", example = "1112223334" )
  private String favoriteNumber;
  
  @Schema( description = "base64_image",example = "iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==" )
  private String base64Image;
  
  @NotBlank
  @Pattern(message = "uploadFlag must be a Y/N", regexp="^[YyNn]$")
  @Schema(description = "crmId",example = "Y", requiredMode = Schema.RequiredMode.REQUIRED)
  private String uploadFlag;
}
