package com.tmb.oneapp.customerservice.model.favorite;

import lombok.Data;

import java.util.Date;

@Data
public class V1FavoriteBillPay {
    private String favoriteId;
    private String billerNickname;
    private String billerCompcode;
    private String refNo1;
    private String refNo2;
    private String fromAccNo;
    private String fromAccName;
    private Date addedDate;
    private String heartFlag;
    private Integer isDeleted;
    private String note;
    private String pictureId;
    private String imageUrl;
    private String defaultImageUrl;
    private String billerLogoImage;
    private String billerName;
    private Integer displayOrder;
    private String billerType;
    private String billerId;
    private String creditCardFlag;
    private Date expiredDate;
}
