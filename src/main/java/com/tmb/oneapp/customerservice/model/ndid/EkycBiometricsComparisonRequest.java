package com.tmb.oneapp.customerservice.model.ndid;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@JsonIgnoreProperties(ignoreUnknown=true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EkycBiometricsComparisonRequest {
    private String documentId;
    private String documentIdType;
    private String verificationMethod;
    private Integer primaryBiometricDataLength;
    private String primaryBiometricDataMethod;
    private String primaryBiometricDataFormat;
    private String primaryBiometricDataSource;
    private String primaryBiometricData;
    private String secondaryBiometricData;
    private Integer secondaryBiometricDataLength;
    private String secondaryBiometricDataMethod;
    private String secondaryBiometricDataFormat;
    private String secondaryBiometricDataSource;
    private String channel;
    private String actionType;
    private String actionTypeDetail;
}
