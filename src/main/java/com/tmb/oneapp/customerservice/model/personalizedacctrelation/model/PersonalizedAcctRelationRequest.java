package com.tmb.oneapp.customerservice.model.personalizedacctrelation.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.Date;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PersonalizedAcctRelationRequest {
    private String personalizedId;
    private String personalizedAcctId;
    private String bankCd;
    private String personalizedAcctStatus;
    private String acctFavoriteFlag;
    private String personalizedAcctName;
    private String jointFlag;
    private String appId;
    private String personalizedAcctNickname;
    private String heartFlag;
    private String switchToAllfree;
    private Date switchDate;
    private String personalizedDisplayAcctStatus;
    private Integer personalizedAcctSortOrder;
    private String viewOnlyFlag;

    private AdditionalInfoPersonalized additionalInfo;
}
