package com.tmb.oneapp.customerservice.model.ekycgetbiometric;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tmb.oneapp.customerservice.model.EkycBiometricResponseBiometric;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class EkycGetBiometricResponseBiometric extends EkycBiometricResponseBiometric {
    @NotNull
    @Schema(description = "Timestamp", example = "1635238588397", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("timestamp")
    private String timestamp;
    @Schema(description = "channel", example = "MB", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("channel")
    private String channel;
}