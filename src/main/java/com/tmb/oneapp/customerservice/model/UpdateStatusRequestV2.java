package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UpdateStatusRequestV2 {
    @NotBlank
    @Pattern(message = "CRM ID must be a number", regexp="^[0-9]{30}$")
    @Schema(description = "crmId",example = "001100000000000000000019076870", requiredMode = Schema.RequiredMode.REQUIRED)
    private String crmId;

    @Schema(description = "channel",example = "AMLO", requiredMode = Schema.RequiredMode.REQUIRED)
    private String channel;

    @Pattern(message = "mb status must be a number", regexp="^$|^\\d{2}$")
    @Schema(description = "Customer MB status", example="02")
    private String mbStatus;

    private String mbStatusReason;

    @Pattern(message = "eb status must be a number", regexp="^$|^\\d{2}$")
    @Schema(description = "Customer EB status", example="02")
    private String ebStatus;

    private String ebStatusReason;
}
