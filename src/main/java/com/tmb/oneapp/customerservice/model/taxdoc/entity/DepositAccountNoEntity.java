package com.tmb.oneapp.customerservice.model.taxdoc.entity;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.customerservice.model.taxdoc.attributeId.DepositTaxTxnAttributeId;
import lombok.Data;
import lombok.ToString;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.IdClass;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;

@ToString
@Data
@Entity
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@IdClass(DepositTaxTxnAttributeId.class)
@Table(schema = "CRMADM", name = "DEPOSIT_TAX_TXN")
public class DepositAccountNoEntity {
    @Id
    @Column(name = "TAXT_ACC_NO")
    private String taxtAccNo;

    @Id
    @Column(name = "TAXT_TRAN_DATE")
    private Date taxtTranDate;
}
