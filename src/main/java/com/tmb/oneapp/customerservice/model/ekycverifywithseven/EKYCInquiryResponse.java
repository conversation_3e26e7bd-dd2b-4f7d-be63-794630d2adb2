package com.tmb.oneapp.customerservice.model.ekycverifywithseven;


import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.customerservice.model.EKYCProspectETEResponseStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EKYCInquiryResponse {
    private EKYCProspectETEResponseStatus status;
    private NDIDInquiryResult ndidResult;

    @EqualsAndHashCode(callSuper = true)
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class NDIDInquiryResult extends EKYCMainRequest {
        private String status;
        private String createDate;
        private String expireDate;
    }

}
