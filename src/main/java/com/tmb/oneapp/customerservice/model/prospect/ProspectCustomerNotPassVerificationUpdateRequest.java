package com.tmb.oneapp.customerservice.model.prospect;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProspectCustomerNotPassVerificationUpdateRequest {
    @NotBlank
    @Schema(description = "prospectId",example = "P12052543816", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("prospect_id")
    private String prospectId;

    @NotBlank
    @Size(max = 250, message= "LAST_NOTPASS_VERIFY_REASON must be 250 characters")
    @Schema(description = "prospectNotPassQaReason", example = "reason data", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("not_pass_qa_reason")
    private String prospectNotPassQaReason;


}
