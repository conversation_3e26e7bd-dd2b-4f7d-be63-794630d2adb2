package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@ToString
public class VerifyPinCacheRequest {

    @NotNull(message = "Key cannot be null")
    @NotEmpty(message = "Key cannot be empty string")
    @NotBlank(message = "Key cannot be blank")
    @Schema(description = "cache key", example = "get_test_key", required = true)
    private String key;

    @NotNull(message = "crm_id cannot be null")
    @NotEmpty(message = "crm_id cannot be empty string")
    @NotBlank(message = "crm_id cannot be blank")
    @Schema(description = "crm_id in cache data", example = "001100000000000000000025521918", required = true)
    @JsonProperty("crm_id")
    private String crmId;

    @NotNull(message = "module cannot be null")
    @NotEmpty(message = "module cannot be empty string")
    @NotBlank(message = "module cannot be blank")
    @Schema(description = "model in cache data", example = "TRANSFER", required = true)
    private String module;
}
