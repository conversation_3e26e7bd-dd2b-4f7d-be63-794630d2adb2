package com.tmb.oneapp.customerservice.model.ekycverifywithseven;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Biometric {
    private String cid;
    private String method;
    private String source;
    private String format;
    private String length;
    private String data;
    private boolean commonFlow;
    private String commonBioData;
    private String actionType;
    private String actionTypeDetail;
}
