package com.tmb.oneapp.customerservice.model.profile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Data;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "profile", "kyc", "additional_kyc", "source_of_incomes", "consents", "addresses", "phones" })
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class KycCustomer {
    @JsonProperty("profile")
    public Profile profile;
    @JsonProperty("kyc")
    public Kyc kyc;
    @JsonProperty("additional_kyc")
    public AdditionalKyc additionalKyc;
    @JsonProperty("source_of_incomes")
    public List<SourceOfIncome> sourceOfIncomes = null;
}