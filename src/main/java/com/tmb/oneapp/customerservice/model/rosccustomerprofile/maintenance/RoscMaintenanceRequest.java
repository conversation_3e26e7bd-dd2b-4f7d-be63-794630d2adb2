package com.tmb.oneapp.customerservice.model.rosccustomerprofile.maintenance;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import com.tmb.oneapp.customerservice.model.rosccustomerprofile.Committee;
import com.tmb.oneapp.customerservice.model.rosccustomerprofile.CustStatus;
import com.tmb.oneapp.customerservice.model.rosccustomerprofile.RoscCustomerModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RoscMaintenanceRequest extends RoscCustomerModel {
    private String custId;
    private String persWorkPermitType;
    private String persWorkPermitTypeOther;
    private String custCustomerLevel3Desc;
    private String persOfficeAddressCountry;
    private CustStatus custStatus;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Committee> committees;
}
