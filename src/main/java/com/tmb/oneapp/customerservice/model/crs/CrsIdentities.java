package com.tmb.oneapp.customerservice.model.crs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CrsIdentities {
    @JsonProperty("seq_nbr")
    public int seqNbr;
    @JsonProperty("country_tax_residence")
    public String countryTaxResidence;
    @JsonProperty("tin")
    public String tin;
    @JsonProperty("no_tin_reason")
    public String noTinReason;
    @JsonProperty("no_tin_reason_b")
    public String bNoTinReason;
    @JsonProperty("create_by")
    public String createBy;
    @JsonProperty("effective_date")
    public String effectiveDate;
}
