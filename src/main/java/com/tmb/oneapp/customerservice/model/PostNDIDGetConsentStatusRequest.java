package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PostNDIDGetConsentStatusRequest {
    private EkycNDIDGetConsentStatusCustomer customer;
    private String ndidEvent;
    private String consentVersion;

}
