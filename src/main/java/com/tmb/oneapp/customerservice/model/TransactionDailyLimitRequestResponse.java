package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TransactionDailyLimitRequestResponse {
	@JsonProperty("request_id")
	private String requestId;
	@JsonProperty("date_time")
	private String dateAndTime;
	@JsonProperty("type")
	private String type;
	@JsonProperty("old_request_limit")
	private Double oldRequestLimit;
	@JsonProperty("new_request_limit")
	private Double newRequestLimit;
	@JsonProperty("status")
	private String status;
	@JsonProperty("last_updated_user")
	private String lastUpdatedUser;
	@JsonProperty("last_updated_date_time")
	private String lastUpdatedDateTime;

}