package com.tmb.oneapp.customerservice.model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProflieByCitizenAndMobile {
    @JsonProperty("cc_id")
    public String ccId;
    @JsonProperty("rm_id")
    public String rmId;
    @JsonProperty("id_card")
    public String idCard;
    @JsonProperty("id_type")
    public String idType;
    @JsonProperty("cust_type_id")
    public String custTypeId;
    @JsonProperty("tha_fullname")
    public String thaFullname;
    @JsonProperty("eng_fullname")
    public String engFullname;
    @JsonProperty("birth_date")
    public String birthDate;
    @JsonProperty("mobile_no")
    public String mobileNo;
}