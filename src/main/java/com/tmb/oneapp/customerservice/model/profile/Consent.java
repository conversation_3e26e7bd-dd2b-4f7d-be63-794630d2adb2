package com.tmb.oneapp.customerservice.model.profile;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "type_of_consent", "flag", "version_number", "channel", "user_id", "signup_date", "system_date" })
@Data
public class Consent {

	@JsonProperty("type_of_consent")
	public String typeOfConsent;
	@JsonProperty("flag")
	public String flag;
	@JsonProperty("version_number")
	public String versionNumber;
	@JsonProperty("channel")
	public String channel;
	@JsonProperty("user_id")
	public String userId;
	@JsonProperty("signup_date")
	public String signupDate;
	@JsonProperty("system_date")
	public String systemDate;

}