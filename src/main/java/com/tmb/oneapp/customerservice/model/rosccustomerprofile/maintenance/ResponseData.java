package com.tmb.oneapp.customerservice.model.rosccustomerprofile.maintenance;


import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ResponseData {
    @JacksonXmlProperty(localName = "header")
    private Header header;
    @JacksonXmlProperty(localName = "body")
    private BodyDetails body;
}
