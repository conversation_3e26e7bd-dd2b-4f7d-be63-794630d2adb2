package com.tmb.oneapp.customerservice.model.profile;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

@NoArgsConstructor
@Data
@AllArgsConstructor
public class KycRequest {
    @NotBlank
    @Pattern(message = "Invalid state", regexp ="(verified|introduction)")
    @Schema(description = "state", requiredMode = Schema.RequiredMode.REQUIRED, example = "introduction")
    private String state;

    @NotNull
    @JsonProperty("ref_id")
    @Schema(description = "ref id", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private String refId;

    @NotNull
    @Schema(description = "product", requiredMode = Schema.RequiredMode.REQUIRED, example = "PF")
    private String product;
}
