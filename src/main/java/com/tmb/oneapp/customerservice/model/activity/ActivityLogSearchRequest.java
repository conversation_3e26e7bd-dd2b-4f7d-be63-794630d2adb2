package com.tmb.oneapp.customerservice.model.activity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ActivityLogSearchRequest {
    @Schema(description = "CRMID", example = "001100000000000000000001184384", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank
    @JsonProperty("crm_id")
    private String crmId;

    @Schema(description = "STATUSTYPE", example = "EB", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("status_type")
    private String statusType;
}
