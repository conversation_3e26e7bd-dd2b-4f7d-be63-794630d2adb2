package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "txn_type_description", schema = "crmadm")
public class TxnTypeDescriptionEntity {
	@Id
	@Column(name = "txn_type")
	@JsonProperty("txn_type")
	private String txnType;

	@Column(name = "txn_type_desc", columnDefinition = "nvarchar2(50)")
	@JsonProperty("txn_type_desc")
	private String txnTypeDesc;

	@Column(name = "cc_display_flag")
	@JsonProperty("cc_display_flag")
	private String ccDisplayFlag;
}
