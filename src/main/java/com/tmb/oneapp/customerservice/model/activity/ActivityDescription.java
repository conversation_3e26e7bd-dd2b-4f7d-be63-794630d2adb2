package com.tmb.oneapp.customerservice.model.activity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Field;

@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ActivityDescription {
    @JsonProperty("flex")
    private String flex;

    @JsonProperty("description")
    @Field("description")
    private String description;

    @JsonProperty("value")
    private String value;
}