package com.tmb.oneapp.customerservice.model.commonfr;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonFRValidationResponse {
    private String ial;
    private String ekycFlag;
    private Boolean validationFlag;
    private String ebCustomerStatusId;
    private String mbCustomerStatusId;
    private String pageNavigation;
    private String uuid;
}
