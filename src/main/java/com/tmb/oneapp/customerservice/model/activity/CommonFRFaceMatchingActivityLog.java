package com.tmb.oneapp.customerservice.model.activity;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.common.model.BaseEvent;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonFRFaceMatchingActivityLog extends BaseEvent {
    private String flow;
    private String facialComparisonResult;
    private String allowToContinue;
    private String reason;
    private String customerVersion;
    private String dopaStatus;
    private String updateIal;
}

