package com.tmb.oneapp.customerservice.model.ekycgetbiometric;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EkycGetBiometricRequest {
    @NotNull
    @Schema(description = "Customer", example = "{\"id\":\"1209600070561\",\"type\":\"cid\"}", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("customer")
    private @Valid EkycGetBiometricRequestCustomer customer;
    @Schema(description = "Biometric", example = "{\"method\":\"selfie\",\"source\":\"TMB\"}", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("biometric")
    private @Valid EkycGetBiometricRequestBiometric biometric;
    @Schema(description = "Channel", example = "MIB", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("channel")
    private String channel;
}