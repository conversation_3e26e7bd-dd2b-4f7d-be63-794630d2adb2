package com.tmb.oneapp.customerservice.model.rosccustomerprofile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@ToString
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RoscCustomerModel {
    @JacksonXmlProperty(localName = "rmNo")
    private String rmId;
    @JacksonXmlProperty(localName = "fullName")
    private String fullName;
    @JacksonXmlProperty(localName = "fullNameEng")
    private String fullNameEng;
    @JacksonXmlProperty(localName = "cardType")
    private String cardType;
    @JacksonXmlProperty(localName = "cardNumber")
    private String idCard;
    @JacksonXmlProperty(localName = "persCardExpiredDate")
    private String persCardExpiredDate;
    @JacksonXmlProperty(localName = "persNationality")
    private String persNationality;
    @JacksonXmlProperty(localName = "persNationality2")
    private String persNationality2;
    @JacksonXmlProperty(localName = "custVerifyWarningList")
    private String custVerifyWarningList;
    @JacksonXmlProperty(localName = "persVerifyInfoType")
    private String persVerifyInfoType;
    @JacksonXmlProperty(localName = "persVerifyAddrType")
    private String persVerifyAddrType;
    @JacksonXmlProperty(localName = "custPoliticalPosition")
    private String custPoliticalPosition;
    @JacksonXmlProperty(localName = "custLiveRiskCountry")
    private String custLiveRiskCountry;
    @JacksonXmlProperty(localName = "custIncomeRiskCountry")
    private String custIncomeRiskCountry;
    @JacksonXmlProperty(localName = "custRiskBusinessType")
    private String custRiskBusinessType;
    @JacksonXmlProperty(localName = "persRiskOccupation")
    private String persRiskOccupation;
    @JacksonXmlProperty(localName = "custRiskLevelDefault")
    private String maxRisk;
    @JacksonXmlElementWrapper(useWrapping = false, localName = "caobAccObj")
    @JacksonXmlProperty(localName = "accountObj")
    private List<CaobAccObj> accountObj;
    @JacksonXmlElementWrapper(useWrapping = false, localName = "cudsDepositSource")
    @JacksonXmlProperty(localName = "custDepositSource")
    private List<CudsDepositSource> custDepositSource;
    @JacksonXmlElementWrapper(useWrapping = false, localName = "cinsIncomeSource")
    @JacksonXmlProperty(localName = "custIncomeSource")
    private List<CinsIncomeSource> custIncomeSource;
    @JacksonXmlProperty(localName = "persCountryOfBirth")
    private String persCountryOfBirth;
    @JacksonXmlProperty(localName = "persCitizenAddressCountry")
    private String registerAddressCountry;
    @JacksonXmlProperty(localName = "custIncomeSourceCountry")
    private String custIncomeSourceCountry;
    @JacksonXmlProperty(localName = "persBirthDate")
    private String persBirthDate;
    @JacksonXmlProperty(localName = "persOccupation")
    private String persOccupation;
    @JacksonXmlProperty(localName = "persBusinessType")
    private String persBusinessType;
    @JacksonXmlProperty(localName = "persOfficeAddress")
    private String officeAddress;
    @JacksonXmlProperty(localName = "persCitizenAddress")
    private String registerAddress;
    @JacksonXmlProperty(localName = "persConnectAddress")
    private String contactAddress;
    @JacksonXmlProperty(localName = "persAccObjOther")
    private String persAccObjOther;
    @JacksonXmlProperty(localName = "persDepositSourceOther")
    private String persDepositSourceOther;
    @JacksonXmlProperty(localName = "custNoDRTXN")
    @JsonProperty("cust_no_dr_txn")
    private String custNoDRTXN;
    @JacksonXmlProperty(localName = "custAllDRAMT")
    @JsonProperty("cust_all_dramt")
    private String custAllDRAMT;
    @JacksonXmlProperty(localName = "custNoCRTXN")
    @JsonProperty("cust_no_cr_txn")
    private String custNoCRTXN;
    @JacksonXmlProperty(localName = "custAllCRAMT")
    @JsonProperty("cust_all_cramt")
    private String custAllCRAMT;
    @JacksonXmlProperty(localName = "custIncomeSourceOther")
    private String custIncomeSourceOther;
    @JacksonXmlProperty(localName = "custEstimateIncome")
    private String custEstimateIncome;
}
