package com.tmb.oneapp.customerservice.model.notification;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Data
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PushInquiryModel {

    @Schema(description = "flow", example = "setting",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private String flow;

    @Schema(description = "App level notification flag ",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private boolean isAppLevelPushNotificationEnabled;

    @Schema(description = "News and offers notification flag ",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private boolean isNewsNotificationEnabled;

    @Schema(description = "mobile_language", example = "TH")
    private String mobileLanguage;

    private List<WidgetAudiencePushNotification> widgets;

    @Data
    @ToString
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class WidgetAudiencePushNotification {
        String widgetId;
        Boolean widgetStatus;
    }

}