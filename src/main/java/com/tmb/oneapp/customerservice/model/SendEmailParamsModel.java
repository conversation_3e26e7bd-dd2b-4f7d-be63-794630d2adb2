package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class SendEmailParamsModel {
    @JsonProperty("template_name")
    private String templateName;
    @JsonProperty("custFullNameEN")
    private String custFullNameEN;
    @JsonProperty("custFullNameTH")
    private String custFullNameTH;
    @JsonProperty("custEmail")
    private String custEmail;
    @JsonProperty("applyResultTH")
    private String applyResultTH;
    @JsonProperty("applyResultEN")
    private String applyResultEN;
}
