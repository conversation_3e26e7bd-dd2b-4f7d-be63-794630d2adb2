package com.tmb.oneapp.customerservice.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@NoArgsConstructor
@ToString
public class Terminal {
    private String id;
    private String type;
    private String branchId;
    private String recordId;
    private String pccTraceId;
    private String traceId;
    private String issuerRoutingId;
    private String benRoutingId;
    private String acquirerRoutingId;
    private String reversalCode;
    private String originalType;
    private String responseCode;
    private String pin;
    private String state;
    private String country;
    private String issuerFee;
    private String benFee;
    private String chargeFee;
    private String region;
    private String trackToLength;
    private String trackToData;
    private String routeStat;
    private String productInd;
    private String dpcId;
    private String releaseId;
    private String switchDate;
    private String chequeNo;
    private String senderBranch;
    private String receiverBranch;
    private String senderReferenceNo;
    private String channelId;

}
