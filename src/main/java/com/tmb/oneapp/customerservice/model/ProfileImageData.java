package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

import java.util.Map;

/**
 * /** Class for set profile Image data
 *
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProfileImageData {

	@JsonProperty("image_url")
	private Map<String, String> profileImageUrlData;
}
