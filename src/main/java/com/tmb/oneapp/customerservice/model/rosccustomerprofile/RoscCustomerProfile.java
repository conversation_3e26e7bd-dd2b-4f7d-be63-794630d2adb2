package com.tmb.oneapp.customerservice.model.rosccustomerprofile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.util.List;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RoscCustomerProfile extends RoscCustomerModel {
    @JacksonXmlProperty(localName = "custId")
    private String custId;
    @JacksonXmlProperty(localName = "createROSCDate")
    private String createROSCDate;
    @JacksonXmlProperty(localName = "branchCode")
    private String branchCode;
    @JacksonXmlProperty(localName = "officeCode")
    private String officeCode;
    @JacksonXmlProperty(localName = "serviceSegment")
    private String serviceSegment;
    @JacksonXmlProperty(localName = "staffId")
    private String staffId;
    @JacksonXmlProperty(localName = "zoneCode")
    private String zoneCode;
    @JacksonXmlProperty(localName = "zoneName")
    private String zoneName;
    @JacksonXmlProperty(localName = "regionalCode")
    private String regionalCode;
    @JacksonXmlProperty(localName = "regionalName")
    private String regionalName;
    @JacksonXmlProperty(localName = "custRefusedServ")
    private String custRefusedServ;
    @JacksonXmlProperty(localName = "persOrigin")
    private String persOrigin;
    @JacksonXmlProperty(localName = "custVerifyRuleStatus")
    private String custVerifyRuleStatus;
    @JacksonXmlProperty(localName = "persWorkPermitType")
    private String persWorkPermitType;
    @JacksonXmlProperty(localName = "persWorkPermitTypeOther")
    private String persWorkPermitTypeOther;
    @JacksonXmlProperty(localName = "custCustomerLevel3Desc")
    private String custCustomerLevel3Desc;
    @JacksonXmlProperty(localName = "custCDMAcctOwner")
    private String custCDMAcctOwner;
    @JacksonXmlProperty(localName = "custCDMAcctOwnerDate")
    private String custCDMAcctOwnerDate;
    @JacksonXmlProperty(localName = "custCDMSup1")
    private String custCDMSup1;
    @JacksonXmlProperty(localName = "custCDMSup1Date")
    private String custCDMSup1Date;
    @JacksonXmlProperty(localName = "custCDMSup2")
    private String custCDMSup2;
    @JacksonXmlProperty(localName = "custCDMSup2Date")
    private String custCDMSup2Date;
    @JacksonXmlElementWrapper(useWrapping = false, localName = "committee")
    @JacksonXmlProperty(localName = "comitteeSection")
    private List<Committee> committees;
    @JacksonXmlProperty(localName = "customerStatus")
    private CustStatus custStatus;
}
