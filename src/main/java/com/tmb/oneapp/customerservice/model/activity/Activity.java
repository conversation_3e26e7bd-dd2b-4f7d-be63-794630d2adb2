package com.tmb.oneapp.customerservice.model.activity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@Data
@Getter
@Setter
@AllArgsConstructor
@Builder
@NoArgsConstructor
public class Activity {
    @NotEmpty(message = "Activity type id is required")
    @JsonProperty("activity_type_id")
    private String activityTypeId;
    @JsonProperty("activitydescription")
    private List<ActivityDescription> activitydescription;
    @JsonProperty("activity_status")
    private String activityStatus;
    @Schema(defaultValue = "channel" ,hidden = true)
    @JsonProperty("channel")
    private String channel;
    @Schema(defaultValue = "crmId" ,hidden = true)
    @JsonProperty("crm_id")
    private String crmId;
    @Schema(defaultValue = "correlationId" ,hidden = true)
    @JsonProperty("correlation_id")
    private String correlationId;
    @Schema(defaultValue = "deviceId" ,hidden = true)
    @JsonProperty("device_id")
    private String deviceId;
    @Schema(defaultValue = "ipAddress" ,hidden = true)
    @JsonProperty("ip_address")
    private String ipAddress;
    @JsonProperty("fail_reason")
    private String failReason;
    @Schema(defaultValue = "timestamp" ,hidden = true)
    @JsonProperty("timestamp")
    private Date timestamp;
}