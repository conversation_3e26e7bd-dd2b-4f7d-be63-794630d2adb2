package com.tmb.oneapp.customerservice.model.deposit;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DepositTransactions {
    private String seqnum;
    private String amounts;
    @JsonProperty("start_date")
    private String startdate;
    @JsonProperty("expire_date")
    private String expiredate;
}
