package com.tmb.oneapp.customerservice.model.activity;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.common.model.BaseEvent;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UpdateFrCustomerTypeActivityLog extends BaseEvent {
    String oldCustomerFrType;
    String newCustomerFrType;

    public UpdateFrCustomerTypeActivityLog (String correlationId, String activityDate, String activityTypeId) {
        super(correlationId, activityDate, activityTypeId);
    }

}
