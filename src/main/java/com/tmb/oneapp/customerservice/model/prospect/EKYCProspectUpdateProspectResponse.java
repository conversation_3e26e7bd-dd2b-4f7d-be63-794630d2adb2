package com.tmb.oneapp.customerservice.model.prospect;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EKYCProspectUpdateProspectResponse extends EKYCProspectSearchProspectResponse{
    private String prospectId;
    private String citizenId;
    private String channel;
}
