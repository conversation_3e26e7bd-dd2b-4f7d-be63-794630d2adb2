package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class RootFirstNameLastNameResponseData {
    @JsonProperty("status")
    public FirstNameLastNameStatus status;
    @JsonProperty("data")
    public DataFirstNameLastNameEmail data;
    @JsonProperty("customers")
    public List<CustomerFirstNameLastName> customers;
}
