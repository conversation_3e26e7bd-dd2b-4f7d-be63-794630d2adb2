package com.tmb.oneapp.customerservice.model.personalizedinformation.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PersonalizedInformationRequest {
    private String personalizedName;
    private String personalizedPictureId;
    private String personalizedMailId;
    private String personalizedMobileNumber;
    private String personalizedFacebookId;
    private String personalizedStatus;
    private String ownFlag;
    private String favoriteFlag;
}
