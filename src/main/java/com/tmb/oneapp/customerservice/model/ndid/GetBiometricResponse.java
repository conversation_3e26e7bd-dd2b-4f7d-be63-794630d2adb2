package com.tmb.oneapp.customerservice.model.ndid;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class GetBiometricResponse {

    private String statusCode;
    private String statusDesc;
    private String remark;
    private String initialVector;
    private String data;

    @Data
    @ToString
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class NDIDProspectsBiometricsData
    {
        private String biometricData;
        private String biometricDataMethod;
        private String biometricDataFormat;
        private String biometricDataSource;
        private String timestamp;
        private String channel;
    }

    @Data
    @ToString
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class ForeignerBiometricsData
    {
        private String passportImage;
    }
}
