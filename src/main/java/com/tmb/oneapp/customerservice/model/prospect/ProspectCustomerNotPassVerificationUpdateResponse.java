package com.tmb.oneapp.customerservice.model.prospect;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProspectCustomerNotPassVerificationUpdateResponse {

    @JsonProperty("prospect_id")
    private String prospectId;
    @JsonProperty("not_pass_qa_reason")
    private String prospectNotPassQaReason;
    @JsonProperty("not_pass_qa_update_date")
    private Long prospectNotPassQaUpdateDate;
    @JsonProperty("not_pass_qa_empid")
    private String prospectNotPassQaEmpId;
}
