package com.tmb.oneapp.customerservice.model.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@NoArgsConstructor
@ToString
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UpdateDetailsRequest {
	
	@Schema(example = "heart")
	private String action;

	@JsonProperty("heart_flag")
	@Schema(example = "Y")
	private String heartFlag;
	
	@Schema(example = "11", requiredMode = Schema.RequiredMode.REQUIRED)
	private String favoriteId;
	
	@Schema(example = "**********", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String personalizedId;
	
	@Schema(example = "add_favorite_awedxxxxx", requiredMode = Schema.RequiredMode.REQUIRED)
	private String cacheKey;
	
	@Schema(example = "accounts/promptpay", requiredMode = Schema.RequiredMode.REQUIRED)
	private String module;
	 

}
