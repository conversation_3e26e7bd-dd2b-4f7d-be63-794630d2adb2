package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Accessors(chain = true)
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PostNDIDGetConsentStatusResponse {
    private EkycNDIDGetConsentStatusCustomer customer;
    private List<EkycNDIDGetConsentStatusRequestList> requestList;
    private String referenceId;

}
