package com.tmb.oneapp.customerservice.model.prospect;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EKYCGetProspectByNameEngQuery {
	@JsonProperty("firstname_eng")
	private String firstNameEng;
	@JsonProperty("lastname_eng")
	private String lastNameEng;

}
