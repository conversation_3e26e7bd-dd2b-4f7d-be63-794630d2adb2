package com.tmb.oneapp.customerservice.model.activity;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CustomerHistoryActivityResponse {
    private String activityId;
    private long activityDate;
    private String activityType;
    private String customerStatus;
    private String employeeId;
    private String reason;
}
