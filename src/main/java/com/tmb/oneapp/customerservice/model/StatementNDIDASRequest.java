package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class StatementNDIDASRequest {
    private String requestId;
    private String startDate;
    private String endDate;
    private List<String> accounts;
    private String paymentAccount;
	private String transactionRefId;
    private String auxiliaryReferenceId;

}
