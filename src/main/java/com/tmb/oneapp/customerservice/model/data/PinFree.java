package com.tmb.oneapp.customerservice.model.data;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PinFree {
	@Schema(example="Y",requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("pin_free_flag")
	private String pinFreeFlag;
	
	@Schema(example="1000",requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("pin_free_tr_limit")
	private Integer pinFreeTRLimit;
	
	@Schema(example="2000",requiredMode = Schema.RequiredMode.REQUIRED)
	@JsonProperty("pin_free_bp_limit")
	private Integer pinFreeBPLimit;
}
