package com.tmb.oneapp.customerservice.model.prospect;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import jakarta.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProspectCustomerSearchRequest {


    @NotBlank
    @Schema(description = "search type as mobile_no or citizen_id or name-eng or name-thai ", example = "mobile_no",requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("prospect_search_type")
    private String prospectSearchType;

    @NotBlank
    @Schema(description = "search value as 09232322222 or 1209600070570 or Beakhyun or พัชรินทร์ ", example = "09232322222",requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("prospect_search_value")
    private String prospectSearchValue;

    @Schema(description = "search value1 as Park, it should use only for customer search by last name eng or last name thai  ", example = "Park")
    @JsonProperty("prospect_search_value1")
    private String prospectsearchValue1;

}
