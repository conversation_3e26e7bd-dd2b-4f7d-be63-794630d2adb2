package com.tmb.oneapp.customerservice.model.activity;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.common.model.BaseEvent;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FRVerifyActivityLog extends BaseEvent {
    private String originalFlow;
    private String customerType;
    private String allowCustomerForeigner;
    private String frServiceSetup;
    private String currentCustomerStatus;
    private String frCustomerType;
    private String frFeature;
    private String frFlag;
    private String asPerCustomerStatus;
    private String customerIal;
    private String ekycFlag;
    private String imageDipChip;
    private String deviceId;
    private String customerVersion;
    private String frReason;
}
