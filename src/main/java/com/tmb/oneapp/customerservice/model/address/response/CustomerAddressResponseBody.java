package com.tmb.oneapp.customerservice.model.address.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerAddressResponseBody {

    @JsonProperty(value = "ident_employment")
    private String identEmployment;

    @JsonProperty(value = "addr_seq")
    private String addrSeq;

    @JsonProperty(value = "regis_addr_flag")
    private String regisAddrFlag;

    @JsonProperty(value = "curr_addr_flag")
    private String currAddrFlag;

    @JsonProperty(value = "work_addr_flag")
    private String workAddrFlag;

    @JsonProperty(value = "working_place")
    private String workingPlace;

    @JsonProperty(value = "address_no")
    private String addressNo;

    @JsonProperty(value = "sub_district")
    private String subDistrict;

    @JsonProperty(value = "district")
    private String district;

    @JsonProperty(value = "province")
    private String province;

    @JsonProperty(value = "postal_code")
    private String postalCode;

    @JsonProperty(value = "accom_owner_type")
    private String accomOwnerType;

    @JsonProperty(value = "country")
    private String country;

    @JsonProperty(value = "system_create")
    private String systemCreate;

    @JsonProperty(value = "create_date")
    private String createDate;

    @JsonProperty(value = "create_by")
    private String createBy;

    @JsonProperty(value = "update_date")
    private String updateDate;

    @JsonProperty(value = "update_by")
    private String updateBy;

}
