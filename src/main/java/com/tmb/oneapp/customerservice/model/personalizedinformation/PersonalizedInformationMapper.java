package com.tmb.oneapp.customerservice.model.personalizedinformation;

import com.tmb.oneapp.customerservice.model.personalizedinformation.entity.PersonalizedInformation;
import com.tmb.oneapp.customerservice.model.personalizedinformation.model.PersonalizedInformationRequest;
import com.tmb.oneapp.customerservice.model.personalizedinformation.model.PersonalizedInformationResponse;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PersonalizedInformationMapper {
    PersonalizedInformationMapper INSTANCE = Mappers.getMapper(PersonalizedInformationMapper.class);
    PersonalizedInformationResponse toPersonalizedInformationResponse(PersonalizedInformation personalizedInformation);
    List<PersonalizedInformationResponse> toPersonalizedInformationResponseList(List<PersonalizedInformation> personalizedInformationList);
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
    PersonalizedInformation toPersonalizedInformation(@MappingTarget PersonalizedInformation personalizedInformation, PersonalizedInformationRequest personalizedInformationRequest);
}
