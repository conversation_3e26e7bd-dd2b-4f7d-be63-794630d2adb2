package com.tmb.oneapp.customerservice.model.rosccustomerprofile.maintenance;


import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class Header {
    @JacksonXmlProperty(localName = "reqId")
    private String reqId;
    @JacksonXmlProperty(localName = "resCode")
    private String resCode;
    @JacksonXmlProperty(localName = "resDesc")
    private String resDesc;
    @JacksonXmlProperty(localName = "productCode")
    private String productCode;
    @JacksonXmlProperty(localName = "acronym")
    private String acronym;
}
