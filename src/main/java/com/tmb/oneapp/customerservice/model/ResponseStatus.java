package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@JsonPropertyOrder({
        "code",
        "description"
})
@Getter
@Setter
@NoArgsConstructor
public class ResponseStatus {

    @JsonProperty("code")
    private String code;
    @JsonProperty("description")
    private String description;
}
