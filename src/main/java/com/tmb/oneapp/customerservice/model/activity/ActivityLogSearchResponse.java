package com.tmb.oneapp.customerservice.model.activity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ActivityLogSearchResponse {
    private long activityDate;
    private String ipAddress;
    private String activityType;
    private String result;
    private String reason;
    private String channel;
    private String employeeId;
    private String activityDetail;
    private String appName;
}
