package com.tmb.oneapp.customerservice.model.data;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Data
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FavDetailsResponse {
	
	private String imageUrl;
	private String favoriteNickname;
	private String favoriteType;
	private String favoriteNumber;
	private Double favoriteAmount;
	private String favNote;
	private String bankCd;
	private Integer categoryId;
	private String createDate;
	private String heartFlag;
	private String billerCompcode;
	private String ref1;
	private String ref2;
	private Integer billerId;
	private String favoriteName;
	private String personalizedId;
}
