package com.tmb.oneapp.customerservice.model.ekycverifybiometric;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tmb.oneapp.customerservice.model.ekycgetbiometric.EkycGetBiometricRequestCustomer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EkycVerifyBiometricRequest {
    @NotNull
    @Schema(description = "Customer", example = "{\"id\":\"1209600070561\",\"type\":\"cid\"}", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("customer")
    private @Valid EkycGetBiometricRequestCustomer customer;
    @Schema(description = "Verification Method", example = "face", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("verification_method")
    private String verificationMethod;
    @Schema(description = "Biometric", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("biometric")
    private @Valid List<EkycVerifyBiometricRequestBiometric> biometric;
    @Schema(description = "Channel", example = "MIB", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("channel")
    private String channel;
    @Schema(description = "Action Type", example = "OTHER", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("action_type")
    private String actionType;
    @Schema(description = "Action Type Detail", example = "Change Daily Limit, Fund Transfer, Uplift, NDID Enrollment, IDP Authentication")
    @JsonProperty("action_type_detail")
    private String actionTypeDetail;
}