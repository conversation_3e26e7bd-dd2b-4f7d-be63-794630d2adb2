package com.tmb.oneapp.customerservice.model.commonfr;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "FACE_RECOGNITION_FEATURE", schema = "CUSTCRMUSR")
public class FaceRecognitionFeature implements Serializable {
    @Id
    @Column(name = "FEATURE_ID")
    private Integer featureId;

    @Column(name = "FEATURE_NAME")
    private String featureName;

    @Column(name = "FEATURE_DESC")
    private String featureDesc;

    @Column(name = "VISIBLE_STATUS")
    private String visibleStatus;

    @Column(name = "CREATED_DATE")
    private Date createdDate;

    @Column(name = "UPDATED_DATE")
    private Date updatedDate;

    @Column(name = "FEATURE_KEY")
    private String featureKey;

    @Column(name = "BOT_POLICY_FLAG")
    private String botPolicyFlag;

    @Column(name = "NORMAL_SETTING")
    private String normalSetting;

    @Column(name = "BLIND_SETTING")
    private String blindSetting;

    @Column(name = "ABROAD_SETTING")
    private String abroadSetting;

    @Column(name = "NORMAL_FOREIGNER_SETTING")
    private String normalForeignerSetting;
}
