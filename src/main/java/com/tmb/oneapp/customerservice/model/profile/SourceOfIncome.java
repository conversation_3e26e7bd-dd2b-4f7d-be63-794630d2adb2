package com.tmb.oneapp.customerservice.model.profile;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "ident_employment", "src_of_inc_code" })
@Data
public class SourceOfIncome {

	@JsonProperty("ident_employment")
	public String identEmployment;
	@JsonProperty("src_of_inc_code")
	public String srcOfIncCode;
	@JsonProperty("update_by")
	public String updateBy;

}