package com.tmb.oneapp.customerservice.model.editprofile;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class AdditionalKycProfile{
    @JsonProperty("kyc_review_last_mtn_date")
    public String kycReviewLastMtnDate;
    @JsonProperty("kyc_last_review_date")
    public String kycLastReviewDate;
    @JsonProperty("kyc_review_last_mtn_user")
    public String kycReviewLastMtnUser;
    @JsonProperty("kyc_review_last_mtn_channel")
    public String kycReviewLastMtnChannel;
    @JsonProperty("update_by")
    public String updateBy;

}
