package com.tmb.oneapp.customerservice.model.rosccustomerprofile;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
@Getter
@Setter
@ToString
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class Committee {
    @JacksonXmlProperty(localName = "commId")
    private String commId;
    @JacksonXmlProperty(localName = "commRMNo")
    private String commRmId;
    @JacksonXmlProperty(localName = "commCommitteeFlag")
    private String commCommitteeFlag;
    @JacksonXmlProperty(localName = "commCustomerRelCD")
    private String commCustomerRelCD;
}
