package com.tmb.oneapp.customerservice.model.personalizedacctrelation.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.Date;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PersonalizedAcctRelationResponse {
    private String crmId;
    private String personalizedId;
    private String personalizedAcctId;
    private String bankCd;
    private String personalizedAcctNickName;
    private String personalizedAcctStatus;
    private String acctFavoriteFlag;
    private Date createDate;
    private Date updateDate;
    private String personalizedAcctName;
    private String jointFlag;
    private String appId;
    private String heartFlag;
    private String switchToAllfree;
    private Date switchDate;
    private String personalizedDisplayAcctStatus;
    private Integer personalizedAcctSortOrder;
    private String viewOnlyFlag;
}
