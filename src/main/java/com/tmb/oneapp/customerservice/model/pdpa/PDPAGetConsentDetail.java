package com.tmb.oneapp.customerservice.model.pdpa;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PDPAGetConsentDetail {
    private String typeOfConsent;
    private String flag;
    private String versionNumber;
    private String channel;
    private String userId;
    private String systemDate;
    private String signupDate;
    private String needUpdateFlag;
    private String needUpdateFlagReason;
}
