package com.tmb.oneapp.customerservice.model.favorite.interfaceobject;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CustomerBillerResponse {
    private String favoriteId;
    private String favoriteNickname;
    private String favoriteType;
    private String favoriteNumber;
    private String favoriteAmount;
    private String favoriteNote;
    private String heartFlag;
    private String pictureId;
    private String imageUrl;
    private String defaultImageUrl;
    private String displayOrder;
    private String bankCd;
    private String categoryId;
    private String billerType;
    private String billerId;
    private String billerCompcode;
    private String ref1;
    private String ref2;
    private String ownFlag;
    @JsonProperty("from_acct_id")
    private String fromAccountId;
}
