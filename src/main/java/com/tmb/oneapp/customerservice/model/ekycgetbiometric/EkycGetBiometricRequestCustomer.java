package com.tmb.oneapp.customerservice.model.ekycgetbiometric;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EkycGetBiometricRequestCustomer {
    @NotNull
    @Schema(description = "id", example = "1209600070561", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("id")
    private String id;
    @Schema(description = "type", example = "cid", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("type")
    private String type;
}