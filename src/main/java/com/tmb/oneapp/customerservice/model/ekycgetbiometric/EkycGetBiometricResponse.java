package com.tmb.oneapp.customerservice.model.ekycgetbiometric;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.tmb.oneapp.customerservice.model.EKYCGetImageETEStatus;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class EkycGetBiometricResponse {
    @Schema(description = "Status", example = "code:'', Message:''", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("status")
    private EKYCGetImageETEStatus status;
    @NotNull
    @Schema(description = "Biometric", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("biometric")
    private @Valid EkycGetBiometricResponseBiometric biometric;
}