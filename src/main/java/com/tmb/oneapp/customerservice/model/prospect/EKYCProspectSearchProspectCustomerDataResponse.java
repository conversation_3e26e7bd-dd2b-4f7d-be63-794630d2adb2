package com.tmb.oneapp.customerservice.model.prospect;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EKYCProspectSearchProspectCustomerDataResponse extends EKYCProspectSearchProspectResponse {
    @JsonProperty("prospect_id")
    private String prospectId;
    @JsonProperty("channel")
    private String channel;
    @JsonProperty("customer")
    private ProspectSearchFlowState customer;

}
