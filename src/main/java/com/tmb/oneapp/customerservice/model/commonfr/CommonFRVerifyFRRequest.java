package com.tmb.oneapp.customerservice.model.commonfr;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonFRVerifyFRRequest {
    @NotBlank(message = "uuid may not be blank")
    private String uuid;
    @NotNull(message = "feature_id may not be null")
    private Integer featureId;
    @NotBlank(message = "flow may not be blank")
    private String flow;
}
