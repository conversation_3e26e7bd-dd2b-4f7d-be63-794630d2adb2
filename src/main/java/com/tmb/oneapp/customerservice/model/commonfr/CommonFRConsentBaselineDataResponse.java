package com.tmb.oneapp.customerservice.model.commonfr;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonFRConsentBaselineDataResponse {
    private String consentName;
    private String consentVersion;
    private String shortConsentTH;
    private String shortConsentEN;
    private String longConsentTH;
    private String longConsentEN;
}
