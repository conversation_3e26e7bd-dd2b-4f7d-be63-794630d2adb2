package com.tmb.oneapp.customerservice.model.ekycverifybiometric;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EkycVerifyBiometricResponseBiometricResult {
    @NotNull
    @Schema(description = "Code", example = "-1", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("code")
    private String code;
    @Schema(description = "Description", example = "Fail", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("description")
    private String description;
    @Schema(description = "Score", example = "0", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("score")
    private String score;
    @Schema(description = "IDP", example = "0", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("idp")
    private String idp;
    @Schema(description = "Verification Reference", example = "cd8a7d59-c2c9-4862-8095-58e0b44487b9", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("verification_reference")
    private String verificationReference;
}