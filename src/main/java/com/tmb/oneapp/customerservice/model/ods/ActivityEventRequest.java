package com.tmb.oneapp.customerservice.model.ods;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.micrometer.core.lang.NonNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class ActivityEventRequest {
		@NonNull
		@JsonProperty("crm_id")
		private String crmId;
		@JsonProperty("correlation_id")
		private String correlationId;
		
		// Internal Field for update notification settings
		@JsonProperty("oneapp")
		private String oneappFlag;
		@JsonProperty("ib_status")
		private String ibStatus;
		@JsonProperty("eb_status")
		private String ebStatus;
		@JsonProperty("mb_status")
		private String mbStatus;
}
