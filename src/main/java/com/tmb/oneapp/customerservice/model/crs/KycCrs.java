package com.tmb.oneapp.customerservice.model.crs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class KycCrs {
    @JsonProperty("crs_answer_dt")
    public String crsAnswerDt;
    @JsonProperty("crs_ps_item_no1")
    public String crsPsItemNo1;
    @JsonProperty("create_by")
    public String createBy;
    @JsonProperty("app_id_create")
    public String appIdCreate;
}
