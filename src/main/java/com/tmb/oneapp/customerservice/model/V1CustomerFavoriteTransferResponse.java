package com.tmb.oneapp.customerservice.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class V1CustomerFavoriteTransferResponse {
    private String personalizedId;
    private String favoriteId;
    private Integer favoriteNo;
    private String favoriteNickname;
    private String favoriteAccNo;
    private String bankPromptpay;
    private String favoriteAccName;
    private String fromAccNo;
    private String fromAccName;
    private Double favoriteAmt;
    private Date addedDate;
    private String heartFlag;
    private String note;
    private String pictureId;
    private String favoriteType;
    private String bankCd;
    private String bankLogoImage;
    private String categoryId;
    private String categoryName;
    private String imageUrl;
    private String defaultImageUrl;
    private String ownFlag;


}
