package com.tmb.oneapp.customerservice.model.personalizedacctrelation.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AccountSettingResponseModel {
    private String personalizedAcctId;
    private String personalizedAcctNickname;
    private String personalizedAcctStatus;
    private String personalizedDisplayAcctStatus;
    private Integer personalizedAcctSortOrder;
    private String viewOnlyFlag;
}
