package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TransactionDailyLimitHistoryResponse {
	@JsonProperty("request_id")
	private String requestId;
	@JsonProperty("date_time")
	private String dateAndTime;
	@JsonProperty("type")
	private String type;
	@JsonProperty("request_limit")
	private Double requestLimit;
	@JsonProperty("status")
	private String status;
	@JsonProperty("reason")
	private String reason;
	@JsonProperty("last_updated_user")
	private String lastUpdatedUser;
	@JsonProperty("user_role")
	private String userRole;
	@JsonProperty("last_updated_date_time")
	private String lastUpdatedDateTime;

}