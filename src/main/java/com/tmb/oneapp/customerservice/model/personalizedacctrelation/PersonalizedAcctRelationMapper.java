package com.tmb.oneapp.customerservice.model.personalizedacctrelation;

import com.tmb.oneapp.customerservice.model.personalizedacctrelation.entity.PersonalizedAcctRelationEntity;
import com.tmb.oneapp.customerservice.model.personalizedacctrelation.model.AccountSettingResponseModel;
import com.tmb.oneapp.customerservice.model.personalizedacctrelation.model.PersonalizedAcctRelationRequest;
import com.tmb.oneapp.customerservice.model.personalizedacctrelation.model.PersonalizedAcctRelationResponse;
import com.tmb.oneapp.customerservice.model.personalizedinformation.entity.PersonalizedInformation;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PersonalizedAcctRelationMapper {
    PersonalizedAcctRelationMapper INSTANCE = Mappers.getMapper(PersonalizedAcctRelationMapper.class);
    @Mapping(target = "personalizedId", source = "personalizedInformation.personalizedId")
    List<PersonalizedAcctRelationResponse> toDTOList(List<PersonalizedAcctRelationEntity> personalizedAcctRelationEntity);
    @Mapping(source = "personalizedInformation.personalizedId", target = "personalizedId")
    PersonalizedAcctRelationResponse toDTO(PersonalizedAcctRelationEntity personalizedAcctRelationEntity);
    AccountSettingResponseModel toAccountSettingDTO(PersonalizedAcctRelationEntity personalizedAcctRelationEntity);
    List<AccountSettingResponseModel> toAccountSettingDTOList(List<PersonalizedAcctRelationEntity> personalizedAcctRelationEntity);

    @Mapping(source = "personalizedAcctRelationRequest.appId", target = "appId")
    @Mapping(source = "personalizedInformation", target = "personalizedInformation")
    @Mapping(target = "personalizedAcctId", ignore = true)
    @Mapping(target = "createDate", ignore = true)
    @Mapping(target = "updateDate", ignore = true)
    PersonalizedAcctRelationEntity toEntity(PersonalizedInformation personalizedInformation, PersonalizedAcctRelationRequest personalizedAcctRelationRequest);

    @Mapping(source = "personalizedAcctRelationRequest.appId", target = "appId")
    @Mapping(source = "personalizedInformation", target = "personalizedInformation")
    @Mapping(target = "personalizedAcctId", ignore = true)
    @Mapping(target = "createDate", ignore = true)
    @Mapping(target = "updateDate", ignore = true)
    PersonalizedAcctRelationEntity toEntity(@MappingTarget PersonalizedAcctRelationEntity personalizedAcctRelationEntity, PersonalizedInformation personalizedInformation, PersonalizedAcctRelationRequest personalizedAcctRelationRequest);

    @AfterMapping
    default void handleAccounts(PersonalizedAcctRelationRequest personalizedAcctRelationRequest, @MappingTarget PersonalizedAcctRelationEntity personalizedAcctRelationEntity) {
        if (ObjectUtils.isNotEmpty(personalizedAcctRelationRequest) && StringUtils.isEmpty(personalizedAcctRelationEntity.getPersonalizedAcctId())) {
            personalizedAcctRelationEntity.setPersonalizedAcctId(personalizedAcctRelationRequest.getPersonalizedAcctId());
        }
    }
}
