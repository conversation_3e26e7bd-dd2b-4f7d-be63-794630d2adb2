package com.tmb.oneapp.customerservice.model.profile;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Data;

import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "ident_employment", "rm_id", "involvedpartytype", "customer_type", "id_type", "id_no",
		"id_released_date", "id_expire_date", "id_birth_date", "nationality", "gender", "marital_status", "no_of_child",
		"first_name_thai", "last_name_thai", "first_name_eng", "last_name_eng", "occupation_code", "business_type_desc",
		"education_code", "business_type_code", "customer_level", "customer_status", "salary", "country_of_income",
		"sales_volume_amt", "no_of_employee", "create_date", "create_by", "last_update", "update_by", "system_owner",
		"full_fill_flag", "working_place", "middle_name_thai", "middle_name_eng", "title_code","employment_status","employment_characteristic","place_of_birth"})
@Data
public class Kyc {

	@JsonProperty("ident_employment")
	public String identEmployment;
	@JsonProperty("rm_id")
	public String rmId;
	@JsonProperty("involvedpartytype")
	public String involvedpartytype;
	@JsonProperty("customer_type")
	public String customerType;
	@JsonProperty("id_type")
	public String idType;
	@JsonProperty("id_no")
	public String idNo;
	@JsonProperty("id_released_date")
	public String idReleasedDate;
	@JsonProperty("id_expire_date")
	public String idExpireDate;
	@JsonProperty("id_birth_date")
	public String idBirthDate;
	@JsonProperty("nationality")
	public String nationality;
	@JsonProperty("gender")
	public String gender;
	@JsonProperty("marital_status")
	public String maritalStatus;
	@JsonProperty("no_of_child")
	public Integer noOfChild;
	@JsonProperty("first_name_thai")
	public String firstNameThai;
	@JsonProperty("last_name_thai")
	public String lastNameThai;
	@JsonProperty("first_name_eng")
	public String firstNameEng;
	@JsonProperty("last_name_eng")
	public String lastNameEng;
	@JsonProperty("occupation_code")
	public String occupationCode;
	@JsonProperty("business_type_desc")
	public String businessTypeDesc;
	@JsonProperty("education_code")
	public String educationCode;
	@JsonProperty("business_type_code")
	public String businessTypeCode;
	@JsonProperty("customer_level")
	public String customerLevel;
	@JsonProperty("customer_status")
	public String customerStatus;
	@JsonProperty("salary")
	public Integer salary;
	@JsonProperty("country_of_income")
	public String countryOfIncome;
	@JsonProperty("sales_volume_amt")
	public BigDecimal salesVolumeAmt;
	@JsonProperty("no_of_employee")
	public Integer noOfEmployee;
	@JsonProperty("create_date")
	public String createDate;
	@JsonProperty("create_by")
	public String createBy;
	@JsonProperty("last_update")
	public String lastUpdate;
	@JsonProperty("update_by")
	public String updateBy;
	@JsonProperty("system_owner")
	public String systemOwner;
	@JsonProperty("full_fill_flag")
	public String fullFillFlag;
	@JsonProperty("working_place")
	public String workingPlace;
	@JsonProperty("middle_name_thai")
	public String middleNameTh;
	@JsonProperty("middle_name_eng")
	public String middleNameEn;
	@JsonProperty("start_work_date")
	public String startWorkDate;
	@JsonProperty("title_code")
	public String titleCode;
	@JsonProperty("employment_status")
	public String employmentStatus;
	@JsonProperty("employment_characteristic")
	public String employmentCharacteristic;
	@JsonProperty("place_of_birth")
	public String placeOfBirth;
}