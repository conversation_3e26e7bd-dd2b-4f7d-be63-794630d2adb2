package com.tmb.oneapp.customerservice.model.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DailyLimits {

    @Schema(example = "1900000", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("domestic_daily_limit")
    private String domesticLimit;

    @Schema(example = "2000000", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("international_daily_limit")
    private String internationalLimit;

    @Schema(example = "2000000", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("billpay_max_limit")
    private String billpayMaxLimit;

    @Schema(example = "2000000", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("cardless_max_limit")
    private String cardlessMaxLimit;

    @Schema(example = "Y", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("domestic_daily_limit_flag")
    private String domesticLimitFlag;

    @Schema(example = "Y", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("international_daily_limit_flag")
    private String internationalLimitFlag;

    @Schema(example = "Y", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("billpay_max_limit_flag")
    private String billpayMaxLimitFlag;

    @Schema(example = "Y", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("cardless_max_limit_flag")
    private String cardlessMaxLimitFlag;

}
