package com.tmb.oneapp.customerservice.model.commonfr;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonFRSuccessItem {
    private String uuid;
    @JsonProperty("commonfr_success")
    private String commonFRSuccess;
    private String createDate;
    private String frStatusCode;
}
