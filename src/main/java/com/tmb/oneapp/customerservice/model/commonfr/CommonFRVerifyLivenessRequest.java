package com.tmb.oneapp.customerservice.model.commonfr;


import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonFRVerifyLivenessRequest {
    @NotBlank(message = "selfie_image may not be blank")
    private String selfieImage;
    @NotBlank(message = "liveness_ref_id may not be blank")
    private String livenessRefId;
    @NotBlank(message = "image_hash may not be blank")
    private String imageHash;
    private String noFirestoreData;
}
