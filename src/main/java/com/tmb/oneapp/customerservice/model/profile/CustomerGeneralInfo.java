package com.tmb.oneapp.customerservice.model.profile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "customer" })
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerGeneralInfo {

	@JsonProperty("customer")
	public Customer customer;

}