package com.tmb.oneapp.customerservice.model;

import lombok.Data;
import lombok.ToString;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;

@Entity
@Data
@ToString
@Table(name = "SERVICE_REQUEST_LOG_INFO", schema = "CRMADM")
public class ServiceRequestLogInfoEntity {

    @Id
    @Column(name = "SR_LOG_ID")
    private String srLogId;

    @Column(name = "CRM_ID")
    private String crmId;

    @Column(name = "P2P_ID")
    private String p2pId;

    @Column(name = "SERVICE_REQUEST_DATE")
    private Date serviceRequestDate;

    @Column(name = "CHANNEL_ID")
    private String channelId;

    @Column(name = "ACTIVITY_TYPE_ID")
    private String activityTypeId;

    @Column(name = "ACTIVITY_FLEX_VALUES")
    private String activityFlexValues;

    @Column(name = "SERVICE_REQUEST_STATUS")
    private String serviceRequestStatus;

    @Column(name = "EMP_ID_MAKER")
    private String empIdMaker;

    @Column(name = "EMP_ID_CHECKER")
    private String empIdChecker;

    @Column(name = "EMP_ID_ANOTHER")
    private String empIdAnother;

    @Column(name = "NOTE")
    private String note;

    @Column(name = "CREATE_DATE")
    private Date createdDate;

    @Column(name = "UPDATE_DATE")
    private Date updateDate;

    @Column(name = "ACTIVITY_REF_ID")
    private String activityRefId;

    @Column(name = "BRANCH_CD")
    private String branchCd;

    @Column(name = "VERIFY_CHANNEL")
    private Integer verifyChannel;

    @Column(name = "EMP_ROLE_CHECKER")
    private String empRoleChecker;


}
