package com.tmb.oneapp.customerservice.model.ndid;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PostBiometricComparisonResponse {

    private String statusCode;
    private String statusDesc;
    private String remark;
    private String verificationRefId;
    private String resultCode;
    private String resultDesc;
    private String score;
    private ResultDetail resultDetail;

    @Data
    @ToString
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class ResultDetail {
        @JsonProperty("liveness_check")
        private String liveNessCheck;
        private String duplicateImageCheck;
        private String scoreCheckString;
    }
}

