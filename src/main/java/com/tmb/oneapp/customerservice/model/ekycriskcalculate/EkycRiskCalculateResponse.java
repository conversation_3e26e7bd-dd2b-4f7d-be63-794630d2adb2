package com.tmb.oneapp.customerservice.model.ekycriskcalculate;


import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.customerservice.model.EKYCCalRiskETEBodyMessageSection;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EkycRiskCalculateResponse {

    @JsonAlias("maxRisk")
    private String maxRisk;

    @JsonAlias("maxRiskRM")
    private String maxRiskRM;

    private String riskDesc;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonAlias("messageSection")
    private List<EKYCCalRiskETEBodyMessageSection> messageSection;
}