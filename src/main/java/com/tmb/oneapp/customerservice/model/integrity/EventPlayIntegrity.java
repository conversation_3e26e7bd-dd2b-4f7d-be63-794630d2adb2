package com.tmb.oneapp.customerservice.model.integrity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventPlayIntegrity {
    String eventName;
    String crmId;
    String deviceModel;
    String newLocalTransactionLimitIncludesTopUp;
    String newMaxLocalTransactionLimitIncludeTopUpHistory;
    String oldLocalTransactionLimitIncludesTopUp;
    String oldMaxLocalTransactionLimitIncludesTopUpHistory;
    String oldCardlessMaxDailyLimit;
    String newCardlessMaxDailyLimit;
    String oldCardlessMaxLimitHistory;
    String newCardlessMaxLimitHistory;
    String oldBillpayMaxDailyLimit;
    String newBillpayMaxDailyLimit;
    String oldBillpayMaxLimitHistory;
    String newBillpayMaxLimitHistory;
    String reasonToChange;
    String reasonFailed;
}
