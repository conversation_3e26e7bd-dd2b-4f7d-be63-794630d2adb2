package com.tmb.oneapp.customerservice.model.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class UpdateCustomerDailyLimitGroupRequest {

    @NotNull
    @Schema(example = "001100000000000000000001184383",
            requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("crm_id")
    private String crmId;

    @NotNull
    @Schema(example = "S",
            requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("group_id")
    private String group;

    @Schema(example = "ttba")
    @JsonProperty("channel")
    private String channel;

}
