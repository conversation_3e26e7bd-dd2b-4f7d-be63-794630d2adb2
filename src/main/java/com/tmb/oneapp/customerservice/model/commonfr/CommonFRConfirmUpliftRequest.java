package com.tmb.oneapp.customerservice.model.commonfr;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonFRConfirmUpliftRequest {
    @NotBlank(message = "flow must not be null/blank")
    private String flow;
    private String oldIal;
    private String oldEkyc;
    @NotNull(message = "feature_id must not be null")
    private Integer featureId;
    private String customerVersion;
}
