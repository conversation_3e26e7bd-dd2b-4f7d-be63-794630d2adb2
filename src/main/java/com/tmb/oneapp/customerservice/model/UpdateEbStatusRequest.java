package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * This object using for update EB, MB, IB status
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateEbStatusRequest {
    @NotBlank
    @Pattern(message = "status must be a number", regexp="^[0-9]{30}$")
    @Schema(description = "crmId",example = "001100000000000000000012027065", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("crm_id")
    private String crmId;

    @Pattern(message = "status must be a number", regexp="^[0-9]*$")
    @NotBlank
    @Schema(description = "Customer status",example = "08", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("customer_status")
    private String status;

    @Pattern(message = "status must be EB, MB, IB", regexp="^[EMI]B$")
    @Schema(description = "status_type", example = "IB",requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("status_type")
    private String statusType;

    @NotBlank
    @Size(max = 250, message = "reason must be 250 characters")
    @Schema(description = "reason",example = "test IB, MB", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("change_status_reason")
    private String reason;

}
