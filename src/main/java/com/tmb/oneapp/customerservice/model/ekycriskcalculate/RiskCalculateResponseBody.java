package com.tmb.oneapp.customerservice.model.ekycriskcalculate;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
@Getter
@Setter
@ToString
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RiskCalculateResponseBody {
    @JsonAlias("maxRisk")
    private String maxRisk;
    @JsonAlias("maxRiskRM")
    private String maxRiskRM;
    @JsonAlias("persSeqKey")
    private String persSeqKey;
    @JsonAlias("shortMsgDesc")
    private String shortMsgDesc;
    @JsonAlias("statusSwf")
    private String statusSwf;
    @JsonAlias("statusSwfPers")
    private String statusSwfPers;
    @JsonAlias("sourceOfRisk")
    private String sourceOfRisk;
    @JsonAlias("resCode1")
    private String resCode1;
    @JsonAlias("resDesc1")
    private String resDesc1;
    @JsonAlias("resCode2")
    private String resCode2;
    @JsonAlias("resDesc2")
    private String resDesc2;
    @JsonAlias("resCode3")
    private String resCode3;
    @JsonAlias("resDesc3")
    private String resDesc3;
    @JsonAlias("resCode4")
    private String resCode4;
    @JsonAlias("resDesc4")
    private String resDesc4;
    @JsonAlias("messageSection")
    private List<MessageSection> messageSection;
    @JsonAlias("warningListPersonSection")
    private List<WarningListPersonSection> warningListPersonSection;
}
