package com.tmb.oneapp.customerservice.model.rosccustomerprofile.creation;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;;
import com.tmb.oneapp.customerservice.model.rosccustomerprofile.RoscCustomerModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


@Getter
@Setter
@ToString
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RoscCreationRequest extends RoscCustomerModel {
    private String cstaReasonComment;
}
