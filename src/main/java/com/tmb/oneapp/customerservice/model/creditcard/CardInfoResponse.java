package com.tmb.oneapp.customerservice.model.creditcard;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CardInfoResponse {
    @JsonProperty("card_embossing_name1" )
    private String engName;
    @JsonProperty("card_embossing_name2" )
    private String thaiName;
}
