package com.tmb.oneapp.customerservice.model.rosccustomerprofile.creation;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class Body {

    @JacksonXmlProperty(localName = "createKYCPersonalResponse", namespace = "http://data.common.tmb.com/WebCreateKYCPersonal/")
    private CreateKYCPersonalResponse createKYCPersonalResponse;
}
