package com.tmb.oneapp.customerservice.model.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class UpdateEligibleGroupRequest {

    @NotNull
    @Schema(example = "M",
            requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("eligible_group_id")
    private String eligibleGroupId;

    @Schema(example = "ttba")
    @JsonProperty("channel")
    private String channel;

    @JsonProperty("reason")
    private String reason;

    @JsonProperty("rule")
    private String rule;

}
