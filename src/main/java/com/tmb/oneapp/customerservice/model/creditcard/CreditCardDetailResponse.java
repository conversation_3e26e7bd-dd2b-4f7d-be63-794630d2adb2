package com.tmb.oneapp.customerservice.model.creditcard;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreditCardDetailResponse {
    @JsonProperty("card_info" )
    private CardInfoResponse cardInfo;
    @JsonProperty("customer" )
    private CustomerCardDetailResponse customer;
    @JsonProperty("card_phones" )
    private List<CardPhoneCardDetailResponse> cardPhones;
}
