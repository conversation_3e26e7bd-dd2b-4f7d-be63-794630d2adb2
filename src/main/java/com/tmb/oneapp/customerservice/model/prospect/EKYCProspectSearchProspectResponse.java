package com.tmb.oneapp.customerservice.model.prospect;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.tmb.oneapp.customerservice.model.EKYCProspectETEErrorStatus;
import com.tmb.oneapp.customerservice.model.EKYCProspectETEResponseStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EKYCProspectSearchProspectResponse  {
    private List<EKYCProspectETEErrorStatus> errors;
    private EKYCProspectETEResponseStatus status;
}
