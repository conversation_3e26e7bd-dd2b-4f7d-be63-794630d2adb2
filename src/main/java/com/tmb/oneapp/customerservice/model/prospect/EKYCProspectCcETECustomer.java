package com.tmb.oneapp.customerservice.model.prospect;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.tmb.oneapp.customerservice.constant.CustomerServiceConstant;
import com.tmb.oneapp.customerservice.model.EKYCProspectETEConsent;
import com.tmb.oneapp.customerservice.model.EKYCProspectETEIdentification;
import com.tmb.oneapp.customerservice.model.EKYCProspectETEOfficeAddress;
import com.tmb.oneapp.customerservice.model.EKYCProspectETEPrimaryAddress;
import com.tmb.oneapp.customerservice.model.EKYCProspectETERisk;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EKYCProspectCcETECustomer {
    private String prospectId;
    private String channel;
    @JsonProperty("identification")
    private EKYCProspectETEIdentification qaIdentification;
    @JsonProperty("title_thai")
    private String qaTitleThai = CustomerServiceConstant.BLANK;
    @JsonProperty("title_eng")
    private String qaTitleEng = CustomerServiceConstant.BLANK;
    @JsonProperty("firstname_thai")
    private String qaFirstnameThai = CustomerServiceConstant.BLANK;
    @JsonProperty("firstname_eng")
    private String qaFirstnameEng = CustomerServiceConstant.BLANK;
    @JsonProperty("lastname_thai")
    private String qaLastnameThai = CustomerServiceConstant.BLANK;
    @JsonProperty("lastname_eng")
    private String qaLastnameEng = CustomerServiceConstant.BLANK;
    @JsonProperty("birth_date")
    private String qaBirthDate = CustomerServiceConstant.BLANK;
    @JsonProperty("gender")
    private String qaGender = CustomerServiceConstant.BLANK;
    @JsonProperty("marital_status")
    private String qaMaritalStatus = CustomerServiceConstant.BLANK;
    @JsonProperty("nationality")
    private String qaNationality = CustomerServiceConstant.BLANK;
    @JsonProperty("other_nationality")
    private String qaOtherNationality = CustomerServiceConstant.BLANK;
    @JsonProperty("education")
    private String qaEducation = CustomerServiceConstant.BLANK;
    @JsonProperty("occupation")
    private String qaOccupation = CustomerServiceConstant.BLANK;
    @JsonProperty("occupation_specify")
    private String qaOccupationSpecify = CustomerServiceConstant.BLANK;
    @JsonProperty("income")
    private String qaIncome = CustomerServiceConstant.BLANK;
    @JsonProperty("country_for_income")
    private String qaCountryForIncome = CustomerServiceConstant.BLANK;
    @JsonProperty("source_for_income")
    private String qaSourceForIncome = CustomerServiceConstant.BLANK;
    @JsonProperty("mobile_no")
    private String qaMobileNumber = CustomerServiceConstant.BLANK;
    @JsonProperty("phone_number")
    private String qaPhoneNumber = CustomerServiceConstant.BLANK;
    @JsonProperty("phone_extension")
    private String qaPhoneExtension = CustomerServiceConstant.BLANK;
    @JsonProperty("email")
    private String qaEmail = CustomerServiceConstant.BLANK;
    @JsonProperty("purpose_for_saving_flag")
    private String qaPurposeForSavingFlag = CustomerServiceConstant.BLANK;
    @JsonProperty("purpose_for_other")
    private String qaPurposeForOther = CustomerServiceConstant.BLANK;
    @JsonProperty("risk")
    private EKYCProspectETERisk qaRisk;
    @JsonProperty("registered_address")
    private EKYCProspectETEPrimaryAddress qaRegisteredAddress;
    @JsonProperty("primary_address")
    private EKYCProspectETEPrimaryAddress qaPrimaryAddress;
    @JsonProperty("office_address")
    private EKYCProspectETEOfficeAddress qaOfficeAddress;
    @JsonProperty("consent")
    private EKYCProspectETEConsent qaConsent;
    @JsonProperty("business_code")
    private String qaBussinessCode = CustomerServiceConstant.BLANK;
    @JsonProperty("country_of_birth")
    private String qaCountryOfBirth = CustomerServiceConstant.THAILAND_CODE;
    @JsonProperty("not_pass_qa_reason")
    private String qaNotPassQAReason = CustomerServiceConstant.BLANK;
    @JsonProperty("not_pass_qa_update_date")
    private String qaNotPassQAUpdateDate = CustomerServiceConstant.BLANK;
    @JsonProperty("not_pass_qa_empid")
    private String qaNotPassQaEmpId = CustomerServiceConstant.BLANK;
    @JsonProperty("verify_method")
    private String qaVerifyMethod = CustomerServiceConstant.BLANK;
    @JsonProperty("flow_state")
    private String qaFlowState;
    @JsonProperty("validate_date")
    private String qaValidateDate = CustomerServiceConstant.BLANK;
    @JsonProperty("device_id")
    private String qaDeviceId = CustomerServiceConstant.BLANK;
    @JsonProperty("reference_id")
    private String qaReferenceId = CustomerServiceConstant.BLANK;
    @JsonProperty("product_code")
    private String qaProductCode = CustomerServiceConstant.BLANK;
}
