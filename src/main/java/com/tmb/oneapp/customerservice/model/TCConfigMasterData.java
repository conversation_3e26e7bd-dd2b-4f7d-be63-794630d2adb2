package com.tmb.oneapp.customerservice.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "T_CC_CONFIG_MASTER")
public class TCConfigMasterData {

	@Id
	@Column(name = "CRM_ID")
	private String crmId;

	@Column(name = "TC_ACCEPTED_VERSION")
	private String tcAcceptVer;

	@Column(name = "TC_ACCEPTED_DATE")
	private Date tcAcceptedDt;

	@Column(name = "APP_ID")
	private String appId;

}
