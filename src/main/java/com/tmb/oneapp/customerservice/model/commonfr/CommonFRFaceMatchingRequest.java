package com.tmb.oneapp.customerservice.model.commonfr;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonFRFaceMatchingRequest {
    @NotBlank(message = "flow must not be null/blank")
    private String flow;
    @NotNull(message = "attempt must not be null")
    private Integer attempt;
    @NotBlank(message = "selfie_image must not be null/blank")
    private String selfieImage;
    @NotBlank(message = "action_type must not be null/blank")
    private String actionType;
    @NotNull(message = "feature_id must not be null")
    private Integer featureId;
    private String livenessRefId;
    private String imageHash;
    private String customerVersion;
    private String isCheckLiveness;
    private String dopaRefId;
}
