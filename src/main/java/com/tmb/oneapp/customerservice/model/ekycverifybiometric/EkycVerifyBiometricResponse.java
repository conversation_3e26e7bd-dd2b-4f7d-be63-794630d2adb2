package com.tmb.oneapp.customerservice.model.ekycverifybiometric;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EkycVerifyBiometricResponse {
    @NotNull
    @Schema(description = "Biometric Result", example = "{\"code\":\"-1\",\"description\":\"Fail\",\"score\":\"0\",\"idp\":\"0\",\"verification_reference\":\"cd8a7d59-c2c9-4862-8095-58e0b44487b9\"}", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("biometric_result")
    private @Valid EkycVerifyBiometricResponseBiometricResult biometricResult;
}