package com.tmb.oneapp.customerservice.model.personalizedinformation.entity;

import com.tmb.oneapp.customerservice.model.personalizedacctrelation.entity.PersonalizedAcctRelationEntity;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString
@Entity
@Table(schema = "CRMADM", name = "personalized_information")
public class PersonalizedInformation {
    @Id
    @Column(name = "PERSONALIZED_ID", nullable = false)
    private String personalizedId;

    @Column(name = "PERSONALIZED_NAME")
    private String personalizedName;

    @Column(name = "PERSONALIZED_PICTURE_ID")
    private String personalizedPictureId;

    @Column(name = "PERSONALIZED_MAIL_ID")
    private String personalizedMailId;

    @Column(name = "PERSONALIZED_MOBILE_NUMBER")
    private String personalizedMobileNumber;

    @Column(name = "PERSONALIZED_FACEBOOK_ID")
    private String personalizedFacebookId;

    @Column(name = "PERSONALIZED_STATUS")
    private String personalizedStatus;

    @Column(name = "OWN_FLAG")
    private String ownFlag;

    @Column(name = "FAVORITE_FLAG")
    private String favoriteFlag;

    @Column(name = "CRM_ID", nullable = false)
    private String crmId;

    @CreationTimestamp
    @Column(name = "CREATE_DATE")
    @Temporal(TemporalType.DATE)
    private Date createDate;

    @UpdateTimestamp
    @Column(name = "UPDATE_DATE")
    @Temporal(TemporalType.DATE)
    private Date updateDate;

    @Column(name = "APP_ID")
    private String appId;

    @OneToMany(mappedBy = "personalizedInformation", cascade = CascadeType.ALL)
    private List<PersonalizedAcctRelationEntity> personalizedAcctRelationEntities;
}
