package com.tmb.oneapp.customerservice.model.personalizefacerecognition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@IdClass(PersonalizedFaceRecognitionId.class)
@Table(name = "PERSONALIZED_FACE_RECOGNITION", schema = "CUSTCRMUSR")
public class PersonalizedFaceRecognitionEntity implements Serializable {
    @Id
    @Column(name = "CRM_ID")
    private String crmId;

    @Id
    @Column(name = "FEATURE_ID")
    private Integer featureId;

    @Column(name = "FLOW_ID")
    private Integer flowId;

    @Column(name = "ENABLE_STATUS")
    private String enableStatus;

    @Column(name = "CREATED_DATE")
    private Date createdDate;

    @Column(name = "UPDATED_DATE")
    private Date updatedDate;
}
