package com.tmb.oneapp.customerservice.model.favorite;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FavoriteSaveResponse {
  private String message;
  private String favoriteId;
  private String favoriteNumber;
  private String personalizedId;
}
