package com.tmb.oneapp.customerservice.model.profile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "id_issue_country", "card_no_expire", "amlo_refuse_flag", "kyc_review_last_mtn_date",
		"kyc_review_last_mtn_user", "kyc_review_last_mtn_channel", "email_type", "email_address", "email_verify_flag",
		"fatca_flag", "create_date", "create_by", "update_date", "update_by", "customer_stage" })
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AdditionalKyc {

	@JsonProperty("id_issue_country")
	public String idIssueCountry;
	@JsonProperty("card_no_expire")
	public String cardNoExpire;
	@JsonProperty("amlo_refuse_flag")
	public String amloRefuseFlag;
	@JsonProperty("kyc_review_last_mtn_date")
	public String kycReviewLastMtnDate;
	@JsonProperty("kyc_review_last_mtn_user")
	public String kycReviewLastMtnUser;
	@JsonProperty("kyc_review_last_mtn_channel")
	public String kycReviewLastMtnChannel;
	@JsonProperty("email_type")
	public String emailType;
	@JsonProperty("email_address")
	public String emailAddress;
	@JsonProperty("email_verify_flag")
	public String emailVerifyFlag;
	@JsonProperty("fatca_flag")
	public String fatcaFlag;
	@JsonProperty("create_date")
	public String createDate;
	@JsonProperty("create_by")
	public String createBy;
	@JsonProperty("update_date")
	public String updateDate;
	@JsonProperty("update_by")
	public String updateBy;
	@JsonProperty("lyt_tier_id")
	public String lytTierId;
	@JsonProperty("limited_flag")
	public String limitedFlag;
	@JsonProperty("limited_date")
	public  String limitedDate;
	@JsonProperty("ial")
	private int ial;
	@JsonProperty("ekyc_flag")
	private String ekycFlag;
	@JsonProperty("customer_stage")
	private String customerStage;

}