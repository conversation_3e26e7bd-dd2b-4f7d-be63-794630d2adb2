package com.tmb.oneapp.customerservice.model.integrity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReduceDailyLimitConfig {

    @JsonProperty("eb_max_limit_amt_current")
    String ebMaxLimitAmtCurrent;

    @JsonProperty("eb_max_limit_amt_hist")
    String ebMaxLimitAmtHist;

    @JsonProperty("cardless_max_limit_amt")
    String cardLessMaxLimitAmt;

    @JsonProperty("cardless_max_limit_amt_hist")
    String cardLessMaxLimitAmtHist;

    @JsonProperty("billpay_max_limit_amt")
    String billPayMaxLimitAmt;

    @JsonProperty("billpay_max_limit_amt_hist")
    String billPayMaxLimitAmtHist;
}
