package com.tmb.oneapp.customerservice.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ReferenceTopUpResponse {
    private String labelTh;
    private String labelEn;
    private Integer maxLength;
    private Boolean isRequireAddFavorite;
    private Boolean isRequirePay;
    private Boolean isMobile;
    private String regEx;
    private String formatType;
    private Boolean isNumeric;
}
