package com.tmb.oneapp.customerservice.model.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSettingsData {

    @Schema(example = "daily-limit", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("action")
    private String action;

    @JsonProperty("pin_free")
    private PinFree pinfree;

    @JsonProperty("daily_limit")
    private DailyLimits dailylimits;

    @JsonProperty("cardless_limit")
    private CardlessLimitation cardlessLimit;

    @JsonProperty("customer_type")
    private String customerType;

    @JsonProperty("ial_level")
    private Integer ialLevel;

    @JsonProperty("uuid")
    private String uuid;

    @JsonProperty("flow_name")
    private String flowName;

    @JsonProperty("feature_id")
    private Integer featureId;
}
