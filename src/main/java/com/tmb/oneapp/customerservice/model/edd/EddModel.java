package com.tmb.oneapp.customerservice.model.edd;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EddModel {
    @JsonProperty("no_of_withdraw_trns_per_mth")
    private String noOfWithdrawTrnsPerMth;
    @JsonProperty("no_of_deposit_trns_per_mth")
    private String noOfDepositTrnsPerMth;
    @JsonProperty("tot_deposit_amt_per_mth")
    private String totDepositAmtPerMth;
    @JsonProperty("tot_withdraw_amt_per_mth")
    private String totWithdrawAmtPerMth;
    @JsonProperty("src_of_ind_asset")
    private String srcOfIndAsset;
    @JsonProperty("net_asset_amt")
    private String netAssetAmt;
    @JsonProperty("record_date")
    private String recordDate;
    @JsonProperty("update_by")
    private String updateBy;
}
