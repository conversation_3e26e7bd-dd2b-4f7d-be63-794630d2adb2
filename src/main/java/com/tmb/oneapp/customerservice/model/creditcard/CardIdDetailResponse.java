package com.tmb.oneapp.customerservice.model.creditcard;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CardIdDetailResponse {
    @JsonProperty("status" )
    private StatusCardDetailResponse status;
    @JsonProperty("credit_card" )
    private CreditCardDetailResponse creditCard;


}
