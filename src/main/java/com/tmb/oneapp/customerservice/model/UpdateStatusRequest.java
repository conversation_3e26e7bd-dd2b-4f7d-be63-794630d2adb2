package com.tmb.oneapp.customerservice.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateStatusRequest {

    @NotBlank
    @Pattern(message = "CRM ID must be a number", regexp="^[0-9]{30}$")
    private String crmId;

    @NotBlank
    @Pattern(message = "status must be a number", regexp="^[0-9]*$")
    private String status;
}
