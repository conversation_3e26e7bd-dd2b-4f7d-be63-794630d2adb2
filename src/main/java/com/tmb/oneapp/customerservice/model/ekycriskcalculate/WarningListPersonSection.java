package com.tmb.oneapp.customerservice.model.ekycriskcalculate;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class WarningListPersonSection {
    @JsonAlias("fullName")
    private String fullName;
    @JsonAlias("cardId")
    private String cardId;
    @JsonAlias("riskLevel")
    private String riskLevel;
    @JsonAlias("remark")
    private String remark;
}
