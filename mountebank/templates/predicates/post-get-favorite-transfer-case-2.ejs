{"predicates": [{"equals": {"method": "POST", "path": "/apis/customers/favorite/transfer", "headers": {"mock": "true", "device-id": "pb_device_id_99"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "transfer-experience-service", "description": "Success"}, "data": [{"favorite_no": 5, "favorite_nickname": "NinkNamePPKBank", "favorite_acc_no": "*************", "bank_promptpay": "?", "favorite_acc_name": "นาย pp kbank", "from_acc_no": null, "from_acc_name": null, "favorite_amt": null, "added_date": *************, "heart_flag": "Y", "favorite_type": "transfer_promptpay", "image_url": "04", "note": "", "category": "5", "bank_cd": "", "reference_1": "", "reference_2": "", "biller_compcode": ""}, {"favorite_no": 2, "favorite_nickname": "NickNameKBank", "favorite_acc_no": "**********", "bank_promptpay": "?", "favorite_acc_name": "นางสาว เคแบงค์", "from_acc_no": null, "from_acc_name": null, "favorite_amt": null, "added_date": *************, "heart_flag": "Y", "favorite_type": "transfer_other", "image_url": "", "note": "", "category": "2", "bank_cd": "", "reference_1": "", "reference_2": "", "biller_compcode": ""}]}}}]}