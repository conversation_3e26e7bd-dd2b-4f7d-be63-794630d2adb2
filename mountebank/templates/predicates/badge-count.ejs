{"predicates": [{"equals": {"method": "GET", "path": "/apis/customers/inbox/badge"}}, {"or": [{"equals": {"headers": {"Device-Id": "9e2ecd1e9413029586122973d2a6a74122c0c3908df2b51e2bc9a88fc5eed5cb"}}}, {"equals": {"headers": {"Device-Id": "d15079c04732de3d6f233f0959c1718cd59c2cb817862623deee707516576adf"}}}, {"equals": {"headers": {"Device-Id": "44f1de69e211749c2cd6459062ac4adc0f1823c7ff62eb639a6e27e0a82b1ab6"}}}, {"equals": {"headers": {"Device-Id": "48d6973ef9123deb1d4cc57069baa86e6896b7e47117fe0f33a5544c6283186f"}}}]}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "999999", "message": "failed", "service": "customers-service", "description": null}, "data": ""}}}]}