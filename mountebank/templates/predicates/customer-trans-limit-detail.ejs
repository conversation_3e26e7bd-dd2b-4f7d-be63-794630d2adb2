{"predicates": [{"equals": {"method": "POST", "path": "/apis/customers/transaction-limit/detail"}}, {"equals": {"headers": {"Accept-Language": "EN"}}}], "responses": [{"inject": "(config)=>{function getSuccessStatus(){return{'code': '0000', 'message': 'success', 'service': 'customers-service', 'description': null}}; function getBadRequestStatus(){return{'code': '400', 'message': 'BAD REQUEST', 'service': 'customers-service', 'description': 'BAD REQUEST'}}; function getResultY(){return{'current_transaction_daily_limit':{'domestic_transaction_daily_limit': 2000000, 'domestic_available_daily_limit': 2000000, 'domestic_maximum_limit': 2000000, 'international_transaction_daily_limit': 2000000, 'international_available_daily_limit': 2000000, 'international_maximum_limit': 2000000}, 'transaction_daily_limit_request':{'request_id': 'ABCD1233445', 'date_time': '01/01/2021 12:30:00', 'type': 'Domestic', 'request_limit': 3000000, 'status': 'Requested', 'last_updated_user': 'TMB User', 'last_updated_date_time': '01/01/2021 12:30:00'}, 'transaction_daily_limit_history': [{'request_id': 'ABCD1233444', 'date_time': '01/01/2021 12:30:00', 'type': 'Domestic', 'request_limit': 2500000, 'status': 'Rejected', 'reason': 'Rejected Reason', 'last_updated_user': 'TMB User', 'user_role': 'TMB transaction staff', 'last_updated_date_time': '01/01/2021 12:30:00'},{'request_id': 'ABCD1233443', 'date_time': '01/01/2021 12:30:00', 'type': 'International', 'request_limit': 2000000, 'status': 'Approved', 'reason': 'Approved Reason', 'last_updated_user': 'TMB User', 'user_role': 'TMB transaction staff', 'last_updated_date_time': '01/01/2021 12:30:00'}], 'pin_free_transaction_limit':{'pin_free_limit_on': 'Y', 'set_pin_free_count': 5, 'transfer_amount': 50000, 'transfer_last_updated_date_time': '01/01/2021 12:30:00', 'bill_payment_amount': 50000, 'bill_payment_last_updated_date_time': '01/01/2021 12:30:00'}}}; function getResultN(){return{'current_transaction_daily_limit':{'domestic_transaction_daily_limit': 2000000, 'domestic_available_daily_limit': 2000000, 'domestic_maximum_limit': 2000000, 'international_transaction_daily_limit': 2000000, 'international_available_daily_limit': 2000000, 'international_maximum_limit': 2000000}, 'transaction_daily_limit_request': null, 'transaction_daily_limit_history': null, 'pin_free_transaction_limit':{'pin_free_limit_on': 'N', 'set_pin_free_count': 0, 'transfer_amount': 50000, 'transfer_last_updated_date_time': '01/01/2021 12:30:00', 'bill_payment_amount': 50000, 'bill_payment_last_updated_date_time': '01/01/2021 12:30:00'}}}; function getResult(){return{'current_transaction_daily_limit':{'domestic_transaction_daily_limit': 0, 'domestic_available_daily_limit': 200000, 'domestic_maximum_limit': 200000, 'international_transaction_daily_limit': 0, 'international_available_daily_limit': 200000, 'international_maximum_limit': 200000}, 'transaction_daily_limit_request': null, 'transaction_daily_limit_history': [{'request_id': 'ABCD1233444', 'date_time': '01/01/2021 12:30:00', 'type': 'Domestic', 'request_limit': 2500000, 'status': 'Rejected', 'reason': 'Rejected Reason', 'last_updated_user': 'TMB User', 'user_role': 'TMB transaction staff', 'last_updated_date_time': '01/01/2021 12:30:00'}], 'pin_free_transaction_limit':{'pin_free_limit_on': 'Y', 'set_pin_free_count': 5, 'transfer_amount': 50000, 'transfer_last_updated_date_time': '01/01/2021 12:30:00', 'bill_payment_amount': 50000, 'bill_payment_last_updated_date_time': '01/01/2021 12:30:00'}}}; function createRequestY(){return{'crm_id': '001100000000000000000012027065', 'app_id': '01'}}; function createRequestN(){return{'crm_id': '001100000000000000000000200101', 'app_id': '01'}}; function createRequest(){return{'crm_id': '001100000000000000000004314395', 'app_id': '01'}}; const requestParamsObj=JSON.parse(config.request.body); if (requestParamsObj.crm_id=='001100000000000000000012027065' && requestParamsObj.app_id=='01'){return{statusCode: 200, header:{'Content-Type': 'application/json',}, body:{status: getSuccessStatus(), data: getResultY(),}}}else if (requestParamsObj.crm_id=='001100000000000000000004314395' && requestParamsObj.app_id=='01'){return{statusCode: 200, header:{'Content-Type': 'application/json',}, body:{status: getSuccessStatus(), data: getResultN(),}}}else if (requestParamsObj.crm_id=='001100000000000000000018590478' && requestParamsObj.app_id=='01'){return{statusCode: 200, header:{'Content-Type': 'application/json',}, body:{status: getSuccessStatus(), data: getResult(),}}}else{return{statusCode: 400, header:{'Content-Type': 'application/json',}, body:{status: getBadRequestStatus(), data: null,}}}}"}]}