{"predicates": [{"equals": {"method": "GET", "path": "/v1/customers-service/configuration/ekyc", "headers": {"mock": "case-1-success"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "customers-service", "description": null}, "data": {"id": "ekyc_module", "face_detect": {"motionType": ["shake_head", "blink_eye", "open_mouth", "shake_left_head", "shake_right_head"], "actionDelay": "5", "timeout": "60", "instructDelay": "5", "minWidth": "720", "minHeigth": "1280"}, "min_age": "18", "name_title": [{"code": "060103", "descTH": "นาย", "descEN": "MR", "gender": "M"}, {"code": "060104", "descTH": "น.ส.", "descEN": "MISS", "gender": "F"}, {"code": "060105", "descTH": "นาง", "descEN": "MRS", "gender": "F"}], "verify_method": [{"code": "01", "channel": "NDID (National Digital ID)", "channelTH": "NDID (National Digital ID)", "descTH": "ยืนยันตัวตนได้ง่ายๆ ผ่านแอปพลิเคชันธนาคารของคุณ", "descEN": "Easy way to verify yourself with your own banking application", "timeout": "60", "urlProvider": "", "companyCode": "", "images": [], "depositDisplay": "Y", "providersId": null, "maxAal": null, "maxIal": null, "isStandAloneOption": "N"}, {"code": "02", "channel": "7 Eleven", "channelTH": "7 Eleven", "descTH": "ยันยันตัวตนที่ 7/11 สาขาใกล้คุณ", "descEN": "Verify yourself with nearby 7/11 stores", "timeout": "300", "urlProvider": "", "companyCode": "", "images": [], "depositDisplay": "Y", "providersId": null, "maxAal": null, "maxIal": null, "isStandAloneOption": "N"}, {"code": "03", "channel": "AIS Touch Points", "channelTH": "จุดบริการ เอไอเอส", "descTH": "แสดงตัวตนที่จุดบริการ เอไอเอส ใกล้คุณ", "descEN": "Digital Identity verification by presenting your ID card at AIS Touch Points", "timeout": "86400", "urlProvider": "https://www.ais.co.th/servicecenter", "companyCode": "993", "images": ["/images/ekyc/agent/ais/<EMAIL>"], "depositDisplay": "Y", "providersId": "EBCD2294-FA47-46F9-9456-41142F1F85F1", "maxAal": "2.2", "maxIal": "2.3", "isStandAloneOption": "N"}, {"code": "04", "channel": "Confirm ID", "channelTH": "ATM ยืนยันตัวตน", "descTH": "ยืนยันตัวตนที่ตู้เอทีเอ็ม ทีทีบี ใกล้คุณ", "descEN": "Verify yourself with a nearby ttb atm", "timeout": "180", "urlProvider": "https://www-uat.tau2904.com/th/contact/location/atmdc", "companyCode": " ", "images": ["/bank/logo/11.png"], "depositDisplay": "Y", "providersId": null, "maxAal": null, "maxIal": null, "isStandAloneOption": "Y"}], "ndid_request_timeout": "1800", "prospect_expiry_days": "30", "branch_info": {"code": "0001", "desc_en": "001 - PHAHONYOTHIN", "desc_th": "001 - สำนักพหลโยธิน", "branch_code": "001", "bu_code": ""}, "bussiness_code_default": "KA090000", "request_otp_enable_time": "60", "bussiness_code": {"mapping": ["KB090000"], "KB090000": ["110", "102", "226", "109", "103", "104", "105", "106", "219", "221", "223", "107", "108"]}, "objective_of_opening": [{"code": "01", "descTH": "ออมเงิน", "descEN": "Saving"}, {"code": "02", "descTH": "ลงทุนในธุรกิจ", "descEN": "Invesment"}, {"code": "03", "descTH": "ชำระเงินกู้", "descEN": "Loan Payment"}, {"code": "04", "descTH": "บัญชีเงินเดือน", "descEN": "Salary account"}, {"code": "05", "descTH": "อื่นๆ", "descEN": "Others"}], "pin_pattern": "(\\d)\\1{2}|^(\\d){7}|^(\\d){0,5}$|012|123|234|345|456|567|678|789|890|098|987|876|765|654|543|432|321|210", "financail_id": "****************", "open_account_service_request": {"cust_type_id": "P", "customer_type": "101", "customer_status": "AC", "tax_withholding": "1", "open_method": "1", "owner_ship_type": "Individual", "relation_type": "Owner"}, "generate_branch_id": "0919", "cal_risk_config": {"selectorflag": "AAII", "productcode": "MIB", "mitigation": "Y", "csgw": "N"}, "office_code": "**********", "ekyc_check_dopa": "false", "ec_service_flag": "Y"}}}}]}