{"predicates": [{"equals": {"method": "POST", "path": "/apis/customers/v2/ekyc/inquirendidrequest"}}, {"equals": {"headers": {"Content-Type": "application/json"}, "body": {"document_id": "*************"}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json"}, "body": {"status": {"code": "0000", "message": "success", "service": "common-service", "description": "success"}, "data": [{"document_id": "*************", "request_id": "6dd6ea62fee1b01f89b24d1ca5d2616451576857a6a3d0d9f24b1d4924f2eeb7", "reference_id": "7e0192af-0278-4184-bc27-2744921b22a4", "datetime": "2022-03-09T17:19", "status": "error_code:30600", "request_message": "คุณกำลังยืนยันตัวตนและประสงค์ให้ส่งข้อมูลส่วนบุคคลเพื่อใช้ในการเปิดบัญชีธนาคารทีทีบีกรุณายืนยันตัวตนภายใน60นาที(Ref:15323)", "expire_date": "2022-03-09T17:19", "rp_node_id": "0249493E-960B-42C2-8972-************", "provider": "ธนาคารทหารไทยธนชาต", "industry_code": null, "company_code": null, "application_code": "ธนาคารทหารไทยธนชาต", "application_name": "", "description": "", "description_th": "ธนาคารทหารไทยธนชาต", "description_en": "TMBThanachartBank", "min_aal": 2.2, "min_ial": 2.3, "data_request": true, "data_request_list": ["001.cust_info_001"], "role": "IDP", "start_date": null, "end_date": null}, {"document_id": "*************", "request_id": "ce4b928a37ca40d5ae75f6fbca9c3bac2785cac443eaf43dfd555ff39ae1d86d", "reference_id": "ba9d2692-14e1-4787-ae1d-f66dc48dd993", "datetime": "2022-02-27T15:47", "status": "timeout", "request_message": "ท่านได้ทำเรื่องขอเปิดบัญชีธนาคารทหารไทยกรุณายืนยันตัวตนภายใน60นาที(Ref:15441)", "expire_date": "2022-02-27T15:47", "rp_node_id": "0249493E-960B-42C2-8972-************", "provider": "ธนาคารทหารไทยธนชาต", "industry_code": null, "company_code": null, "application_code": "ธนาคารทหารไทยธนชาต", "application_name": "", "description": "", "description_th": "ธนาคารทหารไทยธนชาต", "description_en": "TMBThanachartBank", "min_aal": 2.2, "min_ial": 2.3, "data_request": true, "data_request_list": ["001.bank_statement_001", "001.cust_info_001"], "role": "IDP_AS", "start_date": null, "end_date": null}]}}}]}