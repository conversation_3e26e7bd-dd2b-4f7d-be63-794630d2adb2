{
  "predicates":[
     {
        "equals":{
           "method":"POST",
           "path":"/apis/customers/ekyc/status",
           "body": {
            "citizen_id":"1111111111119",
            "reference_id":"1234"
            }
        }
     }
  ],
  "responses":[
     {
      "is":{
         "statusCode":200,
         "headers":{
            "Content-Type":"application/json"
         },
         "body":{
            "status":{
               "code": "0000",
                 "message": "success",
                 "service": "customers-service",
                 "description": {
                  "en": "sucees",
                  "th": "sucees"
                 }
            },
            "data": null
         }
      }
     }
  ]
},
{
   "predicates":[
      {
         "equals":{
            "method":"POST",
            "path":"/apis/customers/ekyc/status",
            "body": {
             "citizen_id":"1111111111119",
             "reference_id":"0"
             }
         }
      }
   ],
   "responses":[
      {
       "is":{
          "statusCode":400,
          "headers":{
             "Content-Type":"application/json"
          },
          "body":{
             "status":{
                "code": "ekyc_ndid_0001",
                  "message": "Fail",
                  "service": "customers-service",
                  "description": {
                     "en": "Invalid Request",
                     "th": "Invalid Request"
                 }
             },
             "data": null
          }
       }
      }
   ]
 }